#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OptionStrategy2 回测环境验证脚本
模拟真实回测环境验证HULL平滑化和STC绘制修复效果
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
sys.path.append(os.path.join(os.path.dirname(__file__), 'pyStrategy'))

# 导入策略
try:
    from pyStrategy.self_strategy.OptionStrategy2 import OptionStrategy2, HullIndicator, STCIndicator
except ImportError:
    sys.path.append('pyStrategy/self_strategy')
    from OptionStrategy2 import OptionStrategy2, HullIndicator, STCIndicator

class MockKLineData:
    """模拟K线数据"""
    def __init__(self, open_price, high, low, close, volume, timestamp):
        self.open = open_price
        self.high = high
        self.low = low
        self.close = close
        self.volume = volume
        self.current_time = timestamp

class MockTickData:
    """模拟Tick数据"""
    def __init__(self, price):
        self.last_price = price
        self.ask_price1 = price + 0.01
        self.ask_price2 = price + 0.02
        self.bid_price1 = price - 0.01
        self.bid_price2 = price - 0.02

def generate_realistic_market_data(days=30, start_price=100):
    """生成逼真的市场数据"""
    minutes_per_day = 240  # 交易时间4小时
    total_minutes = days * minutes_per_day
    
    data = []
    current_price = start_price
    current_time = datetime.now() - timedelta(days=days)
    
    for i in range(total_minutes):
        # 添加市场特征
        time_factor = np.sin(i * 2 * np.pi / minutes_per_day) * 0.3  # 日内周期
        trend_factor = np.sin(i * 2 * np.pi / (days * minutes_per_day)) * 5  # 长期趋势
        
        # 价格变动
        random_change = np.random.normal(0, 0.1)
        price_change = time_factor + trend_factor/100 + random_change
        
        current_price += price_change
        
        # 生成OHLC数据
        open_price = current_price
        high = current_price + abs(np.random.normal(0, 0.2))
        low = current_price - abs(np.random.normal(0, 0.2))
        close = current_price + np.random.normal(0, 0.05)
        volume = int(np.random.uniform(100, 1000))
        
        # 更新当前价格为收盘价
        current_price = close
        
        kline = MockKLineData(open_price, high, low, close, volume, current_time)
        data.append(kline)
        
        current_time += timedelta(minutes=1)
    
    return data

def test_backtest_hull_smoothing():
    """在回测环境中测试HULL平滑效果"""
    print("🏃‍♂️ 回测环境HULL平滑效果验证")
    print("=" * 70)
    
    # 生成回测数据
    market_data = generate_realistic_market_data(days=5, start_price=100)
    
    # 创建策略实例
    strategy = OptionStrategy2()
    
    # 模拟回测过程
    hull_data = {"raw": [], "smoothed_ema3": [], "smoothed_ema5": []}
    price_data = []
    
    print("📊 模拟回测数据处理（共{}个K线）...".format(len(market_data)))
    
    for i, kline in enumerate(market_data):
        if i < 60:  # 跳过前60个K线以获得足够历史数据
            continue
            
        # 更新价格历史
        strategy.price_history.append(kline.close)
        if len(strategy.price_history) > 100:  # 保持最近100个价格
            strategy.price_history.pop(0)
        
        # 更新模拟参数
        strategy.current_params = strategy.param_sets["A"]
        strategy.tick = MockTickData(kline.close)
        
        # 计算不同平滑方式的HULL值
        hull_raw = strategy.hull_indicators["fast"].calculate(
            strategy.price_history, 21, 1, "EMA"  # 无平滑
        )
        hull_ema3 = strategy.hull_indicators["slow"].calculate(
            strategy.price_history, 21, 3, "EMA"  # EMA平滑3
        )
        hull_ema5 = strategy.hull_indicators["signal"].calculate(
            strategy.price_history, 21, 5, "EMA"  # EMA平滑5
        )
        
        hull_data["raw"].append(hull_raw)
        hull_data["smoothed_ema3"].append(hull_ema3)
        hull_data["smoothed_ema5"].append(hull_ema5)
        price_data.append(kline.close)
        
        # 定期输出进度
        if i % 100 == 0:
            print(f"  处理进度: {i}/{len(market_data)} (HULL值: {hull_ema5:.4f})")
    
    # 分析平滑效果
    print(f"\n📈 回测环境HULL平滑效果分析 (共{len(hull_data['raw'])}个数据点):")
    
    # 计算各种指标的波动性
    def calculate_volatility(values):
        if len(values) < 2:
            return 0
        changes = np.diff(values)
        return np.std(changes)
    
    vol_raw = calculate_volatility(hull_data["raw"])
    vol_ema3 = calculate_volatility(hull_data["smoothed_ema3"])
    vol_ema5 = calculate_volatility(hull_data["smoothed_ema5"])
    
    print(f"  • 原始HULL波动率: {vol_raw:.6f}")
    print(f"  • EMA3平滑波动率: {vol_ema3:.6f} (改进: {(vol_raw-vol_ema3)/vol_raw*100:+.2f}%)")
    print(f"  • EMA5平滑波动率: {vol_ema5:.6f} (改进: {(vol_raw-vol_ema5)/vol_raw*100:+.2f}%)")
    
    # 验证平滑效果
    smoothing_effective = vol_ema5 < vol_raw * 0.8  # 至少20%改进
    
    print(f"\n✅ 回测环境HULL平滑效果: {'有效' if smoothing_effective else '无效'}")
    
    return smoothing_effective

def test_backtest_stc_plotting():
    """在回测环境中测试STC绘制数据"""
    print("\n🏃‍♂️ 回测环境STC绘制数据验证")
    print("=" * 70)
    
    # 生成回测数据
    market_data = generate_realistic_market_data(days=3, start_price=100)
    
    # 创建策略实例
    strategy = OptionStrategy2()
    
    stc_timeline = []
    plotting_data_timeline = []
    
    print("📊 收集STC绘制数据...")
    
    for i, kline in enumerate(market_data):
        if i < 100:  # 跳过前100个K线
            continue
            
        # 模拟回测K线回调
        strategy.price_history.append(kline.close)
        if len(strategy.price_history) > 200:
            strategy.price_history.pop(0)
            
        strategy.current_params = strategy.param_sets["A"]
        strategy.tick = MockTickData(kline.close)
        
        # 计算指标（模拟回测中的指标计算）
        strategy.calc_trend(kline)
        strategy.calc_indicator()
        
        # 收集副图指标数据（这是回测系统会调用的）
        sub_data = strategy.sub_indicator_data
        
        stc_timeline.append({
            "time": kline.current_time,
            "stc": strategy.state_map.stc,
            "stc_signal": strategy.state_map.stc_signal,
            "stc_histogram": strategy.state_map.stc_histogram
        })
        
        plotting_data_timeline.append(sub_data.copy())
        
        # 定期输出状态
        if i % 100 == 0:
            print(f"  时间: {kline.current_time.strftime('%H:%M')} | STC: {sub_data['STC']:.2f}")
    
    print(f"\n📈 STC绘制数据质量检查 (共{len(plotting_data_timeline)}个数据点):")
    
    # 检查数据一致性
    data_consistency_checks = []
    
    for i, data in enumerate(plotting_data_timeline):
        # 检查关键字段存在
        required_fields = ["STC", "STC_SIGNAL", "STC_HISTOGRAM", "STC_OVERBOUGHT", "STC_OVERSOLD"]
        fields_complete = all(field in data for field in required_fields)
        
        # 检查数值范围
        stc_in_range = 0 <= data["STC"] <= 100
        signal_in_range = 0 <= data["STC_SIGNAL"] <= 100
        
        # 检查参考线
        ref_lines_correct = (
            data["STC_OVERBOUGHT"] == 80.0 and
            data["STC_OVERSOLD"] == 20.0
        )
        
        if not (fields_complete and stc_in_range and signal_in_range and ref_lines_correct):
            data_consistency_checks.append(i)
    
    consistency_rate = (len(plotting_data_timeline) - len(data_consistency_checks)) / len(plotting_data_timeline)
    
    print(f"  • 数据一致性: {consistency_rate*100:.2f}% ({len(data_consistency_checks)}个异常点)")
    
    # 检查数据动态性
    stc_values = [data["STC"] for data in plotting_data_timeline]
    stc_variation = np.std(stc_values)
    stc_range = max(stc_values) - min(stc_values)
    
    print(f"  • STC变化范围: {min(stc_values):.2f} - {max(stc_values):.2f}")
    print(f"  • STC标准差: {stc_variation:.4f}")
    print(f"  • 数据动态性: {'正常' if stc_variation > 1 else '过低'}")
    
    # 检查绘制兼容性
    plotting_compatible = (
        consistency_rate > 0.95 and  # 95%以上数据一致
        stc_variation > 1 and        # 有足够变化
        stc_range > 10               # 范围足够大
    )
    
    print(f"\n✅ 回测环境STC绘制兼容性: {'兼容' if plotting_compatible else '不兼容'}")
    
    return plotting_compatible

def test_backtest_performance_impact():
    """测试修复对回测性能的影响"""
    print("\n🏃‍♂️ 回测性能影响评估")
    print("=" * 70)
    
    import time
    
    # 生成较大的回测数据集
    market_data = generate_realistic_market_data(days=10, start_price=100)
    
    strategy = OptionStrategy2()
    
    print(f"📊 性能测试（处理{len(market_data)}个K线）...")
    
    start_time = time.time()
    processed_count = 0
    
    for i, kline in enumerate(market_data):
        if i < 100:
            continue
            
        # 完整的回测逻辑流程
        strategy.price_history.append(kline.close)
        if len(strategy.price_history) > 200:
            strategy.price_history.pop(0)
            
        strategy.current_params = strategy.param_sets["A"]
        strategy.tick = MockTickData(kline.close)
        
        # 计算信号（这是回测中的核心逻辑）
        strategy.calc_signal(kline)
        
        # 获取指标数据（图表更新）
        main_data = strategy.main_indicator_data
        sub_data = strategy.sub_indicator_data
        
        processed_count += 1
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"  • 处理数据点: {processed_count}")
    print(f"  • 总耗时: {total_time:.3f}秒")
    print(f"  • 平均耗时: {total_time/processed_count*1000:.3f}毫秒/K线")
    print(f"  • 处理速度: {processed_count/total_time:.1f}K线/秒")
    
    # 性能基准
    performance_acceptable = total_time / processed_count < 0.01  # 每K线少于10ms
    
    print(f"\n✅ 回测性能: {'可接受' if performance_acceptable else '需优化'}")
    
    return performance_acceptable

if __name__ == "__main__":
    print("🔄 OptionStrategy2回测环境验证")
    print("=" * 70)
    print("模拟真实回测环境，验证HULL+STC修复效果")
    print()
    
    # 执行回测环境验证
    test1 = test_backtest_hull_smoothing()
    test2 = test_backtest_stc_plotting()
    test3 = test_backtest_performance_impact()
    
    # 总结验证结果
    print("\n" + "=" * 70)
    print("📋 回测环境验证总结:")
    print(f"  • HULL平滑化回测效果: {'✅ 正常' if test1 else '❌ 异常'}")
    print(f"  • STC绘制回测兼容性: {'✅ 正常' if test2 else '❌ 异常'}")
    print(f"  • 回测性能影响: {'✅ 可接受' if test3 else '❌ 需优化'}")
    
    all_verified = all([test1, test2, test3])
    
    if all_verified:
        print("\n🎉 回测环境验证完全成功！")
        print("\n✅ 验证结论:")
        print("   • HULL平滑化在回测环境中正常工作")
        print("   • STC指标可在附图指标窗口正确绘制")
        print("   • 修复对回测性能影响可接受")
        print("   • 策略可以正常进行回测")
        print("\n🚀 策略已准备好进行实际回测！")
        
        print("\n📋 建议的回测参数:")
        print("   • 平滑类型: EMA")
        print("   • 平滑周期: 3-5")
        print("   • HULL周期: 快线21，慢线55，信号线13")
        print("   • STC参数: 长度23，快MA50，慢MA100")
    else:
        failed_tests = []
        if not test1: failed_tests.append("HULL平滑效果")
        if not test2: failed_tests.append("STC绘制兼容性")
        if not test3: failed_tests.append("回测性能")
        
        print(f"\n⚠️ 以下方面仍需关注: {', '.join(failed_tests)}")
        print("建议在实际回测前进一步验证")
    
    print("=" * 70) 