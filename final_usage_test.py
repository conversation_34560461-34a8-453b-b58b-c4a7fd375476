#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OptionStrategy2修复后使用验证脚本
演示修复后的HULL+STC策略如何正常使用
"""

import sys
import os
import numpy as np
sys.path.append(os.path.join(os.path.dirname(__file__), 'pyStrategy'))

try:
    from pyStrategy.self_strategy.OptionStrategy2 import OptionStrategy2
except ImportError:
    sys.path.append('pyStrategy/self_strategy')
    from OptionStrategy2 import OptionStrategy2

def demo_strategy_usage():
    """演示策略的基本使用方法"""
    print("🎯 OptionStrategy2使用演示")
    print("=" * 60)
    
    # 1. 创建策略实例
    print("1️⃣ 创建策略实例...")
    strategy = OptionStrategy2()
    
    # 2. 配置策略参数
    print("2️⃣ 配置策略参数...")
    strategy.params_map.smooth_type = "EMA"
    strategy.params_map.smooth_period = 5
    strategy.params_map.hull_fast_period = 21
    strategy.params_map.hull_slow_period = 55
    strategy.params_map.hull_signal_period = 13
    
    # 3. 模拟价格数据输入
    print("3️⃣ 模拟价格数据输入...")
    prices = []
    for i in range(100):
        price = 100 + i * 0.1 + np.sin(i * 0.1) * 2 + np.random.normal(0, 0.5)
        prices.append(price)
        strategy.price_history.append(price)
        
        # 保持合理的历史长度
        if len(strategy.price_history) > 200:
            strategy.price_history.pop(0)
    
    # 4. 设置当前参数
    strategy.current_params = strategy.param_sets["A"]
    
    # 5. 模拟Tick数据
    class MockTick:
        def __init__(self, price):
            self.last_price = price
    
    strategy.tick = MockTick(prices[-1])
    
    # 6. 计算技术指标
    print("4️⃣ 计算技术指标...")
    strategy.calc_indicator()
    
    # 7. 获取主图指标数据
    print("5️⃣ 获取指标数据...")
    main_data = strategy.main_indicator_data
    sub_data = strategy.sub_indicator_data
    
    print("\n📊 主图指标数据 (HULL):")
    for key, value in main_data.items():
        print(f"  • {key}: {value:.4f}")
    
    print("\n📊 副图指标数据 (STC):")
    for key, value in sub_data.items():
        print(f"  • {key}: {value:.4f}")
    
    # 8. 验证数据正确性
    print("\n✅ 数据验证:")
    hull_valid = all(isinstance(v, (int, float)) for v in main_data.values())
    stc_valid = (
        0 <= sub_data["STC"] <= 100 and
        0 <= sub_data["STC_SIGNAL"] <= 100 and
        sub_data["STC_OVERBOUGHT"] == 80.0 and
        sub_data["STC_OVERSOLD"] == 20.0
    )
    
    print(f"  • HULL指标数据: {'✅ 有效' if hull_valid else '❌ 无效'}")
    print(f"  • STC指标数据: {'✅ 有效' if stc_valid else '❌ 无效'}")
    
    return hull_valid and stc_valid

def demo_smoothing_effect():
    """演示平滑效果"""
    print("\n🎯 HULL平滑效果演示")
    print("=" * 60)
    
    strategy = OptionStrategy2()
    
    # 生成噪声数据
    noisy_prices = []
    for i in range(50):
        base = 100 + i * 0.1
        noise = np.random.normal(0, 1.0)
        noisy_prices.append(base + noise)
    
    # 计算无平滑和有平滑的HULL值
    no_smooth_values = []
    smooth_values = []
    
    for i in range(21, len(noisy_prices)):
        price_slice = noisy_prices[:i+1]
        
        # 无平滑
        no_smooth = strategy.hull_indicators["fast"].calculate(
            price_slice, 21, 1, "EMA"
        )
        no_smooth_values.append(no_smooth)
        
        # 有平滑
        smooth = strategy.hull_indicators["slow"].calculate(
            price_slice, 21, 5, "EMA"
        )
        smooth_values.append(smooth)
    
    # 计算平滑效果
    no_smooth_volatility = np.std(np.diff(no_smooth_values))
    smooth_volatility = np.std(np.diff(smooth_values))
    improvement = (no_smooth_volatility - smooth_volatility) / no_smooth_volatility * 100
    
    print(f"📈 平滑效果对比:")
    print(f"  • 无平滑HULL波动率: {no_smooth_volatility:.6f}")
    print(f"  • 平滑HULL波动率: {smooth_volatility:.6f}")
    print(f"  • 改进程度: {improvement:+.2f}%")
    
    effective = improvement > 5
    print(f"\n✅ 平滑效果: {'有效' if effective else '无效'}")
    
    return effective

if __name__ == "__main__":
    print("🔧 OptionStrategy2修复验证")
    print("=" * 60)
    print("验证修复后的策略是否可以正常使用")
    print()
    
    # 执行演示
    usage_ok = demo_strategy_usage()
    smoothing_ok = demo_smoothing_effect()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 使用验证总结:")
    print(f"  • 基本使用功能: {'✅ 正常' if usage_ok else '❌ 异常'}")
    print(f"  • 平滑效果验证: {'✅ 有效' if smoothing_ok else '❌ 无效'}")
    
    all_ok = usage_ok and smoothing_ok
    
    if all_ok:
        print("\n🎉 策略修复完全成功！")
        print("✅ 主要功能:")
        print("   • HULL指标计算正确，平滑效果明显")
        print("   • STC指标数据结构完整，绘制兼容")
        print("   • 所有技术指标数据有效")
        print("   • 策略可以正常使用")
        print("\n🚀 可以开始实际回测了！")
        
        print("\n📋 快速开始指南:")
        print("   1. 导入策略: from OptionStrategy2 import OptionStrategy2")
        print("   2. 创建实例: strategy = OptionStrategy2()")
        print("   3. 设置参数: strategy.params_map.smooth_period = 5")
        print("   4. 输入数据: strategy.price_history.append(price)")
        print("   5. 计算指标: strategy.calc_indicator()")
        print("   6. 获取结果: main_data = strategy.main_indicator_data")
    else:
        print("\n⚠️ 部分功能可能还有问题，请检查具体错误")
    
    print("=" * 60) 