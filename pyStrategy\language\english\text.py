# encoding: UTF-8

SAVE_DATA = 'Save Data'

CONTRACT_SYMBOL = 'Symbol'
CONTRACT_NAME = 'Name'
LAST_PRICE = 'Last'
PRE_CLOSE_PRICE = 'PreClose'
VOLUME = 'Volume'
OPEN_INTEREST = 'Open Interest'
OPEN_PRICE = 'Open'
HIGH_PRICE = 'High'
LOW_PRICE = 'Low'
TIME = 'Time'
GATEWAY = 'Gateway'
CONTENT = 'Content'

ERROR_CODE = u'Error Code'
ERROR_MESSAGE = u'Error Message'

TRADE_ID = u'Fill ID'
ORDER_ID = u'Order ID'
DIRECTION = u'Direction'
OFFSET = u'Offset'
PRICE = u'Price'
TRADE_TIME = u'Fill Time'

ORDER_VOLUME = u'Order Volume'
TRADED_VOLUME = u'Filled Volume'
ORDER_STATUS = u'Order Status'
ORDER_TIME = u'Order Time'
CANCEL_TIME = u'Cancel Time'
FRONT_ID = u'Front ID'
SESSION_ID = u'Session ID'
POSITION = u'Position'
YD_POSITION = u'Yesterday Position'
FROZEN = u'Frozen'
POSITION_PROFIT = u'Position Profit'

ACCOUNT_ID = u'Account ID'
PRE_BALANCE = u'Pre Balance'
BALANCE = u'Balance'
AVAILABLE = u'Available'
COMMISSION = u'Commission'
MARGIN = u'Margin'
CLOSE_PROFIT = u'Close Profit'

TRADING = u'Trading'
PRICE_TYPE = u'Price Type'
EXCHANGE = u'Exchange'
CURRENCY = u'Currency'
PRODUCT_CLASS = u'Product Class'
LAST = u'Last'
SEND_ORDER = u'Send Order'
CANCEL_ALL = u'Cancel All'
VT_SYMBOL = u'Vt System Symbol'
CONTRACT_SIZE = u'Contract Size'
PRICE_TICK = u'Price Tick'
STRIKE_PRICE = u'Strike Price'
UNDERLYING_SYMBOL = u'Underlying Symbol'
OPTION_TYPE = u'Option Type'

REFRESH = u'Refresh'
SEARCH = u'Search'
CONTRACT_SEARCH = u'Contract Search'


BID_1 = u'Bid1'
BID_2 = u'Bid2'
BID_3 = u'Bid3'
BID_4 = u'Bid4'
BID_5 = u'Bid5'
ASK_1 = u'Ask1'
ASK_2 = u'Ask2'
ASK_3 = u'Ask3'
ASK_4 = u'Ask4'
ASK_5 = u'Ask5'

BID_PRICE_1 = u'Bid Price 1'
BID_PRICE_2 = u'Bid Price 2'
BID_PRICE_3 = u'Bid Price 3'
BID_PRICE_4 = u'Bid Price 4'
BID_PRICE_5 = u'Bid Price 5'
ASK_PRICE_1 = u'Ask Price 1'
ASK_PRICE_2 = u'Ask Price 2'
ASK_PRICE_3 = u'Ask Price 3'
ASK_PRICE_4 = u'Ask Price 4'
ASK_PRICE_5 = u'Ask Price 5'

BID_VOLUME_1 = u'Bid Volume 1'
BID_VOLUME_2 = u'Bid Volume 2'
BID_VOLUME_3 = u'Bid Volume 3'
BID_VOLUME_4 = u'Bid Volume 4'
BID_VOLUME_5 = u'Bid Volume 5'
ASK_VOLUME_1 = u'Ask Volume 1'
ASK_VOLUME_2 = u'Ask Volume 2'
ASK_VOLUME_3 = u'Ask Volume 3'
ASK_VOLUME_4 = u'Ask Volume 4'
ASK_VOLUME_5 = u'Ask Volume 5'

MARKET_DATA = u'Market Data'
LOG = u'Log'
ERROR = u'Error'
TRADE = u'Fill'
ORDER = u'Order'
POSITION = u'Position'
ACCOUNT = u'Account'

SYSTEM = u'System'
CONNECT_DATABASE = u'Connect Database'
EXIT = u'Exit'
APPLICATION = u'Application'
DATA_RECORDER = u'Data Recorder'
RISK_MANAGER = u'Risk Manager'

STRATEGY = u'Strategy'
CTA_STRATEGY = u'CTA Strategy'

HELP = u'Help'
RESTORE = u'Restore'
ABOUT = u'About'
TEST = u'Test'
CONNECT = u'Connect '

CPU_MEMORY_INFO = u'CPU Usage：{cpu}%   Memory Usage：{memory}%'
CONFIRM_EXIT = u'Confirm Exit？'

GATEWAY_NOT_EXIST = u"Can't find the gateway：{gateway}"
DATABASE_CONNECTING_COMPLETED = u'MongoDB is connected.'
DATABASE_CONNECTING_FAILED = u'Failed to connect to MongoDB.'
DATA_INSERT_FAILED = u'Data insert failed，please connect MongoDB first.'
DATA_QUERY_FAILED = u'Data query failed, please connect MongoDB first.'
DATA_UPDATE_FAILED = u'Data update failed, please connect MongoDB first.'