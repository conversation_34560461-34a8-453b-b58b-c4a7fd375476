from typing import Literal
import numpy as np
import time
from river import linear_model, preprocessing
from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K 线周期")
    mode: Literal["manual", "auto"] = Field(default="manual", title="工作模式")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    max_positions: int = Field(default=5, title="最大持仓数", ge=1, le=10)
    
    # ATR参数设置
    atr_period: int = Field(default=14, title="ATR周期", ge=7, le=21)
    stop_mult: float = Field(default=2.2, title="止损倍数", ge=1.5, le=2.5)
    profit_mult: float = Field(default=3.0, title="止盈倍数", ge=2.5, le=3.5)
    trail_step: float = Field(default=1.0, title="追踪步长", ge=0.5, le=1.5)
    
    # 手动模式固定止盈止损设置
    fixed_stop_loss: float = Field(default=0, title="固定止损价", ge=0)
    fixed_take_profit: float = Field(default=0, title="固定止盈价", ge=0)
    use_fixed_stops: bool = Field(default=False, title="使用固定止盈止损")


class State(BaseState):
    """状态映射模型"""
    # EMA指标
    ema_fast: float = Field(default=0, title="快线EMA")
    ema_mid: float = Field(default=0, title="中线EMA")
    ema_slow: float = Field(default=0, title="慢线EMA")
    
    # ATR指标
    atr: float = Field(default=0, title="ATR")
    
    # 性能指标
    win_rate: float = Field(default=0, title="胜率")
    profit_factor: float = Field(default=0, title="盈亏比")
    sharpe_ratio: float = Field(default=0, title="夏普比率")
    max_drawdown: float = Field(default=0, title="最大回撤")
    
    # 趋势状态
    trend_type: Literal["A", "B"] = Field(default="A", title="行情模式")
    is_trending: bool = Field(default=False, title="是否趋势")
    trend_strength: float = Field(default=0, title="趋势强度", ge=0, le=1)
    trend_duration: int = Field(default=0, title="趋势持续周期")
    volatility: float = Field(default=0, title="波动率")
    
    # 模式切换状态
    mode_switch_count: int = Field(default=0, title="模式切换计数")
    last_switch_time: float = Field(default=0, title="上次切换时间")
    mode_stability: float = Field(default=0, title="模式稳定性", ge=0, le=1)
    
    # 止损止盈
    stop_loss: float = Field(default=0, title="止损价")
    take_profit: float = Field(default=0, title="止盈价")
    trailing_stop: float = Field(default=0, title="追踪止损价")
    
    # 动态止盈止损
    highest_price: float = Field(default=0, title="最高价")
    lowest_price: float = Field(default=0, title="最低价")
    current_profit: float = Field(default=0, title="当前盈亏")
    max_profit: float = Field(default=0, title="最大盈亏")



class OptionStrategy2(BaseStrategy):
    """三周期EMA+ATR买方策略

    该策略结合了三周期EMA指标和ATR指标，用于判断趋势并生成交易信号。
    """

    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()

        # 初始化交易信号
        self.buy_signal = False
        self.sell_signal = False

        # 初始化行情判断相关变量
        self.trend_period = 15  # 趋势判断周期
        self.trend_count = 0
        self.price_history = []
        self.trend_history = []  # 记录历史趋势状态
        self.volatility_history = []  # 记录历史波动率

        # 初始化趋势判断参数
        self.trend_threshold = 0.6  # 趋势判断阈值
        self.volatility_threshold = 0.5  # 波动率阈值
        self.min_trend_duration = 5  # 最小趋势持续周期
        self.max_trend_duration = 30  # 最大趋势持续周期

        # 模式切换参数
        self.mode_switch_threshold = 5  # 最小切换间隔周期
        self.mode_stability_window = 10  # 模式稳定性判断窗口
        self.param_adjust_rate = 0.2  # 参数调整速率

        # 参数组合
        self.param_sets = {
            "A": {  # 趋势型参数
                "ema": [5, 15, 30],
                "atr_period": 14,
                "stop_mult": 2.2,
                "profit_mult": 3.0,
                "trail_step": 1.0
            },
            "B": {  # 震荡型参数
                "ema": [3, 10, 20],
                "atr_period": 21,
                "stop_mult": 1.8,
                "profit_mult": 2.5,
                "trail_step": 0.5
            }
        }
        # 记录上次撤单时间
        self.last_cancel_time = 0

        self.current_params = None
        self.tick: TickData = None
        self.order_id = None
        self.signal_price = 0

        # 动态止盈止损相关变量
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False

        # 在线学习组件
        self.scaler = preprocessing.StandardScaler()
        self.model = linear_model.PARegressor(
            C=0.01,  # 正则化参数
            mode=2,  # PA-II算法
            eps=0.1  # epsilon不敏感损失
        )

        # 交易性能追踪
        self.trade_history = []
        self.performance_window = 20  # 性能计算窗口

        # 参数学习配置
        self.learn_rate = 0.01
        self.min_samples = 10
        self.update_frequency = 5  # 每5个K线更新一次

        # 参数约束
        self.param_bounds = {
            "stop_mult": (1.5, 2.5),
            "profit_mult": (2.5, 3.5),
            "trail_step": (0.5, 1.5)
        }

        # 心跳检测相关
        self.last_heartbeat_time = time.time()
        self.heartbeat_interval = 10  # 秒
        self.heartbeat_active = True
        import threading
        self.heartbeat_timeout_count = 0
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_task, daemon=True)
        self.heartbeat_thread.start()

    def _heartbeat_task(self):
        """心跳检测线程，定期检测主循环是否卡死，不输出日志，仅内部计数和状态维护"""
        while self.heartbeat_active:
            now = time.time()
            if now - self.last_heartbeat_time > self.heartbeat_interval * 2:
                self.heartbeat_timeout_count += 1
                self.heartbeat_status = False
            else:
                self.heartbeat_status = True
            time.sleep(self.heartbeat_interval)

    def on_tick(self, tick: TickData):
        """Tick数据推送回调

        处理每个Tick数据，更新K线生成器。
        """
        self.last_heartbeat_time = time.time()  # 心跳更新时间
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)

    def on_start(self):
        """策略启动回调

        初始化策略运行所需的变量和状态。
        """
        self.last_heartbeat_time = time.time()  # 心跳更新时间
        self.heartbeat_active = True
        if not self.heartbeat_thread.is_alive():
            import threading
            self.heartbeat_thread = threading.Thread(target=self._heartbeat_task, daemon=True)
            self.heartbeat_thread.start()
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()

        super().on_start()

        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.tick = None
        self.price_history = []
        self.trend_count = 0

        # 初始化动态止盈止损相关变量
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        self.state_map.highest_price = 0
        self.state_map.lowest_price = 0
        self.state_map.current_profit = 0
        self.state_map.max_profit = 0

        # 初始化参数
        if self.params_map.mode == "manual":
            self.current_params = {
                "ema": [5, 15, 30],  # 默认EMA参数
                "atr_period": self.params_map.atr_period,
                "stop_mult": self.params_map.stop_mult,
                "profit_mult": self.params_map.profit_mult,
                "trail_step": self.params_map.trail_step
            }
        else:
            self.current_params = None  # 自动模式等待趋势判断

        self.update_status_bar()

    def on_stop(self):
        """策略停止回调

        清理资源并重置状态。
        """
        self.heartbeat_active = False
        super().on_stop()

    # 清理空转功能代码：移除未被调用的辅助方法和冗余逻辑
    # 例如：on_kline、_update_indicators、_calculate_trend、_calculate_volatility、_assess_mode_stability、_execute_mode_a、_execute_mode_b、_manage_positions、_check_trade_conditions
    # 这些方法未被主流程调用，且与主信号执行无直接关联，已移除。

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标

        返回主图指标数据，包括快线EMA、中线EMA、慢线EMA和ATR。
        """
        return {
            "EMA_FAST": self.state_map.ema_fast,
            "EMA_MID": self.state_map.ema_mid,
            "EMA_SLOW": self.state_map.ema_slow,
            "ATR": self.state_map.atr
        }

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标

        返回副图指标数据，包括止损价、止盈价和追踪止损价。
        """
        return {
            "STOP_LOSS": self.state_map.stop_loss,
            "TAKE_PROFIT": self.state_map.take_profit,
            "TRAILING_STOP": self.state_map.trailing_stop
        }

    def on_tick(self, tick: TickData):
        """Tick数据推送回调

        处理每个Tick数据，更新K线生成器。
        """
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调

        当订单被取消时，重置订单ID。
        """
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """成交推送回调

        当订单成交时，重置订单ID。
        """
        super().on_trade(trade, log)
        self.order_id = None

    def on_start(self):
        """策略启动回调

        初始化策略运行所需的变量和状态。
        """
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()

        super().on_start()

        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.tick = None
        self.price_history = []
        self.trend_count = 0

        # 初始化动态止盈止损相关变量
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        self.state_map.highest_price = 0
        self.state_map.lowest_price = 0
        self.state_map.current_profit = 0
        self.state_map.max_profit = 0

        # 初始化参数
        if self.params_map.mode == "manual":
            self.current_params = {
                "ema": [5, 15, 30],  # 默认EMA参数
                "atr_period": self.params_map.atr_period,
                "stop_mult": self.params_map.stop_mult,
                "profit_mult": self.params_map.profit_mult,
                "trail_step": self.params_map.trail_step
            }
        else:
            self.current_params = None  # 自动模式等待趋势判断

        self.update_status_bar()

    def on_stop(self):
        """策略停止回调

        清理资源并重置状态。
        """
        super().on_stop()

    def callback(self, kline: KLineData) -> None:
        """接受K线回调

        处理K线数据，计算信号并更新图表。
        """
        # 计算指标和信号
        self.calc_signal(kline)

        # 信号执行
        self.exec_signal()

        # 线图更新
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        if self.trading:
            self.update_status_bar()

    def real_time_callback(self, kline: KLineData) -> None:
        """实时K线回调

        使用实时推送的K线数据更新指标和图表。
        """
        self.calc_signal(kline)

        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        self.update_status_bar()

    def calc_trend(self, kline: KLineData) -> None:
        """计算趋势状态"""
        # 更新价格历史
        self.price_history.append(kline.close)
        if len(self.price_history) > self.trend_period:
            self.price_history.pop(0)
            
        if len(self.price_history) == self.trend_period:
            # 1. 计算价格变化率
            price_changes = np.diff(self.price_history)
            
            # 2. 计算方向一致性
            direction_consistency = abs(np.sum(np.sign(price_changes))) / len(price_changes)
            
            # 3. 计算波动率
            volatility = np.std(price_changes)
            self.volatility_history.append(volatility)
            if len(self.volatility_history) > 10:
                self.volatility_history.pop(0)
            
            # 4. 计算趋势强度
            # 使用EMA斜率作为趋势强度指标
            ema_slopes = []
            for period in [5, 10, 20]:
                ema = self.kline_generator.producer.ema(period, array=True)
                if len(ema) >= 2:
                    slope = (ema[-1] - ema[-2]) / ema[-2]
                    ema_slopes.append(slope)
            
            # 计算趋势强度（EMA斜率的一致性）
            if ema_slopes:
                trend_strength = abs(np.mean(ema_slopes))
                self.state_map.trend_strength = min(1.0, trend_strength)
            
            # 5. 计算趋势持续性
            if self.state_map.is_trending:
                self.state_map.trend_duration += 1
                if self.state_map.trend_duration > self.max_trend_duration:
                    self.state_map.trend_duration = 0
                    self.state_map.is_trending = False
            else:
                self.state_map.trend_duration = 0
            
            # 6. 综合判断趋势
            # 使用多个指标综合判断趋势
            is_trending = (
                direction_consistency > self.trend_threshold and  # 方向一致性
                volatility > self.volatility_threshold and  # 波动率
                self.state_map.trend_strength > 0.3 and  # 趋势强度
                (self.state_map.trend_duration >= self.min_trend_duration or  # 趋势持续性
                 direction_consistency > 0.8)  # 强趋势可以降低持续性要求
            )
              # 7. 更新趋势状态与模式切换
            # 计算模式稳定性
            if len(self.trend_history) >= self.mode_stability_window:
                recent_trends = self.trend_history[-self.mode_stability_window:]
                self.state_map.mode_stability = recent_trends.count(is_trending) / self.mode_stability_window
            
            current_time = time.time()
            
            # 判断是否需要切换模式
            if is_trending != self.state_map.is_trending:
                # 检查切换条件：
                # 1. 距离上次切换已经足够长
                # 2. 新模式具有足够的稳定性
                if (current_time - self.state_map.last_switch_time >= self.mode_switch_threshold and 
                    self.state_map.mode_stability >= 0.7):
                    
                    self.state_map.is_trending = is_trending
                    new_trend_type = "A" if is_trending else "B"
                    
                    # 如果确实发生了模式切换
                    if new_trend_type != self.state_map.trend_type:
                        self.state_map.trend_type = new_trend_type
                        self.state_map.last_switch_time = current_time
                        self.state_map.mode_switch_count += 1
                        
                        # 渐进式更新参数
                        target_params = self.param_sets[self.state_map.trend_type]
                        if self.current_params:
                            for key in self.current_params:
                                if key == "ema":
                                    self.current_params[key] = target_params[key]  # EMA周期直接切换
                                else:
                                    # 其他参数渐进调整
                                    current_val = self.current_params[key]
                                    target_val = target_params[key]
                                    adjust = (target_val - current_val) * self.param_adjust_rate
                                    self.current_params[key] = current_val + adjust
                        else:
                            self.current_params = target_params.copy()
            
            # 更新趋势历史
            self.trend_history.append(is_trending)
            if len(self.trend_history) > 20:
                self.trend_history.pop(0)
            
            # 8. 更新状态映射
            self.state_map.volatility = volatility
            
            # 9. 趋势反转预警
            if len(self.trend_history) >= 3:
                recent_trends = self.trend_history[-3:]
                if recent_trends.count(True) == 2 and recent_trends[-1] == False:
                    # 趋势可能反转，调整参数
                    if self.state_map.trend_type == "A":
                        self.current_params["stop_mult"] *= 0.9  # 收紧止损
                        self.current_params["profit_mult"] *= 0.9  # 收紧止盈
                    else:
                        self.current_params["stop_mult"] *= 1.1  # 放宽止损
                        self.current_params["profit_mult"] *= 1.1  # 放宽止盈

    def calc_indicator(self) -> None:
        """计算技术指标"""
        if self.current_params is None:
            self.current_params = self.param_sets["A"]  # 默认使用趋势型参数
            
        # 计算EMA
        ema_fast = self.kline_generator.producer.ema(self.current_params["ema"][0], array=True)
        ema_mid = self.kline_generator.producer.ema(self.current_params["ema"][1], array=True)
        ema_slow = self.kline_generator.producer.ema(self.current_params["ema"][2], array=True)
        
        self.state_map.ema_fast = round(ema_fast[-1], 2)
        self.state_map.ema_mid = round(ema_mid[-1], 2)
        self.state_map.ema_slow = round(ema_slow[-1], 2)
        
        # 计算ATR
        atr, _ = self.kline_generator.producer.atr(self.current_params["atr_period"])
        self.state_map.atr = round(atr, 2)
        
        # 计算止损止盈
        if self.tick:
            current_price = self.tick.last_price
            
            # 根据模式选择止损止盈计算方式
            if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
                # 使用固定止盈止损
                self.state_map.stop_loss = self.params_map.fixed_stop_loss
                self.state_map.take_profit = self.params_map.fixed_take_profit
            else:
                # 使用ATR动态止盈止损
                self.state_map.stop_loss = round(
                    current_price - self.state_map.atr * self.current_params["stop_mult"], 2
                )
                self.state_map.take_profit = round(
                    current_price + self.state_map.atr * self.current_params["profit_mult"], 2
                )
            
            self.state_map.trailing_stop = round(
                current_price - self.state_map.atr * self.current_params["trail_step"], 2
            )

    def calc_signal(self, kline: KLineData):
        """计算交易信号

        优化信号生成逻辑，增加信号确认机制，避免频繁切换。
        """
        # 计算趋势
        self.calc_trend(kline)

        # 计算指标
        self.calc_indicator()

        # 更新动态止盈止损
        if self.tick:
            self.update_dynamic_stops(self.tick.last_price)

        # 信号确认机制：引入信号确认周期，避免频繁切换
        signal_confirmation_period = 3
        recent_trends = self.trend_history[-signal_confirmation_period:]

        # 生成交易信号
        if self.state_map.is_trending:
            # 趋势型交易信号
            self.buy_signal = (
                self.state_map.ema_fast > self.state_map.ema_mid > self.state_map.ema_slow and
                self.state_map.trend_strength > 0.4 and  # 增加趋势强度要求
                recent_trends.count(True) >= signal_confirmation_period - 1  # 确认趋势信号
            )
            self.sell_signal = (
                self.state_map.ema_fast < self.state_map.ema_mid < self.state_map.ema_slow or
                self.state_map.trend_strength < 0.2  # 趋势减弱时考虑平仓
            )
        else:
            # 震荡型交易信号
            self.buy_signal = (
                self.state_map.ema_fast < self.state_map.ema_mid and
                self.state_map.ema_mid > self.state_map.ema_slow and
                self.state_map.volatility < self.volatility_threshold * 1.5  # 控制波动率
            )
            self.sell_signal = (
                self.state_map.ema_fast > self.state_map.ema_mid and
                self.state_map.ema_mid < self.state_map.ema_slow
            )

        # 更新价格
        self.long_price = kline.close
        if self.tick:
            self.long_price = self.tick.ask_price1
            if self.params_map.price_type == "D2":
                self.long_price = self.tick.ask_price2

    def update_dynamic_stops(self, current_price: float) -> None:
        """更新动态止盈止损

        优化动态止盈止损逻辑，增加灵活性。
        """
        if self.position_size > 0:
            # 如果使用固定止盈止损，则不更新动态止损
            if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
                return

            # 更新最高价和最低价
            self.state_map.highest_price = max(self.state_map.highest_price, current_price)
            self.state_map.lowest_price = min(self.state_map.lowest_price, current_price)

            # 计算当前盈亏
            self.state_map.current_profit = (current_price - self.entry_price) * self.position_size

            # 更新最大盈亏
            self.state_map.max_profit = max(self.state_map.max_profit, self.state_map.current_profit)

            # --- 优化递进式追踪止盈止损 ---
            # 1. 盈利达到不同档位时，逐步上移止损
            # 2. 不依赖EMA信号，直接根据浮盈和ATR动态调整
            profit_step1 = self.state_map.atr * 1.0
            profit_step2 = self.state_map.atr * 2.0
            profit_step3 = self.state_map.atr * 3.0

            # 默认初始止损
            stop_loss = self.entry_price - self.state_map.atr * self.current_params["stop_mult"]

            if self.state_map.current_profit > profit_step3:
                # 浮盈超过3倍ATR，止损上移到最高价-1.2ATR
                stop_loss = self.state_map.highest_price - self.state_map.atr * 1.2
            elif self.state_map.current_profit > profit_step2:
                # 浮盈超过2倍ATR，止损上移到最高价-1.5ATR
                stop_loss = self.state_map.highest_price - self.state_map.atr * 1.5
            elif self.state_map.current_profit > profit_step1:
                # 浮盈超过1倍ATR，止损上移到最高价-2ATR
                stop_loss = self.state_map.highest_price - self.state_map.atr * 2.0

            # 若止损价低于成本价，取成本价-1ATR，防止过早止损
            stop_loss = max(stop_loss, self.entry_price - self.state_map.atr)
            self.state_map.stop_loss = round(stop_loss, 2)

    def exec_signal(self):
        """简易交易信号执行"""
        self.signal_price = 0

        position = self.get_position(self.params_map.instrument_id)
        self.position_size = position.net_position

        import time
        if self.order_id is not None:
            # 挂单未成交，撤单并记录撤单时间
            self.cancel_order(self.order_id)
            self.last_cancel_time = time.time()
            # 撤单后直接返回，等待下个tick再判断是否需要重新下单
            return


        # 检查是否达到最大持仓数
        if position.net_position >= self.params_map.max_positions:
            self.buy_signal = False

        # 检查动态止损
        if self.tick and position.net_position > 0:
            if self.tick.last_price <= self.state_map.stop_loss:
                self.sell_signal = True

        # 平仓信号处理，允许10秒内重复发单
        if position.net_position > 0 and self.sell_signal:
            self.signal_price = -self.long_price

            # 若无挂单，或距离上次撤单已超过10秒，则允许再次发单
            now = time.time()
            if (self.order_id is None) and (now - self.last_cancel_time >= 10):
                if self.trading:
                    self.order_id = self.auto_close_position(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        price=self.long_price,
                        volume=position.net_position,
                        order_direction="sell"
                    )
                    # 重置动态止盈止损相关变量
                    self.entry_price = 0
                    self.position_size = 0
                    self.is_trailing = False
                    self.state_map.highest_price = 0
                    self.state_map.lowest_price = 0
                    self.state_map.current_profit = 0
                    self.state_map.max_profit = 0

        # 买开
        if self.buy_signal:
            self.signal_price = self.long_price

            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.long_price,
                    order_direction="buy"
                )
                # 记录入场价格
                self.entry_price = self.long_price
                self.state_map.highest_price = self.long_price
                self.state_map.lowest_price = self.long_price

    def calculate_performance_metrics(self):
        """计算策略性能指标"""
        if len(self.trade_history) < self.min_samples:
            return
            
        recent_trades = self.trade_history[-self.performance_window:]
        profits = [t["profit"] for t in recent_trades]
        
        # 计算胜率
        wins = sum(1 for p in profits if p > 0)
        self.state_map.win_rate = wins / len(profits) if profits else 0
        
        # 计算盈亏比
        avg_win = np.mean([p for p in profits if p > 0]) if any(p > 0 for p in profits) else 0
        avg_loss = abs(np.mean([p for p in profits if p < 0])) if any(p < 0 for p in profits) else 1
        self.state_map.profit_factor = avg_win / avg_loss if avg_loss else 0
        
        # 计算夏普比率
        returns = np.diff(profits)
        self.state_map.sharpe_ratio = (np.mean(returns) / (np.std(returns) + 1e-6)) if len(returns) > 1 else 0
        
        # 计算最大回撤
        cumulative = np.cumsum(profits)
        max_dd = 0
        peak = cumulative[0]
        for value in cumulative[1:]:
            if value > peak:
                peak = value
            dd = (peak - value) / peak if peak > 0 else 0
            max_dd = max(max_dd, dd)
        self.state_map.max_drawdown = max_dd
        
    def update_model(self, kline: KLineData):
        """更新在线学习模型"""
        if len(self.trade_history) < self.min_samples:
            return
            
        # 构造特征
        features = {
            "trend_strength": self.state_map.trend_strength,
            "volatility": self.state_map.volatility,
            "mode_stability": self.state_map.mode_stability,
            "win_rate": self.state_map.win_rate,
            "profit_factor": self.state_map.profit_factor,
            "market_phase": 1 if self.state_map.is_trending else 0
        }
        
        # 标准化特征
        scaled_features = self.scaler.learn_one(features).transform_one(features)
        
        # 计算目标变量（复合性能指标）
        target = (self.state_map.win_rate * 0.3 + 
                 self.state_map.profit_factor * 0.3 +
                 self.state_map.sharpe_ratio * 0.2 -
                 self.state_map.max_drawdown * 0.2)
                 
        # 更新模型
        self.model.learn_one(scaled_features, target)
        
        # 预测最优参数调整
        prediction = self.model.predict_one(scaled_features)
        self._adjust_parameters(prediction)
        
    def _adjust_parameters(self, score: float):
        """根据模型预测调整参数"""
        if not self.current_params:
            return
            
        # 根据性能分数动态调整参数
        adjust_ratio = np.clip(score * self.learn_rate, -0.1, 0.1)
        
        for param, (min_val, max_val) in self.param_bounds.items():
            if param in self.current_params:
                current = self.current_params[param]
                adjusted = current * (1 + adjust_ratio)
                # 确保在参数范围内
                self.current_params[param] = np.clip(adjusted, min_val, max_val)

    def on_kline(self, kline: KLineData):
        """K线更新时的回调函数"""
        # 更新技术指标
        self._update_indicators(kline)
        
        # 检查冷却时间
        current_time = kline.current_time
        if hasattr(self, 'last_cancel_time') and \
           current_time - self.last_cancel_time < self.cooldown_seconds:
            return
            
        # 定期更新性能指标和模型
        if len(self.trade_history) % self.update_frequency == 0:
            self.calculate_performance_metrics()
            self.update_model(kline)
            
        # 趋势判断
        trend_direction = self._calculate_trend()
        
        # 波动率分析
        volatility = self._calculate_volatility()
        
        # 模式稳定性评估
        stability = self._assess_mode_stability()
        
        # 根据趋势方向和稳定性决定交易模式
        if stability >= 0.7:  # 稳定性阈值
            if trend_direction > 0 and volatility < self.volatility_threshold:
                self.state_map.trend_type = "A"
            elif trend_direction < 0 or volatility >= self.volatility_threshold:
                self.state_map.trend_type = "B"
                
        # 执行交易逻辑
        if self.state_map.trend_type == "A":
            self._execute_mode_a(kline)
        else:
            self._execute_mode_b(kline)
            
        # 更新持仓管理
        self._manage_positions(kline)

    def _update_indicators(self, kline: KLineData):
        """更新技术指标"""
        # 计算EMA
        ema_fast = self.kline_generator.producer.ema(self.current_params["ema"][0], array=True)
        ema_mid = self.kline_generator.producer.ema(self.current_params["ema"][1], array=True)
        ema_slow = self.kline_generator.producer.ema(self.current_params["ema"][2], array=True)
        
        self.state_map.ema_fast = round(ema_fast[-1], 2)
        self.state_map.ema_mid = round(ema_mid[-1], 2)
        self.state_map.ema_slow = round(ema_slow[-1], 2)
        
        # 计算ATR
        atr, _ = self.kline_generator.producer.atr(self.current_params["atr_period"])
        self.state_map.atr = round(atr, 2)
        
        # 计算止损止盈
        if self.tick:
            current_price = self.tick.last_price
            
            # 根据模式选择止损止盈计算方式
            if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
                # 使用固定止盈止损
                self.state_map.stop_loss = self.params_map.fixed_stop_loss
                self.state_map.take_profit = self.params_map.fixed_take_profit
            else:
                # 使用ATR动态止盈止损
                self.state_map.stop_loss = round(
                    current_price - self.state_map.atr * self.current_params["stop_mult"], 2
                )
                self.state_map.take_profit = round(
                    current_price + self.state_map.atr * self.current_params["profit_mult"], 2
                )
            
            self.state_map.trailing_stop = round(
                current_price - self.state_map.atr * self.current_params["trail_step"], 2
            )

    def _calculate_trend(self) -> float:
        """计算趋势强度"""
        if not hasattr(self.state_map, 'ema_fast') or not hasattr(self.state_map, 'ema_slow'):
            return 0
            
        trend_strength = (self.state_map.ema_fast - self.state_map.ema_slow) / self.state_map.ema_slow
        self.state_map.trend_strength = trend_strength
        return trend_strength
        
    def _calculate_volatility(self) -> float:
        """计算市场波动性"""
        if not hasattr(self.state_map, 'atr'):
            return 0
            
        volatility = self.state_map.atr / self.state_map.ema_mid
        self.state_map.volatility = volatility
        return volatility
        
    def _assess_mode_stability(self) -> float:
        """评估当前模式的稳定性"""
        if not hasattr(self, 'prev_trends'):
            self.prev_trends = []
            
        current_trend = self._calculate_trend()
        self.prev_trends.append(current_trend)
        
        if len(self.prev_trends) > 10:  # 使用最近10个周期的数据
            self.prev_trends.pop(0)
            
        # 计算趋势变化的标准差，标准差越小表示越稳定
        stability = 1 / (1 + np.std(self.prev_trends))
        self.state_map.mode_stability = stability
        return stability

    def _execute_mode_a(self, kline: KLineData):
        """执行模式A的交易逻辑（趋势交易）"""
        if not self._check_trade_conditions():
            return
            
        # 趋势确认信号
        trend_signal = (self.state_map.ema_fast > self.state_map.ema_mid > self.state_map.ema_slow)
        
        # 入场条件：趋势确认且波动率适中
        if trend_signal and self.state_map.volatility < self.volatility_threshold:
            stop_price = kline.close - self.state_map.atr * self.current_params["stop_mult"]
            target_price = kline.close + self.state_map.atr * self.current_params["profit_mult"]
            
            self._place_order(
                direction="long",
                price=kline.close,
                stop_loss=stop_price,
                take_profit=target_price
            )
            
    def _execute_mode_b(self, kline: KLineData):
        """执行模式B的交易逻辑（高波动或震荡市场）"""
        if not self._check_trade_conditions():
            return
            
        # 回调信号
        pullback = (self.state_map.ema_fast < self.state_map.ema_mid and 
                   self.state_map.volatility > self.volatility_threshold)
        
        if pullback:
            # 使用更保守的止损和获利设置
            stop_price = kline.close - self.state_map.atr * (self.current_params["stop_mult"] * 0.8)
            target_price = kline.close + self.state_map.atr * (self.current_params["profit_mult"] * 0.7)
            
            self._place_order(
                direction="long",
                price=kline.close,
                stop_loss=stop_price,
                take_profit=target_price,
                trailing_step=self.state_map.atr * self.current_params["trail_step"]
            )
            
    def _manage_positions(self, kline: KLineData):
        """管理现有持仓"""
        for position in self.get_positions():
            # 更新跟踪止损
            if position.trailing_stop and position.direction == "long":
                new_stop = kline.close - self.state_map.atr * self.current_params["trail_step"]
                if new_stop > position.stop_loss:
                    self.modify_position(
                        position.symbol,
                        stop_loss=new_stop
                    )
                    
            # 根据市场状况动态调整止盈
            if self.state_map.volatility > self.volatility_threshold * 1.5:
                # 市场波动加剧，收紧止盈
                new_target = (position.entry_price + position.take_profit) / 2
                self.modify_position(
                    position.symbol,
                    take_profit=new_target
                )
                
    def _check_trade_conditions(self) -> bool:
        """检查是否满足交易条件"""
        # 检查是否有足够的样本来评估性能
        if len(self.trade_history) < self.min_samples:
            return False
            
        # 检查性能指标是否满足要求
        if (self.state_map.win_rate < 0.4 or
            self.state_map.profit_factor < 1.2 or
            self.state_map.max_drawdown > 0.2):
            return False
            
        # 检查是否已达到最大持仓限制
        if len(self.get_positions()) >= self.max_positions:
            return False
            
        return True

    def on_order_filled(self, order):
        """订单成交回调"""
        # 记录交易
        trade_record = {
            "entry_time": order.filled_time,
            "entry_price": order.filled_price,
            "direction": order.direction,
            "size": order.filled_size,
            "mode": self.state_map.trend_type,
            "volatility": self.state_map.volatility,
            "trend_strength": self.state_map.trend_strength
        }
        self.trade_history.append(trade_record)
        
    def on_order_closed(self, order):
        """订单平仓回调"""
        if len(self.trade_history) > 0:
            last_trade = self.trade_history[-1]
            # 更新最后一笔交易的信息
            last_trade.update({
                "exit_time": order.filled_time,
                "exit_price": order.filled_price,
                "profit": order.realized_pnl,
                "hold_time": order.filled_time - last_trade["entry_time"]
            })
            
            # 更新冷却时间
            self.last_cancel_time = order.filled_time
            
    def on_backtesting_finished(self):
        """回测结束回调"""
        if not self.trade_history:
            return
            
        # 计算最终的性能统计
        self.calculate_performance_metrics()
        
        # 分析模式性能
        mode_a_trades = [t for t in self.trade_history if t["mode"] == "A"]
        mode_b_trades = [t for t in self.trade_history if t["mode"] == "B"]
        
        # 计算每种模式的胜率和收益
        if mode_a_trades:
            mode_a_wins = sum(1 for t in mode_a_trades if t.get("profit", 0) > 0)
            mode_a_winrate = mode_a_wins / len(mode_a_trades)
            mode_a_profit = sum(t.get("profit", 0) for t in mode_a_trades)
        
        if mode_b_trades:
            mode_b_wins = sum(1 for t in mode_b_trades if t.get("profit", 0) > 0)
            mode_b_winrate = mode_b_wins / len(mode_b_trades)
            mode_b_profit = sum(t.get("profit", 0) for t in mode_b_trades)
            
        # 记录最终的模型参数
        self.final_params = self.current_params.copy()
