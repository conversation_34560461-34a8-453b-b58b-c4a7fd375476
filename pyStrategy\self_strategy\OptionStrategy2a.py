from typing import Dict, List, Optional, Tuple
import numpy as np
import time

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator

# =====================
# 1. 参数与状态定义 - 简化版
# =====================
class Params(BaseParams):
    """策略参数配置 - 简化兼容版本"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K线周期")
    order_volume: int = Field(default=1, title="报单数量")
    max_positions: int = Field(default=3, title="最大持仓数", ge=1, le=5)
    
    # HULL指标参数
    hull_fast_period: int = Field(default=14, title="HULL快线周期", ge=5, le=50)
    hull_slow_period: int = Field(default=34, title="HULL慢线周期", ge=20, le=100)
    
    # STC指标参数
    stc_length: int = Field(default=10, title="STC周期", ge=5, le=30)
    stc_fast_ma: int = Field(default=23, title="STC快速MA", ge=10, le=50)
    stc_slow_ma: int = Field(default=50, title="STC慢速MA", ge=30, le=100)
    
    # 风控参数
    stop_mult: float = Field(default=2.0, title="止损倍数", ge=1.5, le=3.0)
    profit_mult: float = Field(default=2.5, title="止盈倍数", ge=2.0, le=4.0)

class State(BaseState):
    """策略状态 - 简化版"""
    # 技术指标状态
    hull_fast: float = Field(default=0, title="HULL快线")
    hull_slow: float = Field(default=0, title="HULL慢线")
    stc: float = Field(default=50, title="STC值")
    stc_signal: float = Field(default=50, title="STC信号线")
    
    # 风控状态
    stop_loss: float = Field(default=0, title="止损价")
    take_profit: float = Field(default=0, title="止盈价")

# =====================
# 2. 简化技术指标
# =====================
class SimpleHull:
    """简化的HULL指标"""
    
    def __init__(self, fast_period=14, slow_period=34):
        self.fast_period = fast_period
        self.slow_period = slow_period
    
    def calculate(self, prices):
        """计算HULL指标"""
        if len(prices) < self.slow_period:
            current_price = prices[-1] if prices else 0
            return {
                'hull_fast': current_price,
                'hull_slow': current_price
            }
        
        # 简化的加权移动平均
        fast_wma = self._wma(prices, self.fast_period)
        slow_wma = self._wma(prices, self.slow_period)
        
        return {
            'hull_fast': float(fast_wma),
            'hull_slow': float(slow_wma)
        }
    
    def _wma(self, data, period):
        """加权移动平均"""
        if len(data) < period:
            return np.mean(data) if data else 0
            
        weights = np.array([i + 1 for i in range(period)])
        values = np.array(data[-period:])
        return float(np.sum(weights * values) / np.sum(weights))

class SimpleSTC:
    """简化的STC指标"""
    
    def __init__(self, length=10, fast_ma=23, slow_ma=50):
        self.length = length
        self.fast_ma = fast_ma
        self.slow_ma = slow_ma
        self.history = []
    
    def calculate(self, prices):
        """计算STC指标"""
        if len(prices) < self.slow_ma:
            return {
                'stc': 50.0,
                'stc_signal': 50.0
            }
        
        try:
            # 简化的STC计算
            fast_ema = self._ema(prices, self.fast_ma)
            slow_ema = self._ema(prices, self.slow_ma)
            
            # 简单的振荡器
            macd = fast_ema - slow_ema
            self.history.append(macd)
            
            if len(self.history) > 50:
                self.history.pop(0)
            
            if len(self.history) >= self.length:
                # 标准化到0-100范围
                recent_history = self.history[-self.length:]
                highest = max(recent_history)
                lowest = min(recent_history)
                
                if highest != lowest:
                    stc_value = 100 * (macd - lowest) / (highest - lowest)
                else:
                    stc_value = 50.0
                
                # 简单信号线
                signal_line = np.mean(self.history[-5:]) if len(self.history) >= 5 else stc_value
                signal_normalized = 100 * (signal_line - lowest) / (highest - lowest) if highest != lowest else 50.0
            else:
                stc_value = 50.0
                signal_normalized = 50.0
            
            return {
                'stc': float(np.clip(stc_value, 0, 100)),
                'stc_signal': float(np.clip(signal_normalized, 0, 100))
            }
            
        except Exception:
            return {
                'stc': 50.0,
                'stc_signal': 50.0
            }
    
    def _ema(self, data, period):
        """指数移动平均"""
        if len(data) < period:
            return np.mean(data) if data else 0
            
        alpha = 2.0 / (period + 1)
        ema = data[0]
        
        for price in data[1:]:
            ema = alpha * price + (1 - alpha) * ema
            
        return ema

# =====================
# 3. 简化信号生成
# =====================
class SimpleSignalGenerator:
    """简化的信号生成器"""
    
    def __init__(self):
        self.signal_history = []
    
    def generate_signal(self, hull_data, stc_data, current_price):
        """生成交易信号"""
        try:
            # HULL趋势判断
            hull_bullish = hull_data.get('hull_fast', 0) > hull_data.get('hull_slow', 0)
            
            # STC动量判断
            stc_value = stc_data.get('stc', 50)
            stc_signal = stc_data.get('stc_signal', 50)
            stc_bullish = stc_value > stc_signal and stc_value > 30
            stc_bearish = stc_value < stc_signal and stc_value < 70
            
            # 简单的信号逻辑
            buy_signal = hull_bullish and stc_bullish
            sell_signal = not hull_bullish and stc_bearish
            
            # 记录信号历史
            self.signal_history.append({
                'buy': buy_signal,
                'sell': sell_signal,
                'timestamp': time.time()
            })
            
            if len(self.signal_history) > 20:
                self.signal_history.pop(0)
            
            return buy_signal, sell_signal
            
        except Exception:
            return False, False

# =====================
# 4. 主策略类 - 简化版
# =====================
class OptionStrategy2a(BaseStrategy):
    """
    OptionStrategy2a - 简化兼容版本
    
    特点：
    1. 移除复杂的ML功能
    2. 移除多线程处理
    3. 简化技术指标计算
    4. 保留核心交易逻辑
    """
    
    def __init__(self):
        super().__init__()
        
        # 参数和状态
        self.params_map = Params()
        self.state_map = State()
        
        # 简化的技术指标
        self.hull_indicator = SimpleHull()
        self.stc_indicator = SimpleSTC()
        self.signal_generator = SimpleSignalGenerator()
        
        # 运行时状态
        self.tick = None
        self.kline_generator = None
        self.position_size = 0
        self.entry_price = 0
        
        # 数据缓存
        self.price_history = []
        
        # 调试计数器
        self._callback_count = 0
        
        print("[OptionStrategy2a] 简化版本初始化完成")
    
    def on_start(self):
        """策略启动"""
        try:
            # 创建K线生成器
            if self.params_map.exchange and self.params_map.instrument_id:
                self.kline_generator = KLineGenerator(
                    callback=self.callback,
                    real_time_callback=self.real_time_callback,
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    style=self.params_map.kline_style
                )
                self.output("K线生成器创建成功")
            else:
                self.output("交易所或合约代码为空，跳过K线生成器创建")
        except Exception as e:
            self.output("K线生成器创建失败: {}".format(e))
            self.kline_generator = None
        
        # 初始化指标状态
        self._initialize_state()
        
        # 调用基类方法创建技术指标窗口
        super().on_start()
        
        # 推送历史数据
        if self.kline_generator:
            try:
                self.kline_generator.push_history_data()
                self.output("历史数据推送完成")
            except Exception as e:
                self.output("历史数据推送失败: {}".format(e))
        
        self.output("策略启动: {}".format(self.__class__.__name__))
        self.output("交易所: {}".format(self.params_map.exchange))
        self.output("合约: {}".format(self.params_map.instrument_id))
    
    def _initialize_state(self):
        """初始化状态"""
        try:
            self.state_map.hull_fast = 0.0
            self.state_map.hull_slow = 0.0
            self.state_map.stc = 50.0
            self.state_map.stc_signal = 50.0
            self.state_map.stop_loss = 0.0
            self.state_map.take_profit = 0.0
        except Exception as e:
            self.output("状态初始化失败: {}".format(e))
    
    def on_tick(self, tick):
        """Tick数据处理"""
        super().on_tick(tick)
        self.tick = tick
        
        if self.kline_generator:
            try:
                self.kline_generator.tick_to_kline(tick)
            except Exception as e:
                self.output("Tick转K线失败: {}".format(e))
    
    def callback(self, kline):
        """K线回调 - 主要逻辑"""
        self._callback_count += 1
        
        try:
            # 更新价格历史
            self.price_history.append(kline.close)
            if len(self.price_history) > 100:
                self.price_history.pop(0)
            
            # 计算技术指标
            hull_data = self._calculate_hull()
            stc_data = self._calculate_stc()
            
            # 生成交易信号
            buy_signal, sell_signal = self._generate_signals(hull_data, stc_data, kline.close)
            
            # 执行交易逻辑
            self._execute_trading(buy_signal, sell_signal, kline)
            
            # 更新状态
            self._update_state(hull_data, stc_data)
            
            # 更新UI
            self._update_ui(kline, hull_data, stc_data)
            
            if self._callback_count % 10 == 0:
                self.output("策略运行正常，已处理{}个K线".format(self._callback_count))
                
        except Exception as e:
            self.output("callback处理失败: {}".format(e))
    
    def real_time_callback(self, kline):
        """实时K线回调"""
        try:
            # 计算实时指标
            hull_data = self._calculate_hull()
            stc_data = self._calculate_stc()
            
            # 更新实时状态
            self._update_state(hull_data, stc_data)
            
            # 更新UI
            self._update_ui(kline, hull_data, stc_data)
            
        except Exception as e:
            self.output("实时回调处理失败: {}".format(e))
    
    def _calculate_hull(self):
        """计算HULL指标"""
        try:
            self.hull_indicator.fast_period = self.params_map.hull_fast_period
            self.hull_indicator.slow_period = self.params_map.hull_slow_period
            return self.hull_indicator.calculate(self.price_history)
        except Exception:
            current_price = self.price_history[-1] if self.price_history else 0
            return {'hull_fast': current_price, 'hull_slow': current_price}
    
    def _calculate_stc(self):
        """计算STC指标"""
        try:
            self.stc_indicator.length = self.params_map.stc_length
            self.stc_indicator.fast_ma = self.params_map.stc_fast_ma
            self.stc_indicator.slow_ma = self.params_map.stc_slow_ma
            return self.stc_indicator.calculate(self.price_history)
        except Exception:
            return {'stc': 50.0, 'stc_signal': 50.0}
    
    def _generate_signals(self, hull_data, stc_data, current_price):
        """生成交易信号"""
        try:
            return self.signal_generator.generate_signal(hull_data, stc_data, current_price)
        except Exception:
            return False, False
    
    def _execute_trading(self, buy_signal, sell_signal, kline):
        """执行交易逻辑"""
        if not self.tick:
            return
        
        try:
            # 获取当前持仓
            position = self.get_position(self.params_map.instrument_id)
            current_position = position.net_position if position else 0
            
            # 处理开仓信号
            if buy_signal and current_position < self.params_map.max_positions:
                if self.trading:
                    order_id = self.send_order(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        volume=self.params_map.order_volume,
                        price=self.tick.ask_price1,
                        order_direction="buy"
                    )
                    
                    if order_id:
                        self.entry_price = self.tick.ask_price1
                        self.output("买入信号执行，数量: {}".format(self.params_map.order_volume))
            
            # 处理平仓信号
            elif sell_signal and current_position > 0:
                if self.trading:
                    order_id = self.auto_close_position(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        price=self.tick.bid_price1,
                        volume=current_position,
                        order_direction="sell"
                    )
                    
                    if order_id:
                        self.output("卖出信号执行，平仓数量: {}".format(current_position))
            
            # 简单止盈止损
            self._handle_stops(current_position)
            
        except Exception as e:
            self.output("交易逻辑执行失败: {}".format(e))
    
    def _handle_stops(self, position_size):
        """处理止盈止损"""
        if position_size <= 0 or self.entry_price <= 0:
            return
        
        try:
            current_price = self.tick.last_price
            
            # 简单的百分比止损止盈
            stop_distance = self.entry_price * 0.02 * self.params_map.stop_mult
            profit_distance = self.entry_price * 0.02 * self.params_map.profit_mult
            
            stop_loss_price = self.entry_price - stop_distance
            take_profit_price = self.entry_price + profit_distance
            
            # 检查止损
            if current_price <= stop_loss_price:
                if self.trading:
                    self.auto_close_position(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        price=current_price,
                        volume=position_size,
                        order_direction="sell"
                    )
                    self.output("触发止损")
            
            # 检查止盈
            elif current_price >= take_profit_price:
                if self.trading:
                    self.auto_close_position(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        price=current_price,
                        volume=position_size,
                        order_direction="sell"
                    )
                    self.output("触发止盈")
            
            # 更新状态
            self.state_map.stop_loss = stop_loss_price
            self.state_map.take_profit = take_profit_price
            
        except Exception as e:
            self.output("止盈止损处理失败: {}".format(e))
    
    def _update_state(self, hull_data, stc_data):
        """更新策略状态"""
        try:
            self.state_map.hull_fast = hull_data.get('hull_fast', 0)
            self.state_map.hull_slow = hull_data.get('hull_slow', 0)
            self.state_map.stc = stc_data.get('stc', 50)
            self.state_map.stc_signal = stc_data.get('stc_signal', 50)
        except Exception:
            pass
    
    def _update_ui(self, kline, hull_data, stc_data):
        """更新UI界面"""
        try:
            if hasattr(self, 'widget') and self.widget:
                ui_data = {
                    "kline": kline,
                    **self.main_indicator_data,
                    **self.sub_indicator_data
                }
                self.widget.recv_kline(ui_data)
        except Exception:
            pass
    
    @property
    def main_indicator_data(self):
        """主图指标数据"""
        return {
            "HULL_FAST": float(self.state_map.hull_fast),
            "HULL_SLOW": float(self.state_map.hull_slow),
        }
    
    @property
    def sub_indicator_data(self):
        """副图指标数据"""
        return {
            "STC": float(self.state_map.stc),
            "STC_SIGNAL": float(self.state_map.stc_signal),
        }
    
    @property
    def main_indicator(self):
        """主图指标列表"""
        return ["HULL_FAST", "HULL_SLOW"]
    
    @property
    def sub_indicator(self):
        """副图指标列表"""
        return ["STC", "STC_SIGNAL"]