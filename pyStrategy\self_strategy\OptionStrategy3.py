from typing import Literal

import numpy as np
import talib
import math

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator

class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K 线周期")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    max_positions: int = Field(default=3, title="最大持仓数", ge=1, le=10)
    
    # 双EMA参数
    ema_fast: int = Field(default=8, title="快线EMA周期", ge=5, le=20)
    ema_slow: int = Field(default=21, title="慢线EMA周期", ge=15, le=50)
    
    # ATR止损参数
    atr_period: int = Field(default=14, title="ATR周期", ge=7, le=21)
    stop_mult: float = Field(default=2.0, title="止损倍数", ge=1.5, le=3.0)
    profit_mult: float = Field(default=3.0, title="止盈倍数", ge=2.0, le=4.0)
    
    # EMA平滑参数
    ema_smooth_period: int = Field(default=3, title="EMA平滑周期", ge=2, le=7)
    ema_smooth_period2: int = Field(default=2, title="EMA二次平滑周期", ge=2, le=5)

class State(BaseState):
    """状态映射模型"""
    # EMA指标
    ema_fast: float = Field(default=0, title="快线EMA")
    ema_slow: float = Field(default=0, title="慢线EMA")
    
    # ATR指标
    atr: float = Field(default=0, title="ATR")
    
    # 止盈止损
    stop_loss: float = Field(default=0, title="止损价")
    take_profit: float = Field(default=0, title="止盈价")
    
    # 持仓相关
    entry_price: float = Field(default=0, title="入场价格")
    highest_price: float = Field(default=0, title="持仓期间最高价")

class OptionStrategy3(BaseStrategy):
    """简化版双EMA+ATR策略"""
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()
        
        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        
        self.tick: TickData = None
        self.order_id = None
        self.signal_price = 0
        
        # 简化的变量
        self.position_size = 0
        self.is_long = False

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标"""
        return {
            "EMA_FAST": float(self.state_map.ema_fast) if self.state_map.ema_fast is not None else 0.0,
            "EMA_SLOW": float(self.state_map.ema_slow) if self.state_map.ema_slow is not None else 0.0,
            "ATR": float(self.state_map.atr) if self.state_map.atr is not None else 0.0
        }

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标"""
        return {
            "STOP_LOSS": float(self.state_map.stop_loss) if self.state_map.stop_loss is not None else 0.0,
            "TAKE_PROFIT": float(self.state_map.take_profit) if self.state_map.take_profit is not None else 0.0
        }

    @property
    def sub_indicator(self) -> list[str]:
        """副图指标名列表"""
        return []

    def on_tick(self, tick: TickData):
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        super().on_trade(trade, log)
        self.order_id = None

    def on_start(self):
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()

        super().on_start()

        # 初始化变量
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.tick = None
        self.position_size = 0
        self.is_long = False
        
        # 初始化指标状态
        self.state_map.ema_fast = 0.0
        self.state_map.ema_slow = 0.0
        self.state_map.atr = 0.0
        self.state_map.stop_loss = 0.0
        self.state_map.take_profit = 0.0
        self.state_map.entry_price = 0.0
        self.state_map.highest_price = 0.0

        self.update_status_bar()

    def on_stop(self):
        super().on_stop()

    def callback(self, kline: KLineData) -> None:
        """接受 K 线回调"""
        # 计算指标和信号
        self.calc_signal(kline)

        # 信号执行
        self.exec_signal()

        # 线图更新
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        if self.trading:
            self.update_status_bar()

    def real_time_callback(self, kline: KLineData) -> None:
        """使用收到的实时推送 K 线来计算指标并更新线图"""
        self.calc_signal(kline)

        # 线图更新
        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        self.update_status_bar()

    def calc_indicator(self) -> None:
        """计算技术指标 - 只计算双EMA和ATR，并对EMA进行双重平滑处理"""
        try:
            # 计算快慢EMA
            ema_fast = self.kline_generator.producer.ema(self.params_map.ema_fast, array=True)
            ema_slow = self.kline_generator.producer.ema(self.params_map.ema_slow, array=True)
            
            # 确保有足够的数据
            if len(ema_fast) == 0 or len(ema_slow) == 0:
                return
            
            # EMA双重平滑处理 - 社区最佳实践：两次SMA平滑
            def smooth_ema(ema_array, smooth_period):
                """使用SMA对EMA进行平滑处理"""
                if len(ema_array) < smooth_period:
                    return ema_array
                
                # 使用numpy的卷积进行SMA计算，这是最高效的方法
                smoothed = np.convolve(ema_array, np.ones(smooth_period)/smooth_period, mode='valid')
                
                # 为了保持数组长度一致，在前面填充原始值
                if len(smoothed) < len(ema_array):
                    padding_length = len(ema_array) - len(smoothed)
                    padding = ema_array[:padding_length]
                    smoothed = np.concatenate([padding, smoothed])
                
                return smoothed
            
            def double_smooth_ema(ema_array, smooth_period1, smooth_period2):
                """对EMA进行双重平滑处理"""
                # 第一次平滑
                first_smooth = smooth_ema(ema_array, smooth_period1)
                # 第二次平滑
                second_smooth = smooth_ema(first_smooth, smooth_period2)
                return second_smooth
            
            # 应用双重平滑处理
            ema_fast_double_smoothed = double_smooth_ema(
                ema_fast, 
                self.params_map.ema_smooth_period, 
                self.params_map.ema_smooth_period2
            )
            ema_slow_double_smoothed = double_smooth_ema(
                ema_slow, 
                self.params_map.ema_smooth_period, 
                self.params_map.ema_smooth_period2
            )
            
            # 设置双重平滑后的EMA值
            if len(ema_fast_double_smoothed) > 0 and not np.isnan(ema_fast_double_smoothed[-1]):
                self.state_map.ema_fast = round(float(ema_fast_double_smoothed[-1]), 2)
            else:
                self.state_map.ema_fast = 0.0
                
            if len(ema_slow_double_smoothed) > 0 and not np.isnan(ema_slow_double_smoothed[-1]):
                self.state_map.ema_slow = round(float(ema_slow_double_smoothed[-1]), 2)
            else:
                self.state_map.ema_slow = 0.0
            
            # 计算ATR（不需要平滑，ATR本身已经是平滑的）
            atr, _ = self.kline_generator.producer.atr(self.params_map.atr_period)
            self.state_map.atr = round(float(atr), 2) if not np.isnan(atr) else 0.0
            
        except Exception as e:
            # 如果计算过程中出现异常，保持默认值
            if self.state_map.ema_fast == 0:
                self.state_map.ema_fast = 0.0
            if self.state_map.ema_slow == 0:
                self.state_map.ema_slow = 0.0  
            if self.state_map.atr == 0:
                self.state_map.atr = 0.0

    def update_stop_levels(self, current_price: float) -> None:
        """更新止盈止损位 - 基于ATR的追踪止损"""
        if self.is_long and self.state_map.atr > 0:
            # 计算止损止盈位
            stop_distance = self.state_map.atr * self.params_map.stop_mult
            profit_distance = self.state_map.atr * self.params_map.profit_mult
            
            # 更新最高价
            if current_price > self.state_map.highest_price:
                self.state_map.highest_price = current_price
            
            # 计算追踪止损
            new_stop_loss = self.state_map.highest_price - stop_distance
            
            # 止损只能向上调整，不能向下
            if new_stop_loss > self.state_map.stop_loss:
                self.state_map.stop_loss = round(new_stop_loss, 2)
            elif self.state_map.stop_loss == 0:  # 初次设置
                self.state_map.stop_loss = round(self.state_map.entry_price - stop_distance, 2)
            
            # 设置止盈位（固定）
            if self.state_map.take_profit == 0:  # 初次设置
                self.state_map.take_profit = round(self.state_map.entry_price + profit_distance, 2)

    def calc_signal(self, kline: KLineData):
        """计算交易信号 - 简化版双EMA交叉"""
        # 计算指标
        self.calc_indicator()
        
        # 更新止盈止损
        if self.tick and self.is_long:
            self.update_stop_levels(self.tick.last_price)
        
        # 重置信号
        self.buy_signal = False
        self.sell_signal = False
        
        # 确保指标有效
        if self.state_map.ema_fast == 0 or self.state_map.ema_slow == 0:
            return
        
        # 简单的双EMA交叉信号
        # 买入信号：快线上穿慢线
        if self.state_map.ema_fast > self.state_map.ema_slow:
            # 获取前一根K线的EMA值来确认交叉
            try:
                ema_fast_prev = self.kline_generator.producer.ema(self.params_map.ema_fast, array=True)
                ema_slow_prev = self.kline_generator.producer.ema(self.params_map.ema_slow, array=True)
                
                if (len(ema_fast_prev) >= 2 and len(ema_slow_prev) >= 2 and 
                    not self.is_long and 
                    ema_fast_prev[-2] <= ema_slow_prev[-2]):  # 前一根K线快线在慢线下方
                    self.buy_signal = True
            except:
                # 如果无法获取历史数据，则使用简单条件
                if not self.is_long:
                    self.buy_signal = True
        
        # 卖出信号：快线下穿慢线或触及止损止盈
        if self.is_long:
            # EMA交叉卖出信号
            if self.state_map.ema_fast < self.state_map.ema_slow:
                try:
                    ema_fast_prev = self.kline_generator.producer.ema(self.params_map.ema_fast, array=True)
                    ema_slow_prev = self.kline_generator.producer.ema(self.params_map.ema_slow, array=True)
                    
                    if (len(ema_fast_prev) >= 2 and len(ema_slow_prev) >= 2 and 
                        ema_fast_prev[-2] >= ema_slow_prev[-2]):  # 前一根K线快线在慢线上方
                        self.sell_signal = True
                except:
                    self.sell_signal = True
            
            # 止损止盈信号
            if self.tick:
                current_price = self.tick.last_price
                if (current_price <= self.state_map.stop_loss or 
                    current_price >= self.state_map.take_profit):
                    self.sell_signal = True
        
        # 更新价格
        self.long_price = kline.close
        if self.tick:
            self.long_price = self.tick.ask_price1
            if self.params_map.price_type == "D2":
                self.long_price = self.tick.ask_price2

    def exec_signal(self):
        """交易信号执行 - 简化版"""
        self.signal_price = 0
        position = self.get_position(self.params_map.instrument_id)
        self.position_size = position.net_position
        self.is_long = self.position_size > 0
        
        if self.order_id is not None:
            self.cancel_order(self.order_id)
            
        # 检查最大持仓数
        if position.net_position >= self.params_map.max_positions:
            self.buy_signal = False
                
        # 平仓逻辑
        if self.is_long and self.sell_signal:
            self.signal_price = -self.long_price
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
                # 重置持仓相关变量
                self.is_long = False
                self.state_map.entry_price = 0
                self.state_map.highest_price = 0
                self.state_map.stop_loss = 0
                self.state_map.take_profit = 0
                
        # 开仓逻辑
        elif not self.is_long and self.buy_signal:
            self.signal_price = self.long_price
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.long_price,
                    order_direction="buy"
                )
                # 记录入场信息
                self.is_long = True
                self.state_map.entry_price = self.long_price
                self.state_map.highest_price = self.long_price
                self.state_map.stop_loss = 0  # 将在下一次更新时计算
                self.state_map.take_profit = 0  # 将在下一次更新时计算