from typing import Literal
import numpy as np
import time
from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K 线周期")
    mode: Literal["manual", "auto"] = Field(default="manual", title="工作模式")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    max_positions: int = Field(default=5, title="最大持仓数", ge=1, le=10)
    
    # 风险控制参数
    max_loss_per_trade: float = Field(default=1000.0, title="单笔最大亏损", ge=0)
    max_hold_period: int = Field(default=20, title="最大持仓周期", ge=1)
    max_position_value: float = Field(default=100000.0, title="最大持仓价值", ge=0)
    max_total_risk: float = Field(default=50000.0, title="最大总风险", ge=0)
    
    # EMA参数设置
    ema_fast_period: int = Field(default=5, title="快速EMA周期", ge=3, le=10)
    ema_mid_period: int = Field(default=10, title="中速EMA周期", ge=8, le=20)
    ema_slow_period: int = Field(default=20, title="慢速EMA周期", ge=15, le=30)
    
    # RSI参数设置
    rsi_period: int = Field(default=14, title="RSI周期", ge=7, le=21)
    rsi_upper: int = Field(default=65, title="RSI上限", ge=60, le=80)
    rsi_lower: int = Field(default=30, title="RSI下限", ge=20, le=40)
    
    # 成交量参数设置
    volume_ma_period: int = Field(default=20, title="成交量MA周期", ge=10, le=30)
    volume_breakout_mult: float = Field(default=1.5, title="成交量突破倍数", ge=1.2, le=2.0)
    
    # ATR参数设置
    atr_period: int = Field(default=14, title="ATR周期", ge=7, le=21)
    atr_short_period: int = Field(default=5, title="短期ATR周期", ge=3, le=10)
    base_atr: float = Field(default=1.0, title="基础ATR值", ge=0.1, le=10.0)
    stop_mult: float = Field(default=1.2, title="止损倍数", ge=0.8, le=2.0)
    profit_mult: float = Field(default=1.5, title="止盈倍数", ge=1.2, le=2.5)
    trail_step: float = Field(default=0.3, title="追踪步长", ge=0.2, le=1.0)
    vol_threshold: float = Field(default=1.5, title="波动率阈值", ge=1.2, le=2.0)
    
    # 动态止盈参数
    profit_take_ratio: float = Field(default=0.5, title="止盈回撤比例", ge=0.3, le=0.7)
    
    # 手动模式固定止盈止损设置
    fixed_stop_loss: float = Field(default=0, title="固定止损价", ge=0)
    fixed_take_profit: float = Field(default=0, title="固定止盈价", ge=0)
    use_fixed_stops: bool = Field(default=False, title="使用固定止盈止损")
    
    # 趋势判断参数
    trend_period_min: int = Field(default=8, title="最小趋势周期", ge=5, le=15)
    trend_period_max: int = Field(default=20, title="最大趋势周期", ge=15, le=30)
    trend_weight_recent: float = Field(default=0.6, title="近期价格权重", ge=0.5, le=0.8)
    volume_factor_weight: float = Field(default=0.3, title="成交量因子权重", ge=0.2, le=0.5)
    
    # RSI平滑参数
    rsi_smooth_period: int = Field(default=3, title="RSI平滑周期", ge=2, le=5)
    rsi_trend_threshold: float = Field(default=0.3, title="RSI趋势阈值", ge=0.2, le=0.5)
    
    # 波动率过滤参数
    vol_filter_period: int = Field(default=5, title="波动率过滤周期", ge=3, le=10)
    vol_filter_threshold: float = Field(default=1.8, title="波动率过滤阈值", ge=1.5, le=2.5)
    
    # 多周期趋势参数
    trend_short_period: int = Field(default=3, title="短周期", ge=3, le=5)
    trend_mid_period: int = Field(default=5, title="中周期", ge=5, le=8)
    trend_long_period: int = Field(default=10, title="长周期", ge=8, le=15)
    trend_strength_threshold: float = Field(default=0.4, title="趋势强度阈值", ge=0.3, le=0.6)
    trend_duration_min: int = Field(default=2, title="最小趋势持续周期", ge=2, le=4)
    
    # 随机动量指标参数
    stoch_k_period: int = Field(default=9, title="随机指标K周期", ge=5, le=14)
    stoch_d_period: int = Field(default=3, title="随机指标D周期", ge=2, le=5)
    stoch_upper: int = Field(default=80, title="随机指标上限", ge=75, le=85)
    stoch_lower: int = Field(default=20, title="随机指标下限", ge=15, le=25)
    
    # 订单执行参数
    order_timeout: int = Field(default=10, title="订单超时时间(秒)", ge=5, le=30)
    
    # 趋势计算高级参数
    std_filter_period: int = Field(default=20, title="标准差周期", ge=10, le=30)
    std_filter_mult: float = Field(default=1.5, title="标准差倍数", ge=1.0, le=2.0)
    momentum_smooth_period: int = Field(default=5, title="动量平滑周期", ge=3, le=8)
    acceleration_period: int = Field(default=3, title="加速度计算周期", ge=2, le=5)
    
    # 斐波那契参数
    fib_period: int = Field(default=20, title="斐波那契周期", ge=10, le=30)
    fib_deviation: float = Field(default=0.02, title="斐波那契偏差", ge=0.01, le=0.05)
    fib_profit_ratio: float = Field(default=0.618, title="斐波那契止盈比率", ge=0.5, le=0.786)
    
    # 傅里叶变换参数
    fft_window_size: int = Field(default=64, title="FFT窗口大小", ge=32, le=128)
    fft_trend_threshold: float = Field(default=0.6, title="FFT趋势阈值", ge=0.4, le=0.8)
    fft_noise_filter: float = Field(default=0.1, title="FFT噪声过滤阈值", ge=0.05, le=0.2)
    fft_dominant_freq_count: int = Field(default=5, title="主导频率数量", ge=3, le=10)
    fft_smoothing_alpha: float = Field(default=0.3, title="FFT平滑系数", ge=0.1, le=0.5)
    fft_cycle_detection_threshold: float = Field(default=0.7, title="周期检测阈值", ge=0.5, le=0.9)


class State(BaseState):
    """状态映射模型"""
    # EMA指标
    ema_fast: float = Field(default=0, title="快线EMA")
    ema_mid: float = Field(default=0, title="中线EMA")
    ema_slow: float = Field(default=0, title="慢线EMA")
    
    # RSI指标
    rsi: float = Field(default=0, title="RSI值")
    
    # 成交量指标
    volume_ratio: float = Field(default=0, title="量比")
    is_volume_breakout: bool = Field(default=False, title="成交量突破")
    
    # ATR指标
    atr: float = Field(default=0, title="ATR")
    atr_short: float = Field(default=0, title="短期ATR")
    
    # 趋势状态
    trend_type: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="行情模式")
    is_trending: bool = Field(default=False, title="是否趋势")
    trend_strength: float = Field(default=0, title="趋势强度", ge=0, le=1)
    trend_duration: int = Field(default=0, title="趋势持续周期")
    volatility_ratio: float = Field(default=1.0, title="波动率比值")
    
    # 止损止盈
    stop_loss: float = Field(default=0, title="止损价")
    take_profit: float = Field(default=0, title="止盈价")
    trailing_stop: float = Field(default=0, title="追踪止损价")
    
    # 动态止盈止损
    highest_price: float = Field(default=0, title="最高价")
    lowest_price: float = Field(default=0, title="最低价")
    current_profit: float = Field(default=0, title="当前盈亏")
    max_profit: float = Field(default=0, title="最大盈亏")
    
    # 趋势相关状态
    price_momentum: float = Field(default=0, title="价格动量")
    volume_factor: float = Field(default=0, title="成交量因子")
    
    # 波动率相关状态
    volatility_state: Literal["低波动", "中等波动", "高波动"] = Field(default="中等波动", title="波动率状态")
    signal_quality: float = Field(default=0, title="信号质量", ge=0, le=1)
    
    # 多周期趋势状态（实际使用的）
    trend_consensus: bool = Field(default=False, title="趋势共识")
    
    # 随机动量指标状态（实际使用的）
    stoch_k: float = Field(default=50, title="随机指标K值")
    stoch_d: float = Field(default=50, title="随机指标D值")
    
    # 斐波那契状态（实际使用的）
    fib_levels: dict = Field(default_factory=dict, title="斐波那契水平")
    fib_support: float = Field(default=0, title="当前支撑位")
    fib_resistance: float = Field(default=0, title="当前阻力位")
    
    # 傅里叶变换分析状态
    fft_trend_direction: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="FFT趋势方向")
    fft_trend_strength: float = Field(default=0, title="FFT趋势强度", ge=0, le=1)
    fft_dominant_frequency: float = Field(default=0, title="主导频率")
    fft_cycle_period: int = Field(default=0, title="主要周期")
    fft_noise_level: float = Field(default=0, title="噪声水平", ge=0, le=1)
    fft_signal_clarity: float = Field(default=0, title="信号清晰度", ge=0, le=1)
    fft_market_regime: Literal["趋势市", "震荡市", "转换期"] = Field(default="震荡市", title="FFT市场状态")
    fft_phase_angle: float = Field(default=0, title="相位角")
    fft_amplitude_ratio: float = Field(default=0, title="振幅比值")


class OptionStrategy6(BaseStrategy):
    """EMA+RSI+成交量突破买方策略"""
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()
        
        # 新增心跳检测相关
        self.heartbeat_interval = 5  # 心跳间隔(秒)
        self.last_heartbeat = time.time()
        self.heartbeat_timeout = 30  # 心跳超时时间(秒)
        
        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        
        # 指标缓存
        self._indicator_cache = {
            'ema': {'last_update': 0, 'value': None},
            'rsi': {'last_update': 0, 'value': None},
            'atr': {'last_update': 0, 'value': None},
            'volume': {'last_update': 0, 'value': None},
            'momentum': {'last_update': 0, 'value': None},
            'fft': {'last_update': 0, 'value': None}
        }
        
        # 历史数据缓存
        self._price_history = []
        self._volume_history = []
        self._rsi_history = []
        self._max_history_length = max(
            self.params_map.ema_slow_period,
            self.params_map.rsi_period,
            self.params_map.atr_period,
            self.params_map.volume_ma_period,
            self.params_map.fft_window_size
        )
        
        # 傅里叶变换相关
        self._fft_price_buffer = []
        self._fft_smoothed_trend = 0
        self._fft_frequency_weights = {}
        self._fft_phase_history = []
        
        # 趋势判断相关
        self.trend_period = 10
        self.trend_count = 0
        self.trend_threshold = 0.6
        self.volatility_threshold = 0.4
        self.min_trend_duration = 5
        self.max_trend_duration = 30
        
        # 参数组合
        self.param_sets = {
            "上升": {
                "ema": [self.params_map.ema_fast_period, 
                       self.params_map.ema_mid_period, 
                       self.params_map.ema_slow_period],
                "stop_mult": 1.5,
                "profit_mult": 2.0,
                "trail_step": 0.5
            },
            "下降": {
                "ema": [self.params_map.ema_fast_period, 
                       self.params_map.ema_mid_period, 
                       self.params_map.ema_slow_period],
                "stop_mult": 1.2,
                "profit_mult": 1.5,
                "trail_step": 0.3
            },
            "震荡": {
                "ema": [self.params_map.ema_fast_period, 
                       self.params_map.ema_mid_period, 
                       self.params_map.ema_slow_period],
                "stop_mult": 1.0,
                "profit_mult": 1.5,
                "trail_step": 0.3
            }
        }
        
        # 确保current_params在初始化时被正确设置
        self.current_params = self.param_sets["震荡"]
        self.tick: TickData = None
        self.order_id = None
        self.signal_price = 0
        
        # 动态止盈止损相关
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        
        # 订单管理相关
        self.order_time = 0
        
        # 多周期趋势状态的内部实现变量
        self._trend_short = "震荡"
        self._trend_mid = "震荡"
        self._trend_long = "震荡"
        
        # RSI相关内部变量
        self._rsi_momentum = 0.0
        self._rsi_trend = "震荡"
        
        # 随机指标动量内部变量
        self._stoch_momentum = "震荡"
        
        # 趋势检测历史数据
        self.trend_history = {
            "short": [],
            "mid": [],
            "long": []
        }
        self.trend_reversal_count = 0

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标"""
        return {
            "EMA_FAST": self.state_map.ema_fast,
            "EMA_MID": self.state_map.ema_mid,
            "EMA_SLOW": self.state_map.ema_slow,
            "ATR": self.state_map.atr,
            "FFT_TREND_STRENGTH": self.state_map.fft_trend_strength
        }

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标"""
        return {
            "RSI": self.state_map.rsi,
            "VOLUME_RATIO": self.state_map.volume_ratio,
            "STOP_LOSS": self.state_map.stop_loss,
            "TAKE_PROFIT": self.state_map.take_profit,
            "TRAILING_STOP": self.state_map.trailing_stop,
            "FFT_SIGNAL_CLARITY": self.state_map.fft_signal_clarity,
            "FFT_NOISE_LEVEL": self.state_map.fft_noise_level
        }

    def _update_indicator_cache(self, indicator_name: str, value: any) -> None:
        """更新指标缓存"""
        self._indicator_cache[indicator_name] = {
            'last_update': time.time(),
            'value': value
        }

    def _get_cached_indicator(self, indicator_name: str, max_age: float = 1.0) -> any:
        """获取缓存的指标值"""
        cache = self._indicator_cache.get(indicator_name)
        return cache['value'] if cache and time.time() - cache['last_update'] <= max_age else None

    def _calc_volume_ratio(self, period: int) -> float:
        """计算成交量比率"""
        if len(self._volume_history) < period:
            return 1.0
            
        current_volume = self._volume_history[-1]
        avg_volume = np.mean(self._volume_history[-period:])
        return current_volume / avg_volume if avg_volume > 0 else 1.0

    def _update_history_data(self, kline: KLineData) -> None:
        """更新历史数据"""
        self._price_history.append(kline.close)
        self._volume_history.append(kline.volume)
        
        rsi = self.kline_generator.producer.rsi(self.params_map.rsi_period)
        self._rsi_history.append(rsi)
        
        max_length = self._max_history_length
        if len(self._price_history) > max_length:
            self._price_history.pop(0)
            self._volume_history.pop(0)
            self._rsi_history.pop(0)

    def calc_indicator(self) -> None:
        """优化后的指标计算函数"""
        self.on_heartbeat()  # 添加心跳检测
        
        if self.current_params is None:
            self.current_params = self.param_sets["震荡"]
            
        # 使用缓存优化EMA计算
        cached_ema = self._get_cached_indicator('ema')
        if cached_ema is None:
            ema_fast = self.kline_generator.producer.ema(self.current_params["ema"][0])
            ema_mid = self.kline_generator.producer.ema(self.current_params["ema"][1])
            ema_slow = self.kline_generator.producer.ema(self.current_params["ema"][2])
            self._update_indicator_cache('ema', (ema_fast, ema_mid, ema_slow))
        else:
            ema_fast, ema_mid, ema_slow = cached_ema
        
        # 更新EMA状态
        self.state_map.ema_fast = round(ema_fast, 2)
        self.state_map.ema_mid = round(ema_mid, 2)
        self.state_map.ema_slow = round(ema_slow, 2)
        
        # 计算ATR
        atr_long, _ = self.kline_generator.producer.atr(self.params_map.atr_period)
        atr_short, _ = self.kline_generator.producer.atr(self.params_map.atr_short_period)
        
        # 更新ATR状态
        self.state_map.atr = round(atr_long, 2)
        self.state_map.atr_short = round(atr_short, 2)
        self.state_map.volatility_ratio = round(atr_short / atr_long, 2) if atr_long > 0 else 1.0
        
        # 计算RSI
        rsi = self.kline_generator.producer.rsi(self.params_map.rsi_period)
        self.state_map.rsi = round(rsi, 2)
        
        # 更新RSI内部变量
        self._update_rsi_internals(rsi)
        
        # 计算成交量比率
        volume_ratio = self._calc_volume_ratio(self.params_map.volume_ma_period)
        self.state_map.volume_ratio = round(volume_ratio, 2)
        
        # 计算止损止盈
        if self.tick:
            current_price = self.tick.last_price
            
            if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
                self.state_map.stop_loss = self.params_map.fixed_stop_loss
                self.state_map.take_profit = self.params_map.fixed_take_profit
            else:
                vol_adjustment = min(self.state_map.volatility_ratio / self.params_map.vol_threshold, 1.0)
                adjusted_stop_mult = self.current_params["stop_mult"] * vol_adjustment
                
                self.state_map.stop_loss = round(current_price - self.state_map.atr * adjusted_stop_mult, 2)
                self.state_map.take_profit = round(current_price + self.state_map.atr * self.current_params["profit_mult"], 2)
            
            self.state_map.trailing_stop = round(current_price - self.state_map.atr * self.current_params["trail_step"], 2)

    def _update_rsi_internals(self, current_rsi: float) -> None:
        """更新RSI相关内部变量"""
        if len(self._rsi_history) >= 2:
            rsi_change = current_rsi - self._rsi_history[-2]
            self._rsi_momentum = rsi_change
            
            # 判断RSI趋势
            if len(self._rsi_history) >= self.params_map.rsi_smooth_period:
                recent_rsi = self._rsi_history[-self.params_map.rsi_smooth_period:]
                rsi_slope = np.polyfit(range(len(recent_rsi)), recent_rsi, 1)[0]
                
                if rsi_slope > self.params_map.rsi_trend_threshold:
                    self._rsi_trend = "上升"
                elif rsi_slope < -self.params_map.rsi_trend_threshold:
                    self._rsi_trend = "下降"
                else:
                    self._rsi_trend = "震荡"

    def calc_trend(self, kline: KLineData) -> None:
        """优化后的趋势判断函数，集成FFT分析"""
        self.on_heartbeat()  # 添加心跳检测
        self._update_history_data(kline)
        
        # 更新FFT分析
        self._update_fft_analysis(kline)
        
        if len(self._price_history) < self.trend_period:
            return
            
        # 优化趋势判断权重计算
        weights = np.exp(np.linspace(-1, 0, self.trend_period)) * self.params_map.trend_weight_recent
        weights = weights[-len(self._price_history):]  # 适配实际数据长度
        weights = weights / np.sum(weights)
        price_changes = np.diff(self._price_history[-self.trend_period:])
        
        # 计算加权变化
        weighted_changes = price_changes * weights
        direction_consistency = abs(np.sum(np.sign(weighted_changes))) / len(weighted_changes)
        
        # 计算成交量因子
        volume_ma = np.mean(self._volume_history[-self.trend_period:])
        volume_std = np.std(self._volume_history[-self.trend_period:])
        recent_volume = np.mean(self._volume_history[-3:])
        
        volume_factor = (recent_volume - volume_ma) / (volume_std if volume_std > 0 else 1)
        volume_factor = max(-1, min(1, volume_factor))
        self.state_map.volume_factor = volume_factor
        
        # 计算趋势强度
        ema_slopes = []
        for period in [self.params_map.ema_fast_period, self.params_map.ema_mid_period]:
            ema = self.kline_generator.producer.ema(period, array=True)
            if len(ema) >= 2:
                slope = (ema[-1] - ema[-2]) / ema[-2]
                ema_slopes.append(slope)
        
        if ema_slopes:
            base_trend_strength = abs(np.mean(ema_slopes))
            volume_component = abs(volume_factor) * self.params_map.volume_factor_weight
            trend_strength = base_trend_strength * (1 + volume_component)
            self.state_map.trend_strength = min(1.0, trend_strength)
            
            price_momentum = np.sum(weighted_changes)
            self.state_map.price_momentum = price_momentum
        
        # 传统方法判断趋势方向
        traditional_trend = "上升" if np.mean(weighted_changes) > 0 else "下降" if direction_consistency > self.trend_threshold else "震荡"
        
        # 整合FFT分析与传统分析
        final_trend = self._integrate_fft_with_traditional_analysis()
        
        # 使用最终趋势结果
        new_trend = final_trend
        
        # 更新趋势状态
        if new_trend != self.state_map.trend_type:
            self.state_map.trend_type = new_trend
            self.state_map.is_trending = new_trend != "震荡"
            self.state_map.trend_duration = 1 if new_trend != "震荡" else 0
            self.current_params = self.param_sets[new_trend]
        elif self.state_map.is_trending:
            self.state_map.trend_duration += 1
            if self.state_map.trend_duration > self.max_trend_duration:
                self.state_map.trend_duration = 0
                self.state_map.is_trending = False
                self.state_map.trend_type = "震荡"
                self.current_params = self.param_sets["震荡"]
        
        # 更新波动率状态
        vol_filter = self.kline_generator.producer.atr(self.params_map.vol_filter_period)[0]
        self.state_map.volatility_state = (
            "高波动" if vol_filter > self.params_map.vol_filter_threshold * self.state_map.atr else
            "低波动" if vol_filter < self.state_map.atr else "中等波动"
        )

    # 新增市场状态评估字段
    market_state: Literal['趋势', '震荡', '过渡'] = Field(default='震荡', title='市场状态')
    volatility_index: float = Field(default=0, title='波动率指数')
    trend_convergence: bool = Field(default=False, title='多周期趋势收敛')

    # 新增信号质量评估字段
    signal_score: float = Field(default=0, title='信号质量评分', ge=0, le=1)
    signal_consistency: float = Field(default=0, title='信号连续性')
    risk_reward_ratio: float = Field(default=0, title='风险回报比')

    # 新增订单执行优化字段
    slippage_adjustment: float = Field(default=0, title='滑点调整值')
    spread_analysis: float = Field(default=0, title='价差分析')
    liquidity_index: float = Field(default=0, title='流动性指数')

    def _get_response_level(self) -> int:
        """获取响应级别"""
        return (
            3 if self.state_map.trend_strength > 0.7 and self.state_map.volatility_ratio < self.params_map.vol_threshold else
            1 if self.state_map.trend_strength < 0.3 or self.state_map.volatility_ratio > self.params_map.vol_threshold * 1.5 else 2
        )

    def _get_base_signals(self) -> dict:
        """获取基础信号"""
        return {
            "trend": {
                "ema_alignment": self.state_map.ema_fast > self.state_map.ema_mid > self.state_map.ema_slow,
                "trend_strength": self.state_map.trend_strength > 0.4,
                "trend_duration": self.state_map.trend_duration >= self.params_map.trend_duration_min
            },
            "rsi": {
                "level": self.params_map.rsi_lower < self.state_map.rsi < self.params_map.rsi_upper,
                "momentum": self._rsi_momentum > 0,
                "trend": self._rsi_trend == "上升"
            },
            "volume": {
                "ratio": self.state_map.volume_ratio > self.params_map.volume_breakout_mult,
                "factor": self.state_map.volume_factor > 0,
                "breakout": self.state_map.is_volume_breakout
            },
            "momentum": {
                "smoothed": self._calculate_smoothed_momentum() > 0,
                "acceleration": self._calculate_price_acceleration() > 0,
                "stoch": self.state_map.stoch_k > self.state_map.stoch_d
            }
        }

    def _calculate_smoothed_momentum(self) -> float:
        """计算平滑动量"""
        if len(self._price_history) < self.params_map.momentum_smooth_period:
            return 0.0
        
        recent_prices = self._price_history[-self.params_map.momentum_smooth_period:]
        momentum = (recent_prices[-1] - recent_prices[0]) / recent_prices[0] if recent_prices[0] > 0 else 0.0
        return momentum * 100

    def _calculate_price_acceleration(self) -> float:
        """计算价格加速度"""
        if len(self._price_history) < self.params_map.acceleration_period + 1:
            return 0.0
        
        recent_prices = self._price_history[-(self.params_map.acceleration_period + 1):]
        changes = np.diff(recent_prices)
        acceleration = changes[-1] - changes[0] if len(changes) >= 2 else 0.0
        return acceleration

    def _calculate_signal_strength(self, base_signals: dict) -> float:
        """计算信号强度"""
        trend_strength = (
            0.4 * base_signals["trend"]["ema_alignment"] +
            0.3 * base_signals["trend"]["trend_strength"] +
            0.3 * base_signals["trend"]["trend_duration"]
        ) * 0.35
        
        rsi_strength = (
            0.4 * base_signals["rsi"]["level"] +
            0.3 * base_signals["rsi"]["momentum"] +
            0.3 * base_signals["rsi"]["trend"]
        ) * 0.25
        
        volume_strength = (
            0.4 * base_signals["volume"]["ratio"] +
            0.3 * base_signals["volume"]["factor"] +
            0.3 * base_signals["volume"]["breakout"]
        ) * 0.25
        
        momentum_strength = (
            0.4 * base_signals["momentum"]["smoothed"] +
            0.3 * base_signals["momentum"]["acceleration"] +
            0.3 * base_signals["momentum"]["stoch"]
        ) * 0.15
        
        return trend_strength + rsi_strength + volume_strength + momentum_strength

    def _get_market_state(self) -> str:
        """获取市场状态，集成FFT分析"""
        trend_score = self._calculate_trend_score()
        vol_score = self._calculate_volatility_score()
        volume_score = self._calculate_volume_score()
        momentum_score = self._calculate_momentum_score()
        
        # 加入FFT分析权重
        fft_score = (
            self.state_map.fft_trend_strength * 0.4 +
            self.state_map.fft_signal_clarity * 0.3 +
            (1 - self.state_map.fft_noise_level) * 0.3
        )
        
        # 综合评分，FFT占20%权重
        market_score = {
            "volatility": vol_score * 0.2,
            "trend": trend_score * 0.25,
            "volume": volume_score * 0.2,
            "momentum": momentum_score * 0.15,
            "fft": fft_score * 0.2
        }
        
        total_score = sum(market_score.values())
        
        # 基于FFT市场状态进行调整
        if self.state_map.fft_market_regime == "趋势市":
            return "趋势" if total_score > 0.6 else "突破"
        elif self.state_map.fft_market_regime == "震荡市":
            return "震荡" if total_score < 0.4 else "平衡"
        else:  # 转换期
            return "反转" if trend_score < 0.3 and momentum_score < 0.3 else "平衡"

    def _calculate_trend_score(self) -> float:
        """计算趋势得分"""
        ema_score = 1.0 if self.state_map.ema_fast > self.state_map.ema_mid > self.state_map.ema_slow else 0.0
        trend_strength = self.state_map.trend_strength
        trend_duration = min(1.0, self.state_map.trend_duration / 20)
        trend_quality = self._calculate_trend_quality()
        
        return min(1.0, max(0.0, (
            ema_score * 0.3 +
            trend_strength * 0.3 +
            trend_duration * 0.2 +
            trend_quality * 0.2
        )))

    def _calculate_volatility_score(self) -> float:
        """计算波动率得分"""
        atr_volatility = self.state_map.volatility_ratio
        
        if len(self._price_history) >= 20:
            price_std = np.std(self._price_history[-20:])
            price_mean = np.mean(self._price_history[-20:])
            price_volatility = price_std / price_mean if price_mean > 0 else 0
        else:
            price_volatility = 0
        
        if len(self._price_history) >= 5:
            short_std = np.std(self._price_history[-5:])
            short_mean = np.mean(self._price_history[-5:])
            short_volatility = short_std / short_mean if short_mean > 0 else 0
        else:
            short_volatility = 0
        
        return min(1.0, max(0.0, (
            atr_volatility * 0.4 +
            price_volatility * 0.3 +
            short_volatility * 0.3
        )))

    def _calculate_volume_score(self) -> float:
        """计算成交量得分"""
        volume_ratio = self.state_map.volume_ratio
        
        if len(self._volume_history) >= 5:
            volume_trend = np.mean(np.diff(self._volume_history[-5:]))
            volume_trend_score = 1.0 if volume_trend > 0 else 0.0
        else:
            volume_trend_score = 0.0
        
        breakout_score = 1.0 if self.state_map.is_volume_breakout else 0.0
        
        return min(1.0, max(0.0, (
            min(1.0, volume_ratio) * 0.4 +
            volume_trend_score * 0.3 +
            breakout_score * 0.3
        )))

    def _calculate_momentum_score(self) -> float:
        """计算动量得分"""
        rsi_momentum = self._rsi_momentum
        rsi_score = min(1.0, max(0.0, (rsi_momentum + 1) / 2))
        
        stoch_momentum = self.state_map.stoch_k - self.state_map.stoch_d
        stoch_score = min(1.0, max(0.0, (stoch_momentum + 100) / 200))
        
        if len(self._price_history) >= 5:
            price_momentum = np.mean(np.diff(self._price_history[-5:]))
            price_score = min(1.0, max(0.0, (price_momentum + 1) / 2))
        else:
            price_score = 0.0
        
        return min(1.0, max(0.0, (
            rsi_score * 0.4 +
            stoch_score * 0.3 +
            price_score * 0.3
        )))

    def _calculate_trend_quality(self) -> float:
        """计算趋势质量"""
        trend_consistency = self._calculate_trend_consistency()
        trend_stability = self._calculate_trend_stability()
        trend_strength = self.state_map.trend_strength
        
        return min(1.0, max(0.0, (
            trend_consistency * 0.4 +
            trend_stability * 0.3 +
            trend_strength * 0.3
        )))

    def _calculate_trend_consistency(self) -> float:
        """计算趋势一致性"""
        if len(self._price_history) < 10:
            return 0.0
        
        price_changes = np.diff(self._price_history[-10:])
        direction_changes = np.diff(np.sign(price_changes))
        
        return 1.0 - (np.sum(np.abs(direction_changes)) / len(direction_changes))
        
    def _analyze_trend_convergence(self, fast_period: int, mid_period: int, slow_period: int) -> float:
        """分析多周期趋势收敛度
        参数:
            fast_period: 快速EMA周期
            mid_period: 中速EMA周期
            slow_period: 慢速EMA周期
        返回:
            趋势收敛度(0-1)
        """
        # 计算各周期EMA斜率
        ema_fast = self.kline_generator.producer.ema(fast_period, array=True)
        ema_mid = self.kline_generator.producer.ema(mid_period, array=True)
        ema_slow = self.kline_generator.producer.ema(slow_period, array=True)
        
        # 计算斜率方向一致性
        slope_fast = ema_fast[-1] - ema_fast[-2]
        slope_mid = ema_mid[-1] - ema_mid[-2]
        slope_slow = ema_slow[-1] - ema_slow[-2]
        
        # 计算收敛度
        direction_score = 1.0 if (slope_fast * slope_mid > 0) and (slope_mid * slope_slow > 0) else 0.0
        angle_score = min(1.0, abs(slope_fast) / (abs(slope_slow) + 1e-5))
        
        return 0.7 * direction_score + 0.3 * angle_score

    def _calculate_trend_stability(self) -> float:
        """计算趋势稳定性"""
        if len(self._price_history) < 20:
            return 0.0
        
        price_std = np.std(self._price_history[-20:])
        price_mean = np.mean(self._price_history[-20:])
        
        return 1.0 - (price_std / price_mean if price_mean > 0 else 0)

    def _confirm_signal(self, response_level: int, base_signals: dict, signal_strength: float, market_state: str) -> bool:
        """确认信号"""
        if response_level == 3:  # 激进
            confirm_count = sum([
                sum(base_signals["trend"].values()) >= 2,
                sum(base_signals["rsi"].values()) >= 2,
                sum(base_signals["volume"].values()) >= 2,
                sum(base_signals["momentum"].values()) >= 2
            ])
            return confirm_count >= 2 and signal_strength > 0.5
        
        elif response_level == 2:  # 适中
            if signal_strength < 0.6:
                return False
                
            confirm_count = sum([
                sum(base_signals["trend"].values()) >= 2,
                sum(base_signals["rsi"].values()) >= 2,
                sum(base_signals["volume"].values()) >= 2
            ])
            return confirm_count >= 2
        
        else:  # 保守
            return signal_strength > 0.7 and all(base_signals["trend"].values()) and all(base_signals["rsi"].values()) and all(base_signals["volume"].values())

    def on_tick(self, tick: TickData):
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """成交回报处理"""
        super().on_trade(trade, log)
        self.order_id = None
        
        if trade.direction == "buy":
            self.entry_price = trade.price
            self.state_map.highest_price = trade.price
            self.state_map.lowest_price = trade.price
        
        position = self.get_position(self.params_map.instrument_id)
        self.position_size = position.net_position

    def on_start(self):
        """策略启动"""
        self._reset_state()
        
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        
        self.kline_generator.push_history_data()
        super().on_start()

        self.current_params = (
            {
                "ema": [self.params_map.ema_fast_period,
                       self.params_map.ema_mid_period,
                       self.params_map.ema_slow_period],
                "stop_mult": self.params_map.stop_mult,
                "profit_mult": self.params_map.profit_mult,
                "trail_step": self.params_map.trail_step
            } if self.params_map.mode == "manual" else self.param_sets["震荡"]
        )

        self._max_history_length = max(
            self.params_map.ema_slow_period,
            self.params_map.rsi_period,
            self.params_map.atr_period,
            self.params_map.volume_ma_period,
            self.params_map.fft_window_size
        ) * 2

        self.update_status_bar()

    def on_stop(self):
        super().on_stop()
        
    def on_heartbeat(self) -> None:
        """心跳检测函数"""
        current_time = time.time()
        if current_time - self.last_heartbeat > self.heartbeat_timeout:
            self.logger.warning("心跳检测超时，重新初始化策略")
            self.__init__()
        self.last_heartbeat = current_time
        
    def run(self) -> None:
        """主运行循环"""
        while True:
            try:
                self.on_heartbeat()
                self.process_market_data()
                time.sleep(0.1)  # 防止CPU占用过高
            except Exception as e:
                self.logger.error(f"策略运行异常: {str(e)}")
                time.sleep(5)  # 异常后等待5秒再重试

    def callback(self, kline: KLineData) -> None:
        """接受 K 线回调"""
        self._update_history_data(kline)
        self.calc_signal(kline)
        self.exec_signal()

        # 计算主图指标
        ema_fast = self.kline_generator.producer.ema(self.current_params["ema"][0])
        ema_mid = self.kline_generator.producer.ema(self.current_params["ema"][1])
        ema_slow = self.kline_generator.producer.ema(self.current_params["ema"][2])
        atr_long, _ = self.kline_generator.producer.atr(self.params_map.atr_period)

        # 更新主图指标状态
        self.state_map.ema_fast = round(ema_fast, 2)
        self.state_map.ema_mid = round(ema_mid, 2)
        self.state_map.ema_slow = round(ema_slow, 2)
        self.state_map.atr = round(atr_long, 2)

        # 计算副图指标
        rsi = self.kline_generator.producer.rsi(self.params_map.rsi_period)
        volume_ratio = self._calc_volume_ratio(self.params_map.volume_ma_period)

        # 更新副图指标状态
        self.state_map.rsi = round(rsi, 2)
        self.state_map.volume_ratio = round(volume_ratio, 2)

        # 更新图表
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        if self.trading:
            self.update_status_bar()

    def real_time_callback(self, kline: KLineData) -> None:
        """使用收到的实时推送 K 线来计算指标并更新线图"""
        self._update_history_data(kline)
        self.calc_signal(kline)

        # 计算主图指标
        ema_fast = self.kline_generator.producer.ema(self.current_params["ema"][0])
        ema_mid = self.kline_generator.producer.ema(self.current_params["ema"][1])
        ema_slow = self.kline_generator.producer.ema(self.current_params["ema"][2])
        atr_long, _ = self.kline_generator.producer.atr(self.params_map.atr_period)

        # 更新主图指标状态
        self.state_map.ema_fast = round(ema_fast, 2)
        self.state_map.ema_mid = round(ema_mid, 2)
        self.state_map.ema_slow = round(ema_slow, 2)
        self.state_map.atr = round(atr_long, 2)

        # 计算副图指标
        rsi = self.kline_generator.producer.rsi(self.params_map.rsi_period)
        volume_ratio = self._calc_volume_ratio(self.params_map.volume_ma_period)

        # 更新副图指标状态
        self.state_map.rsi = round(rsi, 2)
        self.state_map.volume_ratio = round(volume_ratio, 2)

        # 更新图表
        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        self.update_status_bar()

    def update_dynamic_stops(self, current_price: float) -> None:
        """更新动态止盈止损（多级响应版）"""
        if self.position_size <= 0 or (self.params_map.mode == "manual" and self.params_map.use_fixed_stops):
            return
            
        # 更新最高价和最低价
        self.state_map.highest_price = max(current_price, self.state_map.highest_price)
        self.state_map.lowest_price = min(current_price, self.state_map.lowest_price or current_price)
            
        # 计算当前盈亏
        self.state_map.current_profit = (current_price - self.entry_price) * self.position_size
        self.state_map.max_profit = max(self.state_map.current_profit, self.state_map.max_profit)
            
        # 获取市场状态和响应级别
        market_state = self._get_market_state()
        response_level = self._get_response_level()
        
        # 基础ATR倍数
        base_atr_multiple = self.state_map.atr * self.current_params["stop_mult"]
        
        if self.state_map.current_profit > 0:
            # 根据响应级别调整追踪止损
            trail_distance = base_atr_multiple * (0.6 if response_level == 3 else 0.8 if response_level == 2 else 1.0)
            profit_target = self.state_map.highest_price + base_atr_multiple * (1.2 if response_level == 3 else 1.5 if response_level == 2 else 2.0)
            
            # 根据盈利水平动态调整
            if self.entry_price > 0 and self.position_size > 0:
                profit_ratio = self.state_map.current_profit / (self.entry_price * self.position_size)
                
                # 盈利水平调整
                trail_distance *= 0.8 if profit_ratio > 0.05 else 0.9 if profit_ratio > 0.02 else 1.0
                
                # 趋势强度调整
                trail_distance *= 1.1 if self.state_map.trend_strength > 0.6 else 0.9 if self.state_map.trend_strength < 0.3 else 1.0
                
                # 波动率调整
                trail_distance *= 0.9 if self.state_map.volatility_ratio > self.params_map.vol_threshold else 1.0
                
                # 设置止损价格
                self.state_map.stop_loss = round(max(self.entry_price, self.state_map.highest_price - trail_distance), 2)
                
                # 设置止盈价格
                self.state_map.take_profit = round(profit_target, 2)
            else:
                self.state_map.stop_loss = round(max(self.entry_price, self.state_map.highest_price - base_atr_multiple), 2)
                self.state_map.take_profit = round(self.state_map.highest_price + base_atr_multiple * self.current_params["profit_mult"], 2)
        else:
            # 未盈利时的保护性止损
            protection_stop = self.entry_price - base_atr_multiple * (0.8 if response_level == 3 else 1.0 if response_level == 2 else 1.2)
            
            # 确保止损不会太远
            max_loss_distance = base_atr_multiple * 1.5
            self.state_map.stop_loss = round(max(protection_stop, self.entry_price - max_loss_distance), 2)

    def _check_risk_control(self) -> bool:
        """检查风险控制"""
        position = self.get_position(self.params_map.instrument_id)
        current_price = self.tick.last_price if self.tick else 0
        
        # 检查持仓限制
        if position.net_position >= self.params_map.max_positions:
            return False
            
        # 检查持仓盈亏
        if position.net_position > 0 and current_price > 0:
            position_profit = (current_price - self.entry_price) * position.net_position
            if position_profit < -self.params_map.max_loss_per_trade:
                return False
                
        # 检查持仓时间
        if position.net_position > 0 and self.state_map.trend_duration > self.params_map.max_hold_period:
            return False
            
        # 检查单笔风险
        position_value = self.position_size * self.entry_price
        if position_value > self.params_map.max_position_value:
            return False
            
        # 检查总风险
        total_risk = self._calculate_total_risk()
        if total_risk > self.params_map.max_total_risk:
            return False
            
        # 检查波动率风险
        if self.state_map.volatility_ratio > self.params_map.vol_threshold * 1.5:
            return False
            
        # 检查趋势质量
        if self._calculate_trend_quality() < 0.5:
            return False
            
        return True
        
    def _pass_risk_check(self) -> bool:
        """风险检查别名方法"""
        return self._check_risk_control()

    def _calculate_total_risk(self) -> float:
        """计算总风险"""
        if self.current_params is None:
            self.current_params = self.param_sets["震荡"]
            
        if not hasattr(self.state_map, 'atr') or self.state_map.atr is None:
            return 0.0
            
        if not hasattr(self, 'position_size') or self.position_size is None:
            return 0.0
            
        volatility_risk = self.state_map.atr * self.position_size * self.current_params["stop_mult"]
        position_risk = self.position_size * self.params_map.max_loss_per_trade
        
        return min(volatility_risk, position_risk)

    def _composite_signals(self, trend_weight: float, momentum_weight: float, volume_weight: float) -> float:
        """合成趋势、动量和成交量信号"""
        # 获取基础信号
        base_signals = self._get_base_signals()
        
        # 计算各信号强度
        trend_strength = (
            0.4 * base_signals["trend"]["ema_alignment"] +
            0.3 * base_signals["trend"]["trend_strength"] +
            0.3 * base_signals["trend"]["trend_duration"]
        )
        
        momentum_strength = (
            0.4 * base_signals["momentum"]["smoothed"] +
            0.3 * base_signals["momentum"]["acceleration"] +
            0.3 * base_signals["momentum"]["stoch"]
        )
        
        volume_strength = (
            0.4 * base_signals["volume"]["ratio"] +
            0.3 * base_signals["volume"]["factor"] +
            0.3 * base_signals["volume"]["breakout"]
        )
        
        # 应用权重合成最终信号
        composite_signal = (
            trend_strength * trend_weight +
            momentum_strength * momentum_weight +
            volume_strength * volume_weight
        )
        
        return composite_signal

    def calc_signal(self, kline: KLineData) -> None:
        """精密信号计算2.0，集成FFT分析
        
        核心增强模块：
        1. EMA三线排列检测（快/中/慢线多头排列+角度＞15度）
        2. 趋势-动量-成交量三角权重模型（4:3:3）
        3. ATR波动率自适应仓位算法
        4. FFT频域分析增强趋势识别精度
        """
        # 多周期趋势收敛检测
        trend_convergence = self._analyze_trend_convergence(
            fast_period=self.params_map.ema_fast_period,
            mid_period=self.params_map.ema_mid_period,
            slow_period=self.params_map.ema_slow_period
        )

        # 动态权重信号合成
        composite_signal = self._composite_signals(
            trend_weight=0.4,  # 趋势因子
            momentum_weight=0.3,  # RSI动量
            volume_weight=0.3  # 成交量
        )

        # 波动率自适应模块
        atr_ratio = self.state_map.atr / self.params_map.base_atr if self.params_map.base_atr > 0 else 1.0
        position_scale = 1.0 / (1.0 + 0.5 * max(0, atr_ratio - 1.0))

        # FFT信号增强
        fft_enhancement = 1.0
        if self.state_map.fft_signal_clarity > 0.5:
            fft_trend_factor = 1.2 if self.state_map.fft_trend_direction == "上升" else 0.8 if self.state_map.fft_trend_direction == "下降" else 1.0
            fft_enhancement = fft_trend_factor * (1.0 + self.state_map.fft_signal_clarity * 0.3)

        # 综合信号强度计算
        final_strength = composite_signal * trend_convergence * position_scale * fft_enhancement

        # 多空信号逻辑分离
        long_strength = final_strength if final_strength > 0 else 0
        short_strength = abs(final_strength) if final_strength < 0 else 0

        # FFT市场状态校验
        if self.state_map.fft_market_regime == "震荡市" and self.state_map.fft_signal_clarity > 0.7:
            # 震荡市中减少信号强度
            long_strength *= 0.7
            short_strength *= 0.7
        elif self.state_map.fft_market_regime == "趋势市" and self.state_map.fft_signal_clarity > 0.6:
            # 趋势市中增强信号
            long_strength *= 1.3
            short_strength *= 1.3

        # 信号阈值检查
        signal_threshold = 0.6 - (self.state_map.fft_signal_clarity * 0.1)
        
        # 最终买入信号确认
        self.buy_signal = (
            long_strength > signal_threshold and
            self.state_map.ema_fast > self.state_map.ema_mid > self.state_map.ema_slow and
            self.state_map.rsi > self.params_map.rsi_lower and
            self.state_map.volume_ratio > 1.0 and
            self._pass_risk_check()
        )

        # 更新市场结构参数
        self._adjust_by_market_structure(kline)

    def exec_signal(self):
        """交易信号执行，集成FFT分析优化"""
        if not self._check_risk_control():
            self.buy_signal = False
            return
            
        current_time = time.time()
        
        # 检查订单超时
        if self.order_id is not None and current_time - self.order_time > self.params_map.order_timeout:
            self.cancel_order(self.order_id)
            self.order_id = None
            self.order_time = 0
        
        position = self.get_position(self.params_map.instrument_id)
        self.position_size = position.net_position

        # 检查持仓限制
        if position.net_position >= self.params_map.max_positions:
            self.buy_signal = False

        # 获取市场状态和响应级别
        market_state = self._get_market_state()
        response_level = self._get_response_level()
        
        # FFT增强的卖出条件检查
        if self.tick and position.net_position > 0:
            current_price = self.tick.last_price
            current_profit = (current_price - self.entry_price) * self.position_size
            
            # 根据响应级别调整止损触发条件
            stop_loss_buffer = self.state_map.atr * (0.2 if response_level == 3 else 0.1 if response_level == 2 else 0)
            
            # FFT趋势反转信号
            fft_reversal_signal = (
                self.state_map.fft_trend_direction != self.state_map.trend_type and
                self.state_map.fft_signal_clarity > 0.6 and
                self.state_map.fft_trend_strength > 0.5
            )
            
            # FFT噪声风险信号
            fft_noise_risk = (
                self.state_map.fft_noise_level > 0.7 and
                self.state_map.fft_signal_clarity < 0.3
            )
            
            # 检查各种卖出条件
            self.sell_signal = (
                current_price <= self.state_map.stop_loss + stop_loss_buffer or
                current_price >= self.state_map.take_profit or
                (current_profit > 0 and current_profit < self.state_map.max_profit * (1 - self.params_map.profit_take_ratio)) or
                self._check_trend_reversal() or
                self._check_volatility_risk() or
                fft_reversal_signal or
                fft_noise_risk
            )

        # 卖出信号执行
        if position.net_position > 0 and self.sell_signal and self.trading:
            sell_price = self._get_optimal_execution_price("sell", response_level)
            
            if self._validate_execution_price(sell_price, "sell"):
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=sell_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
                self.order_time = current_time
                self._reset_position_state()

        # 买入信号执行（增加FFT验证）
        if self.buy_signal and self.trading:
            # FFT信号质量验证
            fft_quality_check = (
                self.state_map.fft_signal_clarity > 0.4 or
                self.state_map.fft_market_regime == "趋势市"
            )
            
            if fft_quality_check:
                buy_price = self._get_optimal_execution_price("buy", response_level)
                
                if self._validate_execution_price(buy_price, "buy"):
                    self.order_id = self.send_order(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        volume=self.params_map.order_volume,
                        price=buy_price,
                        order_direction="buy"
                    )
                    self.order_time = current_time

    def _check_volatility_risk(self) -> bool:
        """检查波动率风险，集成FFT噪声分析"""
        # 传统波动率风险检查
        traditional_vol_risk = (
            self.state_map.volatility_ratio > self.params_map.vol_threshold * 2.0 or
            self.state_map.atr > self.params_map.base_atr * 3.0
        )
        
        # FFT噪声风险检查
        fft_noise_risk = (
            self.state_map.fft_noise_level > 0.8 or
            self.state_map.fft_signal_clarity < 0.2
        )
        
        # 价格波动风险检查
        price_vol_risk = False
        if len(self._price_history) >= 5:
            recent_volatility = np.std(self._price_history[-5:])
            avg_price = np.mean(self._price_history[-5:])
            if recent_volatility / avg_price > 0.1:  # 10%以上的短期波动
                price_vol_risk = True
                
        return traditional_vol_risk or fft_noise_risk or price_vol_risk

    def _get_optimal_execution_price(self, direction: str, response_level: int) -> float:
        """获取最优执行价格"""
        if not self.tick:
            return 0
            
        bid_price = self.tick.bid_price1
        ask_price = self.tick.ask_price1
        last_price = self.tick.last_price
        
        price_slip = self._calculate_price_slip(direction, response_level)
        
        if direction == "buy":
            return (
                ask_price + price_slip if response_level == 3 else
                last_price + price_slip if response_level == 2 else
                bid_price + price_slip
            )
        else:
            return (
                bid_price - price_slip if response_level == 3 else
                last_price - price_slip if response_level == 2 else
                ask_price - price_slip
            )

    def _calculate_price_slip(self, direction: str, response_level: int) -> float:
        """计算价格滑点"""
        base_slip = self.state_map.atr * 0.1
        slip_mult = 0.5 if response_level == 3 else 1.0 if response_level == 2 else 1.5
        
        # 根据市场状态调整
        market_state = self._get_market_state()
        slip_mult *= 1.5 if market_state == "high_volatility" else 0.8 if market_state == "low_volatility" else 1.0
            
        # 根据成交量调整
        slip_mult *= 1.2 if self.state_map.volume_ratio > 2.0 else 0.8 if self.state_map.volume_ratio < 0.5 else 1.0
            
        return base_slip * slip_mult

    def _validate_execution_price(self, price: float, direction: str) -> bool:
        """验证执行价格合理性"""
        if not self.tick:
            return False
            
        bid_price = self.tick.bid_price1
        ask_price = self.tick.ask_price1
        
        if direction == "buy":
            price_deviation = abs(price - ask_price) / ask_price
            max_deviation = self.state_map.atr * 0.2 / ask_price
            return price_deviation <= max_deviation and bid_price <= price <= ask_price * 1.1
        else:
            price_deviation = abs(price - bid_price) / bid_price
            max_deviation = self.state_map.atr * 0.2 / bid_price
            return price_deviation <= max_deviation and bid_price * 0.9 <= price <= ask_price

    def _adjust_by_market_structure(self, kline: KLineData) -> None:
        """根据市场结构调整交易信号"""
        # 检查斐波那契水平
        if not self.state_map.fib_levels:
            self._calculate_fib_levels(kline)
            
        # 检查支撑阻力位
        current_price = kline.close
        support = self.state_map.fib_support
        resistance = self.state_map.fib_resistance
        
        # 根据价格位置调整信号
        if current_price <= support * (1 + self.params_map.fib_deviation):
            self.buy_signal = True
            self.sell_signal = False
        elif current_price >= resistance * (1 - self.params_map.fib_deviation):
            self.buy_signal = False
            self.sell_signal = True
            
        # 根据波动率状态调整
        if self.state_map.volatility_state == "高波动":
            self.buy_signal = False
            self.sell_signal = False
            
        # 更新多周期趋势状态
        self._update_multi_timeframe_trends()
        
        # 根据趋势共识调整
        if (self._trend_short == self._trend_mid == 
            self._trend_long != "震荡"):
            self.state_map.trend_consensus = True
            if self.state_map.trend_consensus:
                if self._trend_short == "上升":
                    self.buy_signal = True
                elif self._trend_short == "下降":
                    self.sell_signal = True

    def _update_multi_timeframe_trends(self) -> None:
        """更新多周期趋势状态"""
        if len(self._price_history) < max(self.params_map.trend_short_period, 
                                         self.params_map.trend_mid_period,
                                         self.params_map.trend_long_period):
            return
        
        # 短周期趋势
        short_changes = np.diff(self._price_history[-self.params_map.trend_short_period:])
        self._trend_short = "上升" if np.mean(short_changes) > 0 else "下降" if np.mean(short_changes) < 0 else "震荡"
        
        # 中周期趋势
        mid_changes = np.diff(self._price_history[-self.params_map.trend_mid_period:])
        self._trend_mid = "上升" if np.mean(mid_changes) > 0 else "下降" if np.mean(mid_changes) < 0 else "震荡"
        
        # 长周期趋势
        long_changes = np.diff(self._price_history[-self.params_map.trend_long_period:])
        self._trend_long = "上升" if np.mean(long_changes) > 0 else "下降" if np.mean(long_changes) < 0 else "震荡"

    def _calculate_fib_levels(self, kline: KLineData) -> None:
        """计算斐波那契水平"""
        if len(self._price_history) < self.params_map.fib_period:
            return
            
        high = max(self._price_history[-self.params_map.fib_period:])
        low = min(self._price_history[-self.params_map.fib_period:])
        diff = high - low
        
        self.state_map.fib_levels = {
            "0.236": high - diff * 0.236,
            "0.382": high - diff * 0.382,
            "0.5": high - diff * 0.5,
            "0.618": high - diff * 0.618,
            "0.786": high - diff * 0.786
        }
        
        self.state_map.fib_support = self.state_map.fib_levels["0.618"]
        self.state_map.fib_resistance = self.state_map.fib_levels["0.382"]

    def _check_trend_reversal(self) -> bool:
        """检查趋势反转"""
        ema_reversal = self.state_map.ema_fast < self.state_map.ema_mid < self.state_map.ema_slow
        rsi_reversal = self.state_map.rsi < self.params_map.rsi_lower and self._rsi_momentum < 0
        volume_reversal = self.state_map.volume_ratio < 0.8 and self.state_map.volume_factor < 0
        
        return ema_reversal and (rsi_reversal or volume_reversal) and self.state_map.trend_strength < 0.3

    def _reset_position_state(self):
        """重置持仓状态"""
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        self.state_map.highest_price = 0
        self.state_map.lowest_price = 0
        self.state_map.current_profit = 0
        self.state_map.max_profit = 0

    def _reset_state(self):
        """完全重置策略状态"""
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.tick = None
        self._price_history = []
        self._volume_history = []
        self._rsi_history = []
        self.trend_count = 0
        
        # 重置FFT相关状态
        self._fft_price_buffer = []
        self._fft_smoothed_trend = 0
        self._fft_frequency_weights = {}
        self._fft_phase_history = []
        
        self._indicator_cache = {
            'ema': {'last_update': 0, 'value': None},
            'rsi': {'last_update': 0, 'value': None},
            'atr': {'last_update': 0, 'value': None},
            'volume': {'last_update': 0, 'value': None},
            'momentum': {'last_update': 0, 'value': None},
            'fft': {'last_update': 0, 'value': None}
        }
        
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        self.state_map.highest_price = 0
        self.state_map.lowest_price = 0
        self.state_map.current_profit = 0
        self.state_map.max_profit = 0

    def _check_risk_control(self) -> bool:
        """检查风险控制"""
        position = self.get_position(self.params_map.instrument_id)
        current_price = self.tick.last_price if self.tick else 0
        
        # 检查持仓限制
        if position.net_position >= self.params_map.max_positions:
            return False
            
        # 检查持仓盈亏
        if position.net_position > 0 and current_price > 0:
            position_profit = (current_price - self.entry_price) * position.net_position
            if position_profit < -self.params_map.max_loss_per_trade:
                return False
                
        # 检查持仓时间
        if position.net_position > 0 and self.state_map.trend_duration > self.params_map.max_hold_period:
            return False
            
        # 检查单笔风险
        position_value = self.position_size * self.entry_price
        if position_value > self.params_map.max_position_value:
            return False
            
        # 检查总风险
        total_risk = self._calculate_total_risk()
        if total_risk > self.params_map.max_total_risk:
            return False
            
        # 检查波动率风险
        if self.state_map.volatility_ratio > self.params_map.vol_threshold * 1.5:
            return False
            
        # 检查趋势质量
        if self._calculate_trend_quality() < 0.5:
            return False
            
        return True
        
    def _pass_risk_check(self) -> bool:
        """风险检查别名方法"""
        return self._check_risk_control()

    def _calculate_total_risk(self) -> float:
        """计算总风险"""
        if self.current_params is None:
            self.current_params = self.param_sets["震荡"]
            
        if not hasattr(self.state_map, 'atr') or self.state_map.atr is None:
            return 0.0
            
        if not hasattr(self, 'position_size') or self.position_size is None:
            return 0.0
            
        volatility_risk = self.state_map.atr * self.position_size * self.current_params["stop_mult"]
        position_risk = self.position_size * self.params_map.max_loss_per_trade
        
        return min(volatility_risk, position_risk)

    def _composite_signals(self, trend_weight: float, momentum_weight: float, volume_weight: float) -> float:
        """合成趋势、动量和成交量信号"""
        # 获取基础信号
        base_signals = self._get_base_signals()
        
        # 计算各信号强度
        trend_strength = (
            0.4 * base_signals["trend"]["ema_alignment"] +
            0.3 * base_signals["trend"]["trend_strength"] +
            0.3 * base_signals["trend"]["trend_duration"]
        )
        
        momentum_strength = (
            0.4 * base_signals["momentum"]["smoothed"] +
            0.3 * base_signals["momentum"]["acceleration"] +
            0.3 * base_signals["momentum"]["stoch"]
        )
        
        volume_strength = (
            0.4 * base_signals["volume"]["ratio"] +
            0.3 * base_signals["volume"]["factor"] +
            0.3 * base_signals["volume"]["breakout"]
        )
        
        # 应用权重合成最终信号
        composite_signal = (
            trend_strength * trend_weight +
            momentum_strength * momentum_weight +
            volume_strength * volume_weight
        )
        
        return composite_signal

    def _fourier_transform_analysis(self, price_data: list) -> dict:
        """傅里叶变换市场分析
        
        基于FFT的频域分析，提取市场的周期性特征和趋势信号
        """
        if len(price_data) < self.params_map.fft_window_size:
            return {
                'trend_direction': '震荡',
                'trend_strength': 0.0,
                'dominant_frequency': 0.0,
                'cycle_period': 0,
                'noise_level': 0.5,
                'signal_clarity': 0.0,
                'market_regime': '震荡市',
                'phase_angle': 0.0,
                'amplitude_ratio': 0.0
            }
        
        # 准备数据：取最新的窗口数据
        window_data = np.array(price_data[-self.params_map.fft_window_size:])
        
        # 数据预处理：去趋势和标准化
        detrended_data = self._detrend_price_data(window_data)
        normalized_data = self._normalize_data(detrended_data)
        
        # 应用汉宁窗减少频谱泄漏
        windowed_data = normalized_data * np.hanning(len(normalized_data))
        
        # 执行FFT变换
        fft_result = np.fft.fft(windowed_data)
        frequencies = np.fft.fftfreq(len(windowed_data))
        
        # 计算功率谱密度
        power_spectrum = np.abs(fft_result) ** 2
        phase_spectrum = np.angle(fft_result)
        
        # 只分析正频率部分
        positive_freqs = frequencies[:len(frequencies)//2]
        positive_power = power_spectrum[:len(power_spectrum)//2]
        positive_phase = phase_spectrum[:len(phase_spectrum)//2]
        
        # 过滤低频噪声
        noise_threshold = np.max(positive_power) * self.params_map.fft_noise_filter
        significant_indices = positive_power > noise_threshold
        
        # 找到主导频率
        dominant_freq_indices = np.argsort(positive_power)[-self.params_map.fft_dominant_freq_count:]
        dominant_frequencies = positive_freqs[dominant_freq_indices]
        dominant_powers = positive_power[dominant_freq_indices]
        dominant_phases = positive_phase[dominant_freq_indices]
        
        # 计算主导频率
        if len(dominant_frequencies) > 0:
            # 加权平均计算主导频率
            weights = dominant_powers / np.sum(dominant_powers)
            main_frequency = np.sum(dominant_frequencies * weights)
            main_phase = np.sum(dominant_phases * weights)
        else:
            main_frequency = 0.0
            main_phase = 0.0
        
        # 计算周期
        cycle_period = int(1.0 / main_frequency) if main_frequency > 0 else 0
        
        # 分析趋势方向
        trend_direction, trend_strength = self._analyze_fft_trend(
            fft_result, frequencies, window_data
        )
        
        # 计算噪声水平
        noise_level = self._calculate_noise_level(positive_power, significant_indices)
        
        # 计算信号清晰度
        signal_clarity = self._calculate_signal_clarity(dominant_powers, positive_power)
        
        # 判断市场状态
        market_regime = self._determine_market_regime(
            trend_strength, signal_clarity, cycle_period
        )
        
        # 计算振幅比值
        amplitude_ratio = self._calculate_amplitude_ratio(dominant_powers)
        
        return {
            'trend_direction': trend_direction,
            'trend_strength': trend_strength,
            'dominant_frequency': main_frequency,
            'cycle_period': cycle_period,
            'noise_level': noise_level,
            'signal_clarity': signal_clarity,
            'market_regime': market_regime,
            'phase_angle': main_phase,
            'amplitude_ratio': amplitude_ratio
        }
    
    def _detrend_price_data(self, data: np.ndarray) -> np.ndarray:
        """去趋势处理"""
        # 使用线性回归去除长期趋势
        x = np.arange(len(data))
        coeffs = np.polyfit(x, data, 1)
        trend = np.polyval(coeffs, x)
        return data - trend
    
    def _normalize_data(self, data: np.ndarray) -> np.ndarray:
        """数据标准化"""
        mean_val = np.mean(data)
        std_val = np.std(data)
        return (data - mean_val) / (std_val if std_val > 0 else 1.0)
    
    def _analyze_fft_trend(self, fft_result: np.ndarray, frequencies: np.ndarray, 
                          original_data: np.ndarray) -> tuple:
        """分析FFT趋势方向和强度"""
        # 分析低频分量的相位信息
        low_freq_indices = np.where(np.abs(frequencies) < 0.1)[0]
        if len(low_freq_indices) > 1:
            low_freq_power = np.abs(fft_result[low_freq_indices])
            low_freq_phase = np.angle(fft_result[low_freq_indices])
            
            # 加权相位计算
            weights = low_freq_power / np.sum(low_freq_power)
            weighted_phase = np.sum(low_freq_phase * weights)
            
            # 根据相位判断趋势方向
            if -np.pi/4 < weighted_phase < np.pi/4:
                trend_direction = "上升"
                base_strength = 0.7
            elif 3*np.pi/4 < weighted_phase or weighted_phase < -3*np.pi/4:
                trend_direction = "下降" 
                base_strength = 0.7
            else:
                trend_direction = "震荡"
                base_strength = 0.3
        else:
            trend_direction = "震荡"
            base_strength = 0.3
        
        # 计算趋势强度
        recent_slope = (original_data[-1] - original_data[-min(10, len(original_data)//4)]) / min(10, len(original_data)//4)
        slope_strength = min(1.0, abs(recent_slope) * 100)
        
        # 综合强度计算
        trend_strength = min(1.0, base_strength * (0.6 + 0.4 * slope_strength))
        
        return trend_direction, trend_strength
    
    def _calculate_noise_level(self, power_spectrum: np.ndarray, 
                              significant_indices: np.ndarray) -> float:
        """计算噪声水平"""
        if len(significant_indices) == 0:
            return 1.0
        
        total_power = np.sum(power_spectrum)
        noise_power = np.sum(power_spectrum[~significant_indices])
        
        return min(1.0, noise_power / total_power) if total_power > 0 else 0.5
    
    def _calculate_signal_clarity(self, dominant_powers: np.ndarray, 
                                 all_powers: np.ndarray) -> float:
        """计算信号清晰度"""
        if len(dominant_powers) == 0:
            return 0.0
        
        dominant_total = np.sum(dominant_powers)
        all_total = np.sum(all_powers)
        
        return min(1.0, dominant_total / all_total) if all_total > 0 else 0.0
    
    def _determine_market_regime(self, trend_strength: float, signal_clarity: float,
                               cycle_period: int) -> str:
        """判断市场状态"""
        if trend_strength > self.params_map.fft_trend_threshold and signal_clarity > 0.6:
            return "趋势市"
        elif signal_clarity > 0.7 and 5 <= cycle_period <= 20:
            return "震荡市" 
        else:
            return "转换期"
    
    def _calculate_amplitude_ratio(self, dominant_powers: np.ndarray) -> float:
        """计算振幅比值"""
        if len(dominant_powers) < 2:
            return 0.0
        
        sorted_powers = np.sort(dominant_powers)[::-1]
        return sorted_powers[0] / sorted_powers[1] if sorted_powers[1] > 0 else 0.0
    
    def _update_fft_analysis(self, kline: KLineData) -> None:
        """更新FFT分析结果"""
        # 更新价格缓冲区
        self._fft_price_buffer.append(kline.close)
        if len(self._fft_price_buffer) > self.params_map.fft_window_size * 2:
            self._fft_price_buffer.pop(0)
        
        # 检查是否有足够数据进行FFT分析
        if len(self._fft_price_buffer) >= self.params_map.fft_window_size:
            # 获取缓存的FFT结果
            cached_fft = self._get_cached_indicator('fft', max_age=2.0)
            if cached_fft is None:
                fft_analysis = self._fourier_transform_analysis(self._fft_price_buffer)
                self._update_indicator_cache('fft', fft_analysis)
            else:
                fft_analysis = cached_fft
            
            # 更新FFT状态
            self.state_map.fft_trend_direction = fft_analysis['trend_direction']
            self.state_map.fft_trend_strength = round(fft_analysis['trend_strength'], 3)
            self.state_map.fft_dominant_frequency = round(fft_analysis['dominant_frequency'], 6)
            self.state_map.fft_cycle_period = fft_analysis['cycle_period']
            self.state_map.fft_noise_level = round(fft_analysis['noise_level'], 3)
            self.state_map.fft_signal_clarity = round(fft_analysis['signal_clarity'], 3)
            self.state_map.fft_market_regime = fft_analysis['market_regime']
            self.state_map.fft_phase_angle = round(fft_analysis['phase_angle'], 3)
            self.state_map.fft_amplitude_ratio = round(fft_analysis['amplitude_ratio'], 3)
            
            # 平滑FFT趋势信号
            self._fft_smoothed_trend = (
                self._fft_smoothed_trend * (1 - self.params_map.fft_smoothing_alpha) +
                fft_analysis['trend_strength'] * self.params_map.fft_smoothing_alpha
            )
    
    def _integrate_fft_with_traditional_analysis(self) -> str:
        """整合FFT分析与传统技术分析"""
        # 获取传统分析结果
        traditional_trend = self.state_map.trend_type
        traditional_strength = self.state_map.trend_strength
        
        # 获取FFT分析结果
        fft_trend = self.state_map.fft_trend_direction
        fft_strength = self.state_map.fft_trend_strength
        fft_clarity = self.state_map.fft_signal_clarity
        
        # 权重分配
        fft_weight = min(0.6, fft_clarity * 1.2)  # FFT权重基于信号清晰度
        traditional_weight = 1.0 - fft_weight
        
        # 趋势一致性检查
        trend_consensus = (traditional_trend == fft_trend)
        
        # 综合强度计算
        combined_strength = (
            traditional_strength * traditional_weight +
            fft_strength * fft_weight
        )
        
        # 最终趋势判断
        if trend_consensus and combined_strength > self.params_map.trend_strength_threshold:
            final_trend = traditional_trend
            confidence = combined_strength
        elif fft_strength > traditional_strength and fft_clarity > 0.7:
            final_trend = fft_trend
            confidence = fft_strength
        elif traditional_strength > 0.6:
            final_trend = traditional_trend
            confidence = traditional_strength
        else:
            final_trend = "震荡"
            confidence = 0.3
        
        # 更新信号质量
        self.state_map.signal_quality = min(1.0, confidence * (1.0 + fft_clarity * 0.5))
        
        return final_trend