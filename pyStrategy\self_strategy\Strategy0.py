"""
Strategy3 - 高级交易策略 (增强版)
架构: 原始行情 → 指标计算 → 自适应综合模块 → 执行引擎
完全符合无限易Pro架构标准
"""

from typing import Literal, Dict, List, Optional, Tuple, Any
import numpy as np
from collections import deque
import math
import time
# 新增导入
from river import linear_model, preprocessing
from river import tree, feature_selection

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K线周期")
    
    # 技术指标参数
    hull_period: int = Field(default=9, title="HULL周期")
    stc_fast: int = Field(default=23, title="STC快周期")
    stc_slow: int = Field(default=50, title="STC慢周期")
    stc_cycle: int = Field(default=10, title="STC循环周期")
    
    # 交易参数
    trade_direction: Literal["buy", "sell"] = Field(default="buy", title="交易方向")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    
    # 风险控制参数
    stop_loss_pct: float = Field(default=0.02, title="止损百分比")
    take_profit_pct: float = Field(default=0.05, title="止盈百分比")
    
    # 模糊系统参数
    fuzzy_sensitivity: float = Field(default=0.5, title="模糊系统敏感度")
    
    # 自适应系统参数
    adaptation_speed: float = Field(default=0.1, title="适应速度")
    learning_enabled: bool = Field(default=True, title="启用学习")
    
    # 模块权重参数
    technical_weight: float = Field(default=0.25, title="技术指标权重")
    pattern_weight: float = Field(default=0.25, title="形态识别权重")
    wave_weight: float = Field(default=0.25, title="波段分析权重")
    fuzzy_weight: float = Field(default=0.25, title="模糊推理权重")
    
    # 市场状态参数
    volatility_threshold: float = Field(default=0.02, title="波动率阈值")
    trend_strength_threshold: float = Field(default=0.5, title="趋势强度阈值")


class State(BaseState):
    """状态映射模型"""
    # HULL指标状态
    hull_value: float = Field(default=0, title="HULL当前值")
    hull_prev: float = Field(default=0, title="HULL前值")
    
    # STC指标状态
    stc_value: float = Field(default=0, title="STC当前值")
    stc_signal: float = Field(default=0, title="STC信号线")
    
    # 模糊系统状态
    risk_level: str = Field(default="", title="风险等级")
    action_level: str = Field(default="", title="行动等级")
    confidence: float = Field(default=0, title="置信度")
    
    # K线形态状态
    pattern_detected: str = Field(default="", title="检测到的形态")
    pattern_strength: float = Field(default=0, title="形态强度")
    
    # 波段识别状态
    wave_type: str = Field(default="", title="当前波段类型")
    wave_strength: float = Field(default=0, title="波段强度")
    
    # 市场状态
    market_regime: str = Field(default="unknown", title="市场状态")
    volatility_level: float = Field(default=0.0, title="波动率水平")
    trend_strength: float = Field(default=0.0, title="趋势强度")
    
    # 模块性能跟踪
    technical_accuracy: float = Field(default=0.5, title="技术指标准确率")
    pattern_accuracy: float = Field(default=0.5, title="形态识别准确率")
    wave_accuracy: float = Field(default=0.5, title="波段分析准确率")
    fuzzy_accuracy: float = Field(default=0.5, title="模糊推理准确率")
    
    # 综合决策状态
    integrated_signal: float = Field(default=0.0, title="综合信号强度")
    signal_confidence: float = Field(default=0.0, title="信号置信度")
    adaptive_weights: Dict[str, float] = Field(default_factory=dict, title="自适应权重")


# ==================== 模糊系统 ====================

class TrapezoidalFuzzyNumber:
    """梯形模糊数实现"""
    def __init__(self, a: float, b: float, c: float, d: float):
        """
        初始化梯形模糊数
        
        参数:
            a: 左下角点
            b: 左上角点
            c: 右上角点
            d: 右下角点
        """
        assert a <= b <= c <= d, "Invalid trapezoid parameters"
        self.a, self.b, self.c, self.d = a, b, c, d
        
    def membership(self, x: float) -> float:
        """
        计算隶属度
        
        参数:
            x: 输入值
            
        返回:
            隶属度值 (0-1)
        """
        if x < self.a:
            return 0.0
        elif self.a <= x < self.b:
            return (x - self.a) / (self.b - self.a)
        elif self.b <= x <= self.c:
            return 1.0
        elif self.c < x <= self.d:
            return (self.d - x) / (self.d - self.c)
        else:
            return 0.0
    
    def centroid(self) -> float:
        """
        计算重心去模糊化值
        
        返回:
            重心值
        """
        return (self.a + self.b + self.c + self.d) / 4.0


class FuzzySystem:
    """标准模糊推理系统（优化版）"""
    def __init__(self, sensitivity: float = 0.5):
        """
        初始化模糊系统
        
        参数:
            sensitivity: 敏感度参数 (0-1)
        """
        self.sensitivity = sensitivity
        # 初始化模糊集
        self.stability_sets = self._init_stability_sets()
        self.volatility_sets = self._init_volatility_sets()
        self.profit_sets = self._init_profit_sets()
        # 初始化规则库
        self.rules = self._init_rules()
        # 风险和行动等级
        self.risk_levels = ["RiskLow", "RiskMedium", "RiskHigh"]
        self.action_levels = ["Conservative", "Normal", "Aggressive", "Stop"]

    def _init_rules(self) -> List[Dict]:
        """扩展为16条规则，增加权重和学习率"""
        # 规则覆盖更多组合，初始权重1.0，学习率0.01
        return [
            {"stability": "Low", "volatility": "Low", "profit": "Neutral", "risk": "RiskLow", "action": "Conservative", "weight": 1.0, "learn_rate": 0.01},
            {"stability": "Low", "volatility": "Low", "profit": "Positive", "risk": "RiskMedium", "action": "Normal", "weight": 1.0, "learn_rate": 0.01},
            {"stability": "Low", "volatility": "Medium", "profit": "Neutral", "risk": "RiskMedium", "action": "Normal", "weight": 1.0, "learn_rate": 0.01},
            {"stability": "Low", "volatility": "High", "profit": "Negative", "risk": "RiskLow", "action": "Stop", "weight": 1.0, "learn_rate": 0.01},
            {"stability": "Medium", "volatility": "Low", "profit": "Positive", "risk": "RiskMedium", "action": "Normal", "weight": 1.0, "learn_rate": 0.01},
            {"stability": "Medium", "volatility": "Low", "profit": "Negative", "risk": "RiskLow", "action": "Conservative", "weight": 1.0, "learn_rate": 0.01},
            {"stability": "Medium", "volatility": "Medium", "profit": "Neutral", "risk": "RiskMedium", "action": "Normal", "weight": 1.0, "learn_rate": 0.01},
            {"stability": "Medium", "volatility": "High", "profit": "Positive", "risk": "RiskHigh", "action": "Aggressive", "weight": 1.0, "learn_rate": 0.01},
            {"stability": "Medium", "volatility": "High", "profit": "Negative", "risk": "RiskHigh", "action": "Stop", "weight": 1.0, "learn_rate": 0.01},
            {"stability": "High", "volatility": "Low", "profit": "Positive", "risk": "RiskHigh", "action": "Aggressive", "weight": 1.0, "learn_rate": 0.01},
            {"stability": "High", "volatility": "Low", "profit": "Negative", "risk": "RiskMedium", "action": "Normal", "weight": 1.0, "learn_rate": 0.01},
            {"stability": "High", "volatility": "Medium", "profit": "Positive", "risk": "RiskHigh", "action": "Aggressive", "weight": 1.0, "learn_rate": 0.01},
            {"stability": "High", "volatility": "Medium", "profit": "Negative", "risk": "RiskMedium", "action": "Normal", "weight": 1.0, "learn_rate": 0.01},
            {"stability": "High", "volatility": "High", "profit": "Positive", "risk": "RiskHigh", "action": "Aggressive", "weight": 1.0, "learn_rate": 0.01},
            {"stability": "High", "volatility": "High", "profit": "Negative", "risk": "RiskHigh", "action": "Stop", "weight": 1.0, "learn_rate": 0.01},
            {"stability": "Low", "volatility": "Medium", "profit": "Positive", "risk": "RiskMedium", "action": "Normal", "weight": 1.0, "learn_rate": 0.01},
        ]

    def _init_stability_sets(self):
        """
        初始化稳定性模糊集（Low, Medium, High）
        """
        return {
            "Low": TrapezoidalFuzzyNumber(0.0, 0.0, 0.3, 0.5),
            "Medium": TrapezoidalFuzzyNumber(0.3, 0.5, 0.5, 0.7),
            "High": TrapezoidalFuzzyNumber(0.5, 0.7, 1.0, 1.0)
        }

    def _init_volatility_sets(self):
        """
        初始化波动率模糊集（Low, Medium, High）
        """
        return {
            "Low": TrapezoidalFuzzyNumber(0.0, 0.0, 0.01, 0.02),
            "Medium": TrapezoidalFuzzyNumber(0.01, 0.02, 0.03, 0.04),
            "High": TrapezoidalFuzzyNumber(0.03, 0.04, 1.0, 1.0)
        }

    def _init_profit_sets(self):
        """
        初始化利润趋势模糊集（Negative, Neutral, Positive）
        """
        return {
            "Negative": TrapezoidalFuzzyNumber(-1.0, -1.0, -0.01, 0.0),
            "Neutral": TrapezoidalFuzzyNumber(-0.01, 0.0, 0.0, 0.01),
            "Positive": TrapezoidalFuzzyNumber(0.0, 0.01, 1.0, 1.0)
        }

    def fuzzify(self, inputs: Dict[str, float]) -> Dict[str, Dict[str, float]]:
        """
        模糊化输入（优化：stability=0.6*hull_trend_strength+0.4*(1-volatility)，profit_trend=stc斜率）
        参数:
            inputs: 包含hull_trend_strength, volatility, stc_values的字典
        返回:
            各输入变量的隶属度字典
        """
        # 兼容旧接口
        hull_trend_strength = inputs.get('hull_trend_strength', 0.5)
        volatility = inputs.get('volatility', 0.05)
        stc_values = inputs.get('stc_values', [0, 0, 0, 0])
        close_price = inputs.get('close_price', 1)
        # 新稳定性
        stability = 0.6 * hull_trend_strength + 0.4 * (1 - volatility)
        # 新利润趋势
        if len(stc_values) >= 4:
            profit_trend = (stc_values[-1] - stc_values[-4]) / 3 / 100.0
        else:
            profit_trend = 0.0
        memberships = {
            'stability': {k: f.membership(stability) for k, f in self.stability_sets.items()},
            'volatility': {k: f.membership(volatility) for k, f in self.volatility_sets.items()},
            'profit': {k: f.membership(profit_trend) for k, f in self.profit_sets.items()}
        }
        return memberships

    def infer(self, memberships: Dict[str, Dict[str, float]], rule_feedback: Optional[List[bool]] = None) -> Tuple[str, str, float]:
        """
        模糊推理（带权重自适应）
        参数:
            memberships: 模糊化后的隶属度字典
            rule_feedback: 每条规则的表现反馈（可选，True=正确，False=错误）
        返回:
            (风险等级, 行动等级, 置信度)
        """
        stability_mem = memberships['stability']
        volatility_mem = memberships['volatility']
        profit_mem = memberships['profit']
        # 计算综合风险等级
        risk_scores = {}
        for risk_level in self.risk_levels:
            score = 0.0
            for idx, rule in enumerate(self.rules):
                if rule['risk'] == risk_level:
                    s_match = stability_mem.get(rule['stability'], 0)
                    v_match = volatility_mem.get(rule['volatility'], 0)
                    p_match = profit_mem.get(rule['profit'], 0)
                    # 应用敏感度调整
                    rule_strength = min(s_match, v_match, p_match) * rule.get('weight', 1.0)
                    rule_strength = rule_strength * (1 + (self.sensitivity - 0.5))
                    score += rule_strength
                    # 权重自适应
                    if rule_feedback is not None:
                        if rule_feedback[idx]:
                            rule['weight'] = min(1.5, rule['weight'] + rule['learn_rate'])
                        else:
                            rule['weight'] = max(0.5, rule['weight'] - rule['learn_rate'])
            risk_scores[risk_level] = score
        # 计算综合行动等级
        action_scores = {}
        for action_level in self.action_levels:
            score = 0.0
            for idx, rule in enumerate(self.rules):
                if rule['action'] == action_level:
                    s_match = stability_mem.get(rule['stability'], 0)
                    v_match = volatility_mem.get(rule['volatility'], 0)
                    p_match = profit_mem.get(rule['profit'], 0)
                    rule_strength = min(s_match, v_match, p_match) * rule.get('weight', 1.0)
                    rule_strength = rule_strength * (1 + (self.sensitivity - 0.5))
                    score += rule_strength
                    # 权重自适应
                    if rule_feedback is not None:
                        if rule_feedback[idx]:
                            rule['weight'] = min(1.5, rule['weight'] + rule['learn_rate'])
                        else:
                            rule['weight'] = max(0.5, rule['weight'] - rule['learn_rate'])
            action_scores[action_level] = score
        # 选择最高分数的风险等级和行动等级
        best_risk = max(risk_scores.items(), key=lambda x: x[1])[0]
        best_action = max(action_scores.items(), key=lambda x: x[1])[0]
        # 计算置信度
        confidence = max(max(risk_scores.values()), max(action_scores.values()))
        return best_risk, best_action, confidence


class HullMovingAverage:
    """TradingView标准Hull移动平均线（优化版）"""
    def __init__(self, period: int = 9):
        self.period = period
        self.half_period = max(1, int(period / 2))
        self.sqrt_period = max(1, int(np.sqrt(period)))
        self.prices = deque(maxlen=500)
        self._is_ready = False
        # 新增缓存队列
        self.wma_half_cache = deque(maxlen=100)
        self.wma_full_cache = deque(maxlen=100)
        self.raw_cache = deque(maxlen=100)
        self.hma_cache = deque(maxlen=100)
        self.cache_valid = False

    def _wma(self, data, period):
        if len(data) < period:
            return None
        weights = np.arange(1, period + 1)
        window = np.array(list(data)[-period:])
        return np.dot(window, weights) / weights.sum()

    def update(self, price: float) -> None:
        if not isinstance(price, (int, float)) or math.isnan(price) or math.isinf(price):
            return
        self.prices.append(float(price))
        if len(self.prices) >= self.period:
            self._is_ready = True
        # 增量计算并维护缓存
        if not self._is_ready:
            self.cache_valid = False
            return
        # 1. WMA(price, n/2)
        wma_half = self._wma(self.prices, self.half_period)
        if wma_half is not None:
            self.wma_half_cache.append(wma_half)
        # 2. WMA(price, n)
        wma_full = self._wma(self.prices, self.period)
        if wma_full is not None:
            self.wma_full_cache.append(wma_full)
        # 3. raw = 2*wma_half - wma_full
        if len(self.wma_half_cache) > 0 and len(self.wma_full_cache) > 0:
            raw = 2 * self.wma_half_cache[-1] - self.wma_full_cache[-1]
            self.raw_cache.append(raw)
        # 4. WMA(raw, sqrt(n))
        if len(self.raw_cache) >= self.sqrt_period:
            hma = self._wma(self.raw_cache, self.sqrt_period)
            if hma is not None:
                self.hma_cache.append(hma)
        self.cache_valid = True

    def get_hull(self):
        if not self._is_ready or len(self.hma_cache) < 2:
            return None, None
        # 优先用缓存
        return self.hma_cache[-1], self.hma_cache[-2]
    
    def is_ready(self) -> bool:
        return self._is_ready

    def get_trend(self):
        """
        获取趋势方向 ("BULLISH", "BEARISH", "NEUTRAL")
        """
        h1, h2 = self.get_hull()
        if h1 is None or h2 is None:
            return None
        if h1 > h2:
            return "BULLISH"
        elif h1 < h2:
            return "BEARISH"
        else:
            return "NEUTRAL"


class SchaffTrendCycle:
    """Schaff趋势周期指标"""
    def __init__(self, fast_period: int = 23, slow_period: int = 50, cycle_period: int = 10):
        """
        初始化Schaff趋势周期指标
        
        参数:
            fast_period: 快速周期
            slow_period: 慢速周期
            cycle_period: 循环周期
        """
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.cycle_period = cycle_period
        
        # 使用deque实现高效循环缓冲区
        self.prices = deque(maxlen=200)
        self.macd_line = deque(maxlen=100)
        self.stoch1 = deque(maxlen=50)
        self.stoch2 = deque(maxlen=50)
        self.stc_final = deque(maxlen=30)
        self.stc_signal = deque(maxlen=20)
        
    def ema(self, data: List[float], period: int) -> Optional[List[float]]:
        """
        指数移动平均
        
        参数:
            data: 价格数据
            period: 周期
            
        返回:
            EMA值列表
        """
        if len(data) < period:
            return None
        
        alpha = 2.0 / (period + 1)
        ema_values = [data[0]]
        
        for i in range(1, len(data)):
            ema = alpha * data[i] + (1 - alpha) * ema_values[-1]
            ema_values.append(ema)
        
        return ema_values
    
    def update(self, price: float) -> None:
        """
        更新STC值
        
        参数:
            price: 最新价格
        """
        self.prices.append(price)
        
        if len(self.prices) < self.slow_period:
            return
        
        # 计算MACD线
        fast_ema = self.ema(list(self.prices), self.fast_period)
        slow_ema = self.ema(list(self.prices), self.slow_period)
        
        if fast_ema and slow_ema:
            # 取最新值计算MACD
            macd_value = fast_ema[-1] - slow_ema[-1]
            self.macd_line.append(macd_value)
            
            # 计算Stoch1
            if len(self.macd_line) >= self.cycle_period:
                stoch1_value = self._calculate_stochastic_single(list(self.macd_line), self.cycle_period)
                if stoch1_value is not None:
                    self.stoch1.append(stoch1_value)
                    
                    # 计算Stoch2
                    if len(self.stoch1) >= self.cycle_period:
                        stoch2_value = self._calculate_stochastic_single(list(self.stoch1), self.cycle_period)
                        if stoch2_value is not None:
                            self.stoch2.append(stoch2_value)
                            
                            # 计算最终STC值
                            if len(self.stoch2) >= 3:
                                stc_smoothed = self.ema(list(self.stoch2), 3)
                                if stc_smoothed:
                                    self.stc_final.append(stc_smoothed[-1])
                                    
                                    # 计算STC信号线
                                    if len(self.stc_final) >= 3:
                                        signal_values = self.ema(list(self.stc_final), 3)
                                        if signal_values:
                                            self.stc_signal.append(signal_values[-1])
    
    def _calculate_stochastic_single(self, data: List[float], period: int) -> Optional[float]:
        """
        计算单个随机指标值
        
        参数:
            data: 价格数据
            period: 周期
            
        返回:
            随机值
        """
        if len(data) < period:
            return None
        
        window = data[-period:]
        highest = np.max(window)
        lowest = np.min(window)
        
        if highest == lowest:
            return 50.0
        else:
            return 100 * (data[-1] - lowest) / (highest - lowest)
    
    def get_stc(self) -> Tuple[Optional[float], Optional[float]]:
        """
        获取当前STC值
        
        返回:
            (STC值, 信号线值)
        """
        if len(self.stc_final) >= 2 and len(self.stc_signal) >= 1:
            return self.stc_final[-1], self.stc_signal[-1]
        return None, None
    
    def is_ready(self) -> bool:
        """
        检查指标是否就绪
        
        返回:
            是否就绪
        """
        return len(self.stc_final) > 2 and len(self.stc_signal) > 1


class CandlePatternRecognizer:
    """K线形态识别器（优化版）"""
    def __init__(self, window_size: int = 10):
        """
        初始化K线形态识别器
        参数:
            window_size: 窗口大小
        """
        self.window_size = window_size
        self.kline_buffer = deque(maxlen=window_size)
        self.atr = deque(maxlen=14)
        self.avg_volume = deque(maxlen=30)

    def update(self, kline: KLineData) -> None:
        """
        更新K线数据，并维护ATR和平均成交量
        参数:
            kline: K线数据
        """
        self.kline_buffer.append({
            'open': kline.open,
            'high': kline.high,
            'low': kline.low,
            'close': kline.close,
            'volume': kline.volume
        })
        # 维护ATR
        if len(self.kline_buffer) >= 2:
            prev = self.kline_buffer[-2]
            tr = max(
                self.kline_buffer[-1]['high'] - self.kline_buffer[-1]['low'],
                abs(self.kline_buffer[-1]['high'] - prev['close']),
                abs(self.kline_buffer[-1]['low'] - prev['close'])
            )
            self.atr.append(tr)
        # 维护平均成交量
        self.avg_volume.append(kline.volume)

    def recognize_patterns(self) -> Dict[str, bool]:
        """
        识别关键K线形态（含动态阈值和成交量确认）
        返回:
            形态识别结果字典
        """
        if len(self.kline_buffer) < 3:
            return {}
        features = {}
        current = self.kline_buffer[-1]
        prev = self.kline_buffer[-2]
        prev2 = self.kline_buffer[-3]
        # 动态因子
        close_price = current['close']
        atr = self.atr[-1] if self.atr else 0.01
        avg_vol = sum(self.avg_volume)/len(self.avg_volume) if self.avg_volume else 1
        dynamic_factor = 0.7 * (atr / close_price) if close_price else 0.01
        # 单K线形态
        features['hammer'] = self._is_hammer(current, dynamic_factor, avg_vol)
        features['shooting_star'] = self._is_shooting_star(current, dynamic_factor, avg_vol)
        features['marubozu'] = self._is_marubozu(current, dynamic_factor, avg_vol)
        # 双K线形态
        features['bullish_engulfing'] = self._is_bullish_engulfing(prev, current, dynamic_factor, avg_vol)
        features['bearish_engulfing'] = self._is_bearish_engulfing(prev, current, dynamic_factor, avg_vol)
        features['pregnant'] = self._is_pregnant(prev, current, dynamic_factor, avg_vol)
        features['piercing'] = self._is_piercing(prev, current, dynamic_factor, avg_vol)
        features['dark_cloud'] = self._is_dark_cloud(prev, current, dynamic_factor, avg_vol)
        # 三K线形态
        features['three_white_soldiers'] = self._is_three_white_soldiers(prev2, prev, current, dynamic_factor, avg_vol)
        features['three_black_crows'] = self._is_three_black_crows(prev2, prev, current, dynamic_factor, avg_vol)
        features['morning_star'] = self._is_morning_star(prev2, prev, current, dynamic_factor, avg_vol)
        features['evening_star'] = self._is_evening_star(prev2, prev, current, dynamic_factor, avg_vol)
        # 缺口识别
        features['gap_up'] = self._is_gap_up(prev, current, dynamic_factor, avg_vol)
        features['gap_down'] = self._is_gap_down(prev, current, dynamic_factor, avg_vol)
        return features

    def _is_hammer(self, candle: Dict, dynamic_factor, avg_vol) -> bool:
        body = abs(candle['close'] - candle['open'])
        total_range = candle['high'] - candle['low']
        lower_shadow = min(candle['open'], candle['close']) - candle['low']
        upper_shadow = candle['high'] - max(candle['open'], candle['close'])
        if total_range == 0 or body == 0:
            return False
        return (
            lower_shadow >= 2 * body * dynamic_factor and
            upper_shadow <= max(body * 0.5, total_range * 0.1) * dynamic_factor and
            body / total_range >= 0.05 * dynamic_factor and
            candle['volume'] > avg_vol * 0.8
        )

    def _is_shooting_star(self, candle: Dict, dynamic_factor, avg_vol) -> bool:
        body = abs(candle['close'] - candle['open'])
        total_range = candle['high'] - candle['low']
        upper_shadow = candle['high'] - max(candle['open'], candle['close'])
        return (
            body > 0 and
            upper_shadow >= 2 * body * dynamic_factor and
            (min(candle['open'], candle['close']) - candle['low']) <= body * 0.3 * dynamic_factor and
            candle['volume'] > avg_vol * 0.8
        )

    def _is_marubozu(self, candle: Dict, dynamic_factor, avg_vol) -> bool:
        body = abs(candle['close'] - candle['open'])
        total_range = candle['high'] - candle['low']
        return (
            body > 0 and
            (body / total_range) > 0.9 * dynamic_factor and
            candle['volume'] > avg_vol * 0.8
        )

    def _is_bullish_engulfing(self, prev: Dict, current: Dict, dynamic_factor, avg_vol) -> bool:
        prev_is_bearish = prev['close'] < prev['open']
        current_is_bullish = current['close'] > current['open']
        engulfs = (current['open'] <= prev['close'] and current['close'] >= prev['open'])
        tolerance = abs(prev['close'] - prev['open']) * 0.1 * dynamic_factor
        engulfs_tol = (current['open'] <= prev['close'] + tolerance and current['close'] >= prev['open'] - tolerance)
        return (
            prev_is_bearish and current_is_bullish and
            (engulfs or engulfs_tol) and
            current['volume'] > avg_vol * 0.8
        )

    def _is_bearish_engulfing(self, prev: Dict, current: Dict, dynamic_factor, avg_vol) -> bool:
        prev_is_bullish = prev['close'] > prev['open']
        current_is_bearish = current['close'] < current['open']
        engulfs = (current['open'] >= prev['close'] and current['close'] <= prev['open'])
        tolerance = abs(prev['close'] - prev['open']) * 0.1 * dynamic_factor
        engulfs_tol = (current['open'] >= prev['close'] - tolerance and current['close'] <= prev['open'] + tolerance)
        return (
            prev_is_bullish and current_is_bearish and
            (engulfs or engulfs_tol) and
            current['volume'] > avg_vol * 0.8
        )

    def _is_three_white_soldiers(self, prev2: Dict, prev: Dict, current: Dict, dynamic_factor, avg_vol) -> bool:
        return (
            prev2['close'] > prev2['open'] and
            prev['close'] > prev['open'] and
            current['close'] > current['open'] and
            prev['open'] > prev2['close'] and
            current['open'] > prev['close'] and
            current['volume'] > avg_vol * 0.8
        )

    def _is_three_black_crows(self, prev2: Dict, prev: Dict, current: Dict, dynamic_factor, avg_vol) -> bool:
        return (
            prev2['close'] < prev2['open'] and
            prev['close'] < prev['open'] and
            current['close'] < current['open'] and
            prev['open'] < prev2['close'] and
            current['open'] < prev['close'] and
            current['volume'] > avg_vol * 0.8
        )

    def _is_gap_up(self, prev: Dict, current: Dict, dynamic_factor, avg_vol) -> bool:
        gap_size = current['low'] - prev['high']
        if gap_size <= 0:
            return False
        avg_price = (prev['high'] + prev['low']) / 2
        relative_gap = gap_size / avg_price if avg_price > 0 else 0
        return relative_gap > 0.001 * dynamic_factor and current['volume'] > avg_vol * 0.8

    def _is_gap_down(self, prev: Dict, current: Dict, dynamic_factor, avg_vol) -> bool:
        gap_size = prev['low'] - current['high']
        if gap_size <= 0:
            return False
        avg_price = (prev['high'] + prev['low']) / 2
        relative_gap = gap_size / avg_price if avg_price > 0 else 0
        return relative_gap > 0.001 * dynamic_factor and current['volume'] > avg_vol * 0.8

    # 新增形态
    def _is_pregnant(self, prev: Dict, current: Dict, dynamic_factor, avg_vol) -> bool:
        # 孕线：当前K线实体完全包在前一K线实体内
        prev_body = abs(prev['close'] - prev['open'])
        curr_body = abs(current['close'] - current['open'])
        return (
            curr_body < prev_body * 0.7 * dynamic_factor and
            min(current['open'], current['close']) > min(prev['open'], prev['close']) and
            max(current['open'], current['close']) < max(prev['open'], prev['close']) and
            current['volume'] > avg_vol * 0.8
        )

    def _is_piercing(self, prev: Dict, current: Dict, dynamic_factor, avg_vol) -> bool:
        # 刺透形态：前阴后阳，阳线收盘高于前阴线实体中点
        prev_is_bearish = prev['close'] < prev['open']
        curr_is_bullish = current['close'] > current['open']
        mid = (prev['open'] + prev['close']) / 2
        return (
            prev_is_bearish and curr_is_bullish and
            current['open'] < prev['close'] and
            current['close'] > mid and
            current['volume'] > avg_vol * 0.8
        )

    def _is_dark_cloud(self, prev: Dict, current: Dict, dynamic_factor, avg_vol) -> bool:
        # 乌云盖顶：前阳后阴，阴线收盘低于前阳线实体中点
        prev_is_bullish = prev['close'] > prev['open']
        curr_is_bearish = current['close'] < current['open']
        mid = (prev['open'] + prev['close']) / 2
        return (
            prev_is_bullish and curr_is_bearish and
            current['open'] > prev['close'] and
            current['close'] < mid and
            current['volume'] > avg_vol * 0.8
        )

    def _is_morning_star(self, prev2: Dict, prev: Dict, current: Dict, dynamic_factor, avg_vol) -> bool:
        # 晨星：前阴、中小实体、后阳，且中间K线跳空
        prev_is_bearish = prev2['close'] < prev2['open']
        curr_is_bullish = current['close'] > current['open']
        small_body = abs(prev['close'] - prev['open']) < abs(prev2['close'] - prev2['open']) * 0.5 * dynamic_factor
        gap = prev['low'] > prev2['close']
        return (
            prev_is_bearish and small_body and gap and curr_is_bullish and current['close'] > prev['close'] and current['volume'] > avg_vol * 0.8
        )

    def _is_evening_star(self, prev2: Dict, prev: Dict, current: Dict, dynamic_factor, avg_vol) -> bool:
        # 暮星：前阳、中小实体、后阴，且中间K线跳空
        prev_is_bullish = prev2['close'] > prev2['open']
        curr_is_bearish = current['close'] < current['open']
        small_body = abs(prev['close'] - prev['open']) < abs(prev2['close'] - prev2['open']) * 0.5 * dynamic_factor
        gap = prev['high'] < prev2['close']
        return (
            prev_is_bullish and small_body and gap and curr_is_bearish and current['close'] < prev['close'] and current['volume'] > avg_vol * 0.8
        )

    def is_ready(self) -> bool:
        return len(self.kline_buffer) >= 3


class WavePatternRecognizer:
    """波段识别器（优化版）"""
    def __init__(self, lookback_period: int = 50):
        """
        初始化波段识别器
        参数:
            lookback_period: 回溯周期
        """
        self.lookback_period = lookback_period
        self.price_history = deque(maxlen=lookback_period)
        self.hull_history = deque(maxlen=lookback_period)
        self.stc_history = deque(maxlen=lookback_period)
        self.swing_points = deque(maxlen=20)  # 存储最近的20个摆动点
        self.confirmed_swings = deque(maxlen=5)
        self.current_wave = {
            'type': 'unknown',
            'start_price': 0,
            'start_time': None,
            'duration': 0,
            'strength': 0,
            'swing_count': 0
        }
        self.waves = deque(maxlen=10)
        self.min_swing_size = 0
        self._last_atr = 0
        self._last_volatility = 0
        self._last_market_regime = 'unknown'

    def update(self, price: float, hull_value: float, stc_value: float, timestamp=None, atr: float = None, volatility: float = None, market_regime: str = None) -> None:
        """
        更新波段识别器，支持动态参数
        参数:
            price: 价格
            hull_value: HULL值
            stc_value: STC值
            timestamp: 时间戳
            atr: 最新ATR
            volatility: 市场波动率
            market_regime: 市场状态
        """
        self.price_history.append(price)
        self.hull_history.append(hull_value)
        self.stc_history.append(stc_value)
        # 记录ATR和波动率
        if atr is not None:
            self._last_atr = atr
        if volatility is not None:
            self._last_volatility = volatility
        if market_regime is not None:
            self._last_market_regime = market_regime
        # 设置最小摆动幅度
        self.min_swing_size = 0.5 * self._last_atr if self._last_atr else 0.01
        # 至少需要10个数据点才能开始识别
        if len(self.price_history) < 10:
            return
        self._identify_swing_points()
        self._identify_current_wave(timestamp)

    def _identify_swing_points(self) -> None:
        if len(self.price_history) < 5:
            return
        prices = list(self.price_history)
        idx = -3
        # 局部高点
        if prices[idx] > prices[idx-1] and prices[idx] > prices[idx-2] and prices[idx] > prices[idx+1] and prices[idx] > prices[idx+2]:
            price_diff = abs(prices[idx] - prices[idx-2])
            # 幅度过滤
            if price_diff >= self.min_swing_size:
                # 时间确认机制：连续2根K线确认
                if not self.swing_points or self.swing_points[-1]['type'] != 'high':
                    self.swing_points.append({'type': 'high', 'price': prices[idx], 'index': len(prices) + idx, 'confirmed': False})
                    # 二次确认
                    if len(self.swing_points) >= 2 and self.swing_points[-2]['type'] == 'high':
                        self.swing_points[-1]['confirmed'] = True
                        self.confirmed_swings.append(self.swing_points[-1])
        # 局部低点
        elif prices[idx] < prices[idx-1] and prices[idx] < prices[idx-2] and prices[idx] < prices[idx+1] and prices[idx] < prices[idx+2]:
            price_diff = abs(prices[idx] - prices[idx-2])
            if price_diff >= self.min_swing_size:
                if not self.swing_points or self.swing_points[-1]['type'] != 'low':
                    self.swing_points.append({'type': 'low', 'price': prices[idx], 'index': len(prices) + idx, 'confirmed': False})
                    if len(self.swing_points) >= 2 and self.swing_points[-2]['type'] == 'low':
                        self.swing_points[-1]['confirmed'] = True
                        self.confirmed_swings.append(self.swing_points[-1])

    def _identify_current_wave(self, timestamp) -> None:
        swings = list(self.swing_points)[-4:]
        if len(swings) >= 2:
            last_swing = swings[-1]
            prev_swing = swings[-2]
            # 趋势强度：基于Hull斜率
            if len(self.hull_history) >= 4:
                hulls = list(self.hull_history)[-4:]
                hull_slope = (hulls[-1] - hulls[0]) / 3 if hulls[0] != 0 else 0
            else:
                hull_slope = 0
            # 波动率过滤：高波动时要求2倍强度
            strength = abs(last_swing['price'] - prev_swing['price']) / prev_swing['price'] if prev_swing['price'] != 0 else 0
            if self._last_volatility > 0.03:
                strength = strength if abs(hull_slope) > 2 * self._last_volatility else 0
            # 市场状态整合：不同市态下参数可调
            if self._last_market_regime in ['trending', 'volatile_trending']:
                min_strength = 0.01
            else:
                min_strength = 0.02
            # 波段类型
            if last_swing['type'] == 'high' and prev_swing['type'] == 'low' and strength > min_strength:
                wave_type = 'uptrend'
            elif last_swing['type'] == 'low' and prev_swing['type'] == 'high' and strength > min_strength:
                wave_type = 'downtrend'
            else:
                wave_type = 'consolidation'
            if wave_type != self.current_wave['type']:
                if self.current_wave['type'] != 'unknown':
                    self.waves.append(self.current_wave.copy())
                self.current_wave = {
                    'type': wave_type,
                    'start_price': prev_swing['price'],
                    'start_time': timestamp,
                    'duration': last_swing['index'] - prev_swing['index'],
                    'strength': strength,
                    'swing_count': 1
                }
            else:
                self.current_wave['duration'] = last_swing['index'] - prev_swing['index'] + self.current_wave['duration']
                self.current_wave['strength'] = (self.current_wave['strength'] + strength) / 2
                self.current_wave['swing_count'] += 1

    def get_wave_info(self) -> Dict:
        return {
            'type': self.current_wave['type'],
            'strength': self.current_wave['strength'],
            'duration': self.current_wave['duration'],
            'swing_count': self.current_wave['swing_count']
        }

    def is_ready(self) -> bool:
        return len(self.swing_points) >= 2


#==================== 自适应系统组件 ====================

class MarketRegimeClassifier:
    """市场状态分类器"""
    def __init__(self, lookback_period: int = 50):
        """
        初始化市场状态分类器
        
        参数:
            lookback_period: 回溯周期
        """
        self.lookback_period = lookback_period
        self.price_history = deque(maxlen=lookback_period)
        self.volume_history = deque(maxlen=lookback_period)
        self.regime_history = deque(maxlen=10)  # 存储最近的市场状态
        self.volatility_window = deque(maxlen=20)  # 波动率计算窗口
        
    def update(self, price: float, volume: float = 0) -> None:
        """
        更新市场数据
        
        参数:
            price: 价格
            volume: 成交量
        """
        self.price_history.append(price)
        self.volume_history.append(volume)
        
        # 计算最新波动率
        if len(self.price_history) >= 2:
            latest_return = abs(self.price_history[-1] / self.price_history[-2] - 1)
            self.volatility_window.append(latest_return)
    
    def classify_regime(self) -> Tuple[str, float, float]:
        """
        分类市场状态
        
        返回:
            (市场状态, 波动率水平, 趋势强度)
        """
        if len(self.price_history) < 20:
            return "unknown", 0.0, 0.0
        
        # 计算波动率
        volatility = np.std(list(self.volatility_window)) * np.sqrt(252) if self.volatility_window else 0.0
        
        # 计算趋势强度
        prices = np.array(list(self.price_history))
        x = np.arange(len(prices))
        if len(x) > 1:
            slope, _ = np.polyfit(x, prices, 1)
            trend_strength = abs(slope) / np.mean(prices) * 100  # 归一化趋势强度
        else:
            trend_strength = 0.0
        
        # 分类市场状态
        if volatility > 0.03:  # 高波动
            if trend_strength > 0.5:
                regime = "volatile_trending"
            else:
                regime = "volatile_ranging"
        else:  # 低波动
            if trend_strength > 0.3:
                regime = "trending"
            else:
                regime = "ranging"
        
        # 更新市场状态历史
        self.regime_history.append(regime)
        
        return regime, volatility, trend_strength
    
    def get_regime_stability(self) -> float:
        """
        获取市场状态稳定性
        
        返回:
            稳定性得分 (0-1)
        """
        if len(self.regime_history) < 5:
            return 0.5
        
        # 计算最近状态的一致性
        regimes = list(self.regime_history)
        most_common = max(set(regimes), key=regimes.count)
        stability = regimes.count(most_common) / len(regimes)
        
        return stability


class AdaptiveWeightAllocator:
    """自适应权重分配器"""
    def __init__(self, adaptation_speed: float = 0.1):
        """
        初始化自适应权重分配器
        
        参数:
            adaptation_speed: 适应速度 (0-1)
        """
        self.adaptation_speed = adaptation_speed
        self.base_weights = {
            'technical': 0.25, 
            'pattern': 0.25, 
            'wave': 0.25, 
            'fuzzy': 0.25
        }
        
        # 不同市场状态下的权重配置
        self.regime_specific_weights = {
            'trending': {'wave': 0.4, 'technical': 0.3, 'pattern': 0.2, 'fuzzy': 0.1},
            'ranging': {'pattern': 0.4, 'fuzzy': 0.3, 'technical': 0.2, 'wave': 0.1},
            'volatile_trending': {'technical': 0.4, 'fuzzy': 0.3, 'wave': 0.2, 'pattern': 0.1},
            'volatile_ranging': {'fuzzy': 0.4, 'pattern': 0.3, 'technical': 0.2, 'wave': 0.1},
            'unknown': self.base_weights
        }
        
        self.current_weights = self.base_weights.copy()
        self.performance_history = {
            'technical': deque(maxlen=50),
            'pattern': deque(maxlen=50),
            'wave': deque(maxlen=50),
            'fuzzy': deque(maxlen=50)
        }
    
    def update_weights(self, market_regime: str, module_performance: Dict[str, float]) -> Dict[str, float]:
        """
        更新权重
        
        参数:
            market_regime: 市场状态
            module_performance: 各模块性能
            
        返回:
            更新后的权重
        """
        # 获取市场状态特定权重
        target_weights = self.regime_specific_weights.get(market_regime, self.base_weights).copy()
        
        # 更新性能历史
        for module, performance in module_performance.items():
            if module in self.performance_history:
                self.performance_history[module].append(performance)
        
        # 基于性能调整权重
        performance_adjustments = {}
        total_performance = sum(module_performance.values())
        
        if total_performance > 0:
            for module, performance in module_performance.items():
                # 性能占比作为权重调整因子
                performance_adjustments[module] = performance / total_performance
        else:
            # 如果没有性能数据，使用均匀分布
            for module in module_performance:
                performance_adjustments[module] = 1.0 / len(module_performance)
        
        # 应用性能调整
        adjusted_weights = {}
        for module in target_weights:
            if module in performance_adjustments:
                # 混合市场状态权重和性能权重
                adjusted_weights[module] = (
                    target_weights[module] * 0.7 + 
                    performance_adjustments[module] * 0.3
                )
            else:
                adjusted_weights[module] = target_weights[module]
        
        # 归一化权重
        total_weight = sum(adjusted_weights.values())
        if total_weight > 0:
            for module in adjusted_weights:
                adjusted_weights[module] /= total_weight
        
        # 平滑过渡到新权重
        for module in self.current_weights:
            if module in adjusted_weights:
                self.current_weights[module] = (
                    (1 - self.adaptation_speed) * self.current_weights[module] + 
                    self.adaptation_speed * adjusted_weights[module]
                )
        
        return self.current_weights
    
    def get_current_weights(self) -> Dict[str, float]:
        """
        获取当前权重
        
        返回:
            当前权重
        """
        return self.current_weights


class SignalFusionEngine:
    """信号融合引擎"""
    def __init__(self):
        """初始化信号融合引擎"""
        self.signal_history = {
            'technical': deque(maxlen=10),
            'pattern': deque(maxlen=10),
            'wave': deque(maxlen=10),
            'fuzzy': deque(maxlen=10)
        }
        self.signal_decay_factor = 0.9  # 信号衰减因子
        self.consistency_threshold = 0.7  # 一致性阈值
    
    def fuse_signals(self, current_signals: Dict[str, float], weights: Dict[str, float]) -> Tuple[float, float]:
        """
        融合信号
        
        参数:
            current_signals: 当前各模块信号
            weights: 各模块权重
            
        返回:
            (综合信号强度, 置信度)
        """
        # 更新信号历史
        for module, signal in current_signals.items():
            if module in self.signal_history:
                self.signal_history[module].append(signal)
        
        # 计算信号一致性
        signal_directions = {}
        for module, signal in current_signals.items():
            signal_directions[module] = 1 if signal > 0 else (-1 if signal < 0 else 0)
        
        # 检查信号方向一致性
        directions = list(signal_directions.values())
        if not directions:
            return 0.0, 0.0
            
        positive_count = directions.count(1)
        negative_count = directions.count(-1)
        neutral_count = directions.count(0)
        
        total_count = len(directions)
        if total_count == 0:
            return 0.0, 0.0
            
        # 计算一致性得分
        if positive_count > negative_count:
            consistency = positive_count / total_count
            dominant_direction = 1
        elif negative_count > positive_count:
            consistency = negative_count / total_count
            dominant_direction = -1
        else:
            consistency = neutral_count / total_count
            dominant_direction = 0
        
        # 应用权重融合信号
        weighted_signal = 0.0
        total_weight = 0.0
        
        for module, signal in current_signals.items():
            if module in weights:
                module_weight = weights[module]
                # 如果信号方向与主导方向一致，增强权重
                if signal_directions[module] == dominant_direction:
                    module_weight *= 1.2
                
                weighted_signal += signal * module_weight
                total_weight += module_weight
        
        if total_weight > 0:
            final_signal = weighted_signal / total_weight
        else:
            final_signal = 0.0
        
        # 计算置信度
        confidence = consistency * min(1.0, total_weight)
        
        return final_signal, confidence
    
    def handle_conflicts(self, signals: Dict[str, float], performance: Dict[str, float]) -> Dict[str, float]:
        """
        处理信号冲突
        
        参数:
            signals: 各模块信号
            performance: 各模块性能
            
        返回:
            调整后的信号
        """
        # 检测信号冲突
        signal_directions = {}
        for module, signal in signals.items():
            signal_directions[module] = 1 if signal > 0 else (-1 if signal < 0 else 0)
        
        # 计算方向分布
        direction_counts = {1: 0, -1: 0, 0: 0}
        for direction in signal_directions.values():
            direction_counts[direction] += 1
        
        # 如果没有明显冲突，返回原始信号
        max_count = max(direction_counts.values())
        if max_count >= len(signals) * 0.7:  # 70%一致
            return signals
        
        # 处理冲突 - 基于历史表现调整权重
        adjusted_signals = {}
        for module, signal in signals.items():
            if module in performance:
                # 根据模块性能调整信号强度
                performance_factor = performance.get(module, 0.5)
                adjusted_signals[module] = signal * performance_factor
            else:
                adjusted_signals[module] = signal * 0.5  # 默认减半
        
        return adjusted_signals
    
    def calculate_signal_strength(self, raw_signal: float, confidence: float) -> float:
        """
        计算最终信号强度
        
        参数:
            raw_signal: 原始信号
            confidence: 置信度
            
        返回:
            调整后的信号强度
        """
        # 应用置信度调整信号强度
        adjusted_signal = raw_signal * confidence
        
        # 信号强度限制在[-1, 1]范围内
        return max(-1.0, min(1.0, adjusted_signal))


class PerformanceTracker:
    """性能跟踪器"""
    def __init__(self, memory_length: int = 100):
        """
        初始化性能跟踪器
        
        参数:
            memory_length: 记忆长度
        """
        self.memory_length = memory_length
        self.trade_history = deque(maxlen=memory_length)
        self.module_performance = {
            'technical': {'correct': 0, 'total': 0},
            'pattern': {'correct': 0, 'total': 0},
            'wave': {'correct': 0, 'total': 0},
            'fuzzy': {'correct': 0, 'total': 0}
        }
        self.market_performance = {
            'trending': {'correct': 0, 'total': 0},
            'ranging': {'correct': 0, 'total': 0},
            'volatile_trending': {'correct': 0, 'total': 0},
            'volatile_ranging': {'correct': 0, 'total': 0}
        }
    
    def record_trade(self, trade_result: float, signals: Dict[str, float], weights: Dict[str, float], market_regime: str) -> None:
        """
        记录交易结果
        
        参数:
            trade_result: 交易结果 (正值为盈利，负值为亏损)
            signals: 各模块信号
            weights: 各模块权重
            market_regime: 市场状态
        """
        # 记录交易
        self.trade_history.append({
            'result': trade_result,
            'signals': signals.copy(),
            'weights': weights.copy(),
            'market_regime': market_regime,
            'timestamp': time.time()
        })
        
        # 更新各模块表现
        is_profitable = trade_result > 0
        
        for module, signal in signals.items():
            if module in self.module_performance:
                # 信号方向与结果一致视为正确
                signal_correct = (signal > 0 and trade_result > 0) or (signal < 0 and trade_result < 0)
                
                self.module_performance[module]['total'] += 1
                if signal_correct:
                    self.module_performance[module]['correct'] += 1
        
        # 更新市场状态表现
        if market_regime in self.market_performance:
            self.market_performance[market_regime]['total'] += 1
            if is_profitable:
                self.market_performance[market_regime]['correct'] += 1
    
    def evaluate_module_performance(self) -> Dict[str, float]:
        """
        评估各模块表现
        
        返回:
            各模块准确率
        """
        performance = {}
        
        for module, stats in self.module_performance.items():
            if stats['total'] > 0:
                performance[module] = stats['correct'] / stats['total']
            else:
                performance[module] = 0.5  # 默认值
        
        return performance
    
    def evaluate_market_performance(self) -> Dict[str, float]:
        """
        评估各市场状态表现
        
        返回:
            各市场状态准确率
        """
        performance = {}
        
        for regime, stats in self.market_performance.items():
            if stats['total'] > 0:
                performance[regime] = stats['correct'] / stats['total']
            else:
                performance[regime] = 0.5  # 默认值
        
        return performance
    
    def get_optimization_suggestions(self) -> Dict[str, Any]:
        """
        获取优化建议
        
        返回:
            优化建议
        """
        module_performance = self.evaluate_module_performance()
        market_performance = self.evaluate_market_performance()
        
        # 找出表现最好和最差的模块
        best_module = max(module_performance.items(), key=lambda x: x[1])[0] if module_performance else None
        worst_module = min(module_performance.items(), key=lambda x: x[1])[0] if module_performance else None
        
        # 找出表现最好和最差的市场状态
        best_regime = max(market_performance.items(), key=lambda x: x[1])[0] if market_performance else None
        worst_regime = min(market_performance.items(), key=lambda x: x[1])[0] if market_performance else None
        
        suggestions = {
            'best_module': best_module,
            'worst_module': worst_module,
            'best_regime': best_regime,
            'worst_regime': worst_regime,
            'module_performance': module_performance,
            'market_performance': market_performance
        }
        
        return suggestions# =====
#=========== 主策略类 ==================

class Strategy0(BaseStrategy):
    """Strategy3 - 高级交易策略 (增强版)"""
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        """参数表"""

        self.state_map = State()
        """状态表"""

        # 信号标志
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False

        # 当前行情
        self.tick = None
        self.order_id = None
        self.signal_price = 0
        
        # 技术指标
        self.hull_calculators = {
            9: HullMovingAverage(9),
            21: HullMovingAverage(21),
            55: HullMovingAverage(55)
        }
        self.stc_calculator = None
        self.pattern_recognizer = None
        self.wave_recognizer = None
        self.fuzzy_system = None
        
        # 自适应系统组件
        self.market_classifier = None
        self.weight_allocator = None
        self.signal_fusion = None
        self.performance_tracker = None
        
        # 历史数据
        self.hull_prev = 0
        self.stc_prev = 0
        
        # 交易记录
        self.last_trade_price = 0
        self.last_trade_time = 0
        self.trade_count = 0

        # 新增：River模型与特征缩放器
        self.river_scaler = preprocessing.StandardScaler()
        self.river_model = tree.HoeffdingTreeClassifier()
        self.selector = feature_selection.VarianceThreshold(threshold=0.01)
        self.river_pipeline = self.selector | self.river_scaler | self.river_model
        self._last_features = None
        self._last_label = None

    @property
    def main_indicator_data(self) -> Dict[str, float]:
        """主图指标，输出多周期HULL"""
        data = {}
        for period, hull in self.hull_calculators.items():
            value, _ = hull.get_hull()
            data[f"HULL{period}"] = value if value is not None else 0.0
        data["STC"] = self.state_map.stc_value
        data["STC_Signal"] = self.state_map.stc_signal
        return data

    @property
    def sub_indicator_data(self) -> Dict[str, float]:
        """副图指标"""
        return {
            "Confidence": self.state_map.signal_confidence,
            "WaveStrength": self.state_map.wave_strength,
            "PatternStrength": self.state_map.pattern_strength,
            "IntegratedSignal": self.state_map.integrated_signal
        }

    def on_tick(self, tick: TickData) -> None:
        """
        Tick数据回调
        
        参数:
            tick: Tick数据
        """
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)

    def on_order_cancel(self, order: OrderData) -> None:
        """
        撤单推送回调
        
        参数:
            order: 订单数据
        """
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """
        成交回调
        
        参数:
            trade: 成交数据
            log: 是否记录日志
        """
        super().on_trade(trade, log)
        self.order_id = None
        
        # 记录交易
        trade_result = 0.0
        if self.last_trade_price > 0:
            if trade.direction == "buy":
                trade_result = self.last_trade_price - trade.price
            else:
                trade_result = trade.price - self.last_trade_price
        
        self.last_trade_price = trade.price
        self.last_trade_time = time.time()
        self.trade_count += 1
        
        # 记录性能
        if self.performance_tracker and trade_result != 0:
            # 获取当前模块信号
            signals = {
                'technical': 1.0 if self.hull_calculators[9].get_trend() == "BULLISH" else -1.0,
                'pattern': self.state_map.pattern_strength if self.state_map.pattern_detected in ['bullish_engulfing', 'hammer'] else -self.state_map.pattern_strength,
                'wave': 1.0 if self.state_map.wave_type == 'uptrend' else -1.0,
                'fuzzy': 1.0 if self.state_map.action_level == "Aggressive" else (-1.0 if self.state_map.action_level == "Stop" else 0.0)
            }
            
            # 记录交易结果
            self.performance_tracker.record_trade(
                trade_result,
                signals,
                self.state_map.adaptive_weights,
                self.state_map.market_regime
            )
            
            # 更新模块性能
            module_performance = self.performance_tracker.evaluate_module_performance()
            self.state_map.technical_accuracy = module_performance.get('technical', 0.5)
            self.state_map.pattern_accuracy = module_performance.get('pattern', 0.5)
            self.state_map.wave_accuracy = module_performance.get('wave', 0.5)
            self.state_map.fuzzy_accuracy = module_performance.get('fuzzy', 0.5)

        # 新增：用成交结果训练river模型
        if self._last_features is not None and trade_result != 0:
            label = 1 if trade_result > 0 else 0
            try:
                self.river_pipeline = self.river_pipeline.learn_one(self._last_features, label)
            except Exception:
                pass

    def on_start(self) -> None:
        """策略启动"""
        # 先初始化技术指标和自适应系统组件
        self.hull_calculators = {
            9: HullMovingAverage(9),
            21: HullMovingAverage(21),
            55: HullMovingAverage(55)
        }
        self.stc_calculator = SchaffTrendCycle(
            self.params_map.stc_fast,
            self.params_map.stc_slow,
            self.params_map.stc_cycle
        )
        self.pattern_recognizer = CandlePatternRecognizer()
        self.wave_recognizer = WavePatternRecognizer()
        self.fuzzy_system = FuzzySystem(self.params_map.fuzzy_sensitivity)
        self.market_classifier = MarketRegimeClassifier()
        self.weight_allocator = AdaptiveWeightAllocator(self.params_map.adaptation_speed)
        self.signal_fusion = SignalFusionEngine()
        self.performance_tracker = PerformanceTracker()

        # 初始化K线生成器
        self.kline_generator = KLineGenerator(
            callback=self.on_kline,
            real_time_callback=self.on_realtime_kline,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()

        super().on_start()

        # 重置信号
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False
        self.tick = None
        
        # 初始化自适应权重
        self.state_map.adaptive_weights = {
            'technical': self.params_map.technical_weight,
            'pattern': self.params_map.pattern_weight,
            'wave': self.params_map.wave_weight,
            'fuzzy': self.params_map.fuzzy_weight
        }

        self.update_status_bar()
        print("自适应综合策略已启动")

        # River模型重置
        self.river_scaler = preprocessing.StandardScaler()
        self.river_model = tree.HoeffdingTreeClassifier()
        self.selector = feature_selection.VarianceThreshold(threshold=0.01)
        self.river_pipeline = self.selector | self.river_scaler | self.river_model
        self._last_features = None
        self._last_label = None

        # 多周期HULL重置
        self.hull_calculators = {
            9: HullMovingAverage(9),
            21: HullMovingAverage(21),
            55: HullMovingAverage(55)
        }

    def on_stop(self) -> None:
        """策略停止"""
        super().on_stop()
        print("自适应综合策略已停止")
        
        # 输出性能统计
        if self.performance_tracker:
            suggestions = self.performance_tracker.get_optimization_suggestions()
            print(f"策略优化建议:")
            print(f"- 最佳模块: {suggestions['best_module']}")
            print(f"- 最差模块: {suggestions['worst_module']}")
            print(f"- 最佳市场状态: {suggestions['best_regime']}")
            print(f"- 最差市场状态: {suggestions['worst_regime']}")
            print(f"- 模块性能: {suggestions['module_performance']}")

    def on_kline(self, kline: KLineData) -> None:
        """标准K线回调，整合指标与信号处理"""
        self.calculate_indicators(kline)
        self.calc_signal(kline)
        self.execute_trading_signals()
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })
        if self.trading:
            self.update_status_bar()

    def on_realtime_kline(self, kline: KLineData) -> None:
        """实时K线回调，指标与图表更新"""
        self.calculate_indicators(kline)
        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })
        self.update_status_bar()

    def calculate_indicators(self, kline: KLineData) -> None:
        """统一处理所有指标与市场状态更新"""
        self.update_indicators(kline)
        self.update_market_state()

    def execute_trading_signals(self) -> None:
        """执行交易信号，包含风控检查"""
        if not self.trading:
            return
        # 可扩展风控检查，如最大仓位等
        self.exec_signal()

    def calc_signal(self, kline: KLineData) -> None:
        """
        计算交易信号
        参数:
            kline: K线数据
        """
        # 检查指标是否就绪
        if not self.hull_calculators[9].is_ready() or not self.stc_calculator.is_ready():
            return
        # 生成各模块信号
        module_signals = self._generate_module_signals()
        # 处理信号冲突
        module_performance = {
            'technical': self.state_map.technical_accuracy,
            'pattern': self.state_map.pattern_accuracy,
            'wave': self.state_map.wave_accuracy,
            'fuzzy': self.state_map.fuzzy_accuracy
        }
        adjusted_signals = self.signal_fusion.handle_conflicts(module_signals, module_performance)
        # 融合信号
        integrated_signal, confidence = self.signal_fusion.fuse_signals(
            adjusted_signals,
            self.state_map.adaptive_weights
        )
        # 计算最终信号强度
        final_signal = self.signal_fusion.calculate_signal_strength(integrated_signal, confidence)
        # River模型预测加异常处理
        river_proba = 0.5
        if self._last_features is not None:
            try:
                proba = self.river_pipeline.predict_proba_one(self._last_features)
                river_proba = proba.get(1, 0.5)
            except Exception as e:
                self.log_error(f"River预测异常: {str(e)}")
                river_proba = 0.5
        self.state_map.integrated_signal = 0.5 * final_signal + 0.5 * (2 * river_proba - 1)
        self.state_map.signal_confidence = max(confidence, abs(river_proba - 0.5) * 2)
        self.buy_signal = river_proba > 0.6
        self.sell_signal = river_proba < 0.4
        if self.params_map.trade_direction == "buy":
            self.short_signal = self.sell_signal
            self.cover_signal = self.buy_signal
        else:
            self.short_signal = self.buy_signal
            self.cover_signal = self.sell_signal
        self.long_price = self.short_price = kline.close
        if self.tick:
            self.long_price = self.tick.ask_price1
            self.short_price = self.tick.bid_price1
            if self.params_map.price_type == "D2":
                self.long_price = self.tick.ask_price2
                self.short_price = self.tick.bid_price2

    def update_market_state(self) -> None:
        """更新市场状态"""
        # 分类市场状态
        regime, volatility, trend_strength = self.market_classifier.classify_regime()
        
        # 更新状态
        self.state_map.market_regime = regime
        self.state_map.volatility_level = volatility
        self.state_map.trend_strength = trend_strength
        
        # 打印市场状态变化
        if hasattr(self, '_last_regime') and self._last_regime != regime:
            print(f"市场状态变化: {self._last_regime} -> {regime} (波动率: {volatility:.4f}, 趋势强度: {trend_strength:.4f})")
        
        self._last_regime = regime

    def update_adaptive_weights(self) -> None:
        """根据市场状态动态调整模块权重
        更新逻辑:
        1. 获取各模块最新准确率
        2. 结合当前市场状态配置
        3. 计算平滑过渡权重
        """
        if not self.params_map.learning_enabled:
            return
        module_performance = {
            'technical': self.state_map.technical_accuracy,
            'pattern': self.state_map.pattern_accuracy,
            'wave': self.state_map.wave_accuracy,
            'fuzzy': self.state_map.fuzzy_accuracy
        }
        updated_weights = self.weight_allocator.update_weights(
            self.state_map.market_regime,
            module_performance
        )
        self.state_map.adaptive_weights = updated_weights

    def _generate_module_signals(self) -> Dict[str, float]:
        """
        生成各模块信号
        
        返回:
            各模块信号
        """
        signals = {}
        
        # 技术指标信号
        hull_current = self.state_map.hull_value
        hull_prev = self.hull_prev
        stc_current = self.state_map.stc_value
        stc_prev = self.stc_prev
        
        hull_up = hull_current > hull_prev
        stc_up = stc_current > stc_prev
        
        if hull_up and stc_up:
            signals['technical'] = 1.0
        elif not hull_up and not stc_up:
            signals['technical'] = -1.0
        else:
            signals['technical'] = 0.0
        
        # 形态识别信号
        pattern = self.state_map.pattern_detected
        strength = self.state_map.pattern_strength
        
        if pattern in ['bullish_engulfing', 'hammer', 'three_white_soldiers', 'gap_up']:
            signals['pattern'] = strength
        elif pattern in ['bearish_engulfing', 'shooting_star', 'three_black_crows', 'gap_down']:
            signals['pattern'] = -strength
        else:
            signals['pattern'] = 0.0
        
        # 波段分析信号
        wave_type = self.state_map.wave_type
        wave_strength = self.state_map.wave_strength
        
        if wave_type == 'uptrend':
            signals['wave'] = wave_strength
        elif wave_type == 'downtrend':
            signals['wave'] = -wave_strength
        else:
            signals['wave'] = 0.0
        
        # 模糊推理信号
        action_level = self.state_map.action_level
        confidence = self.state_map.confidence
        
        if action_level == "Aggressive":
            signals['fuzzy'] = confidence
        elif action_level == "Normal":
            signals['fuzzy'] = confidence * 0.5
        elif action_level == "Conservative":
            signals['fuzzy'] = confidence * 0.2
        elif action_level == "Stop":
            signals['fuzzy'] = -confidence
        else:
            signals['fuzzy'] = 0.0
        
        return signals

    def exec_signal(self) -> None:
        """执行交易信号"""
        self.signal_price = 0
        
        position = self.get_position(self.params_map.instrument_id)
        
        if self.order_id is not None:
            # 挂单未成交，撤单
            self.cancel_order(self.order_id)
        
        # 平仓逻辑
        if position.net_position > 0 and self.sell_signal:
            self.signal_price = -self.short_price
            
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.short_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
                print(f"执行卖出平仓: 价格={self.short_price}, 数量={position.net_position}, 信号强度={self.state_map.integrated_signal:.2f}, 置信度={self.state_map.signal_confidence:.2f}")
        elif position.net_position < 0 and self.cover_signal:
            self.signal_price = self.long_price
            
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=abs(position.net_position),
                    order_direction="buy"
                )
                print(f"执行买入平仓: 价格={self.long_price}, 数量={abs(position.net_position)}, 信号强度={self.state_map.integrated_signal:.2f}, 置信度={self.state_map.signal_confidence:.2f}")
        
        # 开仓逻辑
        if self.short_signal:
            self.signal_price = -self.short_price
            
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.short_price,
                    order_direction="sell"
                )
                print(f"执行卖出开仓: 价格={self.short_price}, 数量={self.params_map.order_volume}, 信号强度={self.state_map.integrated_signal:.2f}, 置信度={self.state_map.signal_confidence:.2f}")

    def on_order(self, order: OrderData) -> None:
        """订单状态回调"""
        super().on_order(order)
        # 可扩展订单状态处理逻辑

    def update_indicators(self, kline):
        """
        更新所有技术指标（同步状态）
        """
        # 多周期HULL同步更新
        for period, hull in self.hull_calculators.items():
            hull.update(kline.close)
        # 选用主周期（如9）作为state_map.hull_value
        hull_value, hull_prev = self.hull_calculators[9].get_hull()
        if hull_value is not None:
            self.hull_prev = self.state_map.hull_value
            self.state_map.hull_prev = self.state_map.hull_value
            self.state_map.hull_value = hull_value

        # 更新STC指标
        self.stc_calculator.update(kline.close)
        stc_value, stc_signal = self.stc_calculator.get_stc()
        if stc_value is not None:
            self.stc_prev = self.state_map.stc_value
            self.state_map.stc_value = stc_value
            self.state_map.stc_signal = stc_signal

        # 更新K线形态识别
        self.pattern_recognizer.update(kline)
        patterns = self.pattern_recognizer.recognize_patterns()
        detected = ''
        for k, v in patterns.items():
            if v:
                detected = k
                break
        self.state_map.pattern_detected = detected
        self.state_map.pattern_strength = 1.0 if detected else 0.0

        # 更新波段识别
        self.wave_recognizer.update(
            price=kline.close,
            hull_value=self.state_map.hull_value,
            stc_value=self.state_map.stc_value,
            timestamp=getattr(kline, 'datetime', None)
        )
        wave_info = self.wave_recognizer.get_wave_info()
        self.state_map.wave_type = wave_info.get('type', '')
        self.state_map.wave_strength = wave_info.get('strength', 0.0)

    def update_status_bar(self):
        """
        状态栏热更新，支持窗口和图表标题
        """
        # 构建状态文本
        trend_symbol = "↑" if self.state_map.trend_strength > 0 else "↓"
        position_info = ""
        try:
            position = self.get_position(self.params_map.instrument_id)
            if position and hasattr(position, 'net_position') and position.net_position != 0:
                position_info = f" | 持仓:{position.net_position}手"
        except Exception:
            pass
        status = (
            f"合约: {self.params_map.instrument_id} | "
            f"趋势: {abs(self.state_map.trend_strength):.2f}{trend_symbol} | "
            f"波动: {self.state_map.volatility_level:.4f} | "
            f"信号: {self.state_map.integrated_signal:.2f} | "
            f"置信度: {self.state_map.signal_confidence:.2f} | "
            f"形态: {self.state_map.pattern_detected} | "
            f"波段: {self.state_map.wave_type}" + position_info
        )
        if hasattr(self, 'widget') and self.widget is not None:
            # 更新窗口标题
            if hasattr(self.widget, 'setWindowTitle'):
                self.widget.setWindowTitle(f"策略 - {self.__class__.__name__} | {status[:50]}...")
            # 更新图表标题显示状态
            if hasattr(self.widget, 'kline_widget') and self.widget.kline_widget is not None:
                display_title = f"{self.params_map.instrument_id} | {status[:30]}..."
                if hasattr(self.widget.kline_widget, 'set_title'):
                    self.widget.kline_widget.set_title(display_title)