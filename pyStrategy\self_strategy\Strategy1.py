"""
Strategy1 - 多模块融合信号自适应交易策略
架构: 行情数据 → 多模块指标计算 → 自适应权重融合 → 置信度评估 → 动态阈值决策 → 执行引擎
符合无限易Pro官方规范标准

本策略整合了多种技术分析方法:
- HULL移动平均线 (多周期优化版)
- Schaff趋势周期指标
- K线形态识别 (增强版)
- 波段分析 (自适应版)
- 模糊逻辑系统 (自学习版)
- 模块相关性分析
- 动态阈值优化系统

通过多模块融合信号+自适应权重+置信度+灵活阈值体系，
直接用融合信号强度决策开平仓，保证策略响应性，充分利用多因子优势。
"""

from typing import Literal, Dict, List, Optional, Tuple, Any
import numpy as np
from collections import deque
import math
import time

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K线周期")
    
    # 技术指标参数
    hull_period: int = Field(default=9, title="HULL周期")
    stc_fast: int = Field(default=23, title="STC快周期")
    stc_slow: int = Field(default=50, title="STC慢周期")
    stc_cycle: int = Field(default=10, title="STC循环周期")
    
    # 交易参数
    trade_direction: Literal["buy", "sell"] = Field(default="buy", title="交易方向")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    
    # 风险控制参数
    stop_loss_pct: float = Field(default=0.02, title="止损百分比")
    take_profit_pct: float = Field(default=0.05, title="止盈百分比")
    
    # 模糊系统参数
    fuzzy_sensitivity: float = Field(default=0.5, title="模糊系统敏感度")
    
    # 自适应系统参数
    adaptation_speed: float = Field(default=0.1, title="适应速度")
    learning_enabled: bool = Field(default=True, title="启用学习")
    
    # 模块权重参数
    technical_weight: float = Field(default=0.25, title="技术指标权重")
    pattern_weight: float = Field(default=0.25, title="形态识别权重")
    wave_weight: float = Field(default=0.25, title="波段分析权重")
    fuzzy_weight: float = Field(default=0.25, title="模糊推理权重")

    # 市场状态参数
    volatility_threshold: float = Field(default=0.02, title="波动率阈值")
    trend_strength_threshold: float = Field(default=0.5, title="趋势强度阈值")

    # 新增：动态阈值参数
    base_signal_threshold: float = Field(default=0.6, title="基础信号阈值")
    confidence_threshold: float = Field(default=0.7, title="置信度阈值")
    correlation_threshold: float = Field(default=0.8, title="模块相关性阈值")

    # 新增：阈值自适应参数
    threshold_adaptation_speed: float = Field(default=0.05, title="阈值适应速度")
    volatility_scaling_factor: float = Field(default=1.5, title="波动率缩放因子")


class State(BaseState):
    """状态映射模型"""
    # HULL指标状态
    hull_value: float = Field(default=0, title="HULL当前值")
    hull_prev: float = Field(default=0, title="HULL前值")
    
    # STC指标状态
    stc_value: float = Field(default=0, title="STC当前值")
    stc_signal: float = Field(default=0, title="STC信号线")
    
    # 模糊系统状态
    risk_level: str = Field(default="", title="风险等级")
    action_level: str = Field(default="", title="行动等级")
    confidence: float = Field(default=0, title="置信度")
    
    # K线形态状态
    pattern_detected: str = Field(default="", title="检测到的形态")
    pattern_strength: float = Field(default=0, title="形态强度")
    
    # 波段识别状态
    wave_type: str = Field(default="", title="当前波段类型")
    wave_strength: float = Field(default=0, title="波段强度")
    
    # 市场状态
    market_regime: str = Field(default="unknown", title="市场状态")
    volatility_level: float = Field(default=0.0, title="波动率水平")
    trend_strength: float = Field(default=0.0, title="趋势强度")
    
    # 模块性能跟踪
    technical_accuracy: float = Field(default=0.5, title="技术指标准确率")
    pattern_accuracy: float = Field(default=0.5, title="形态识别准确率")
    wave_accuracy: float = Field(default=0.5, title="波段分析准确率")
    fuzzy_accuracy: float = Field(default=0.5, title="模糊推理准确率")
    
    # 综合决策状态
    integrated_signal: float = Field(default=0.0, title="综合信号强度")
    signal_confidence: float = Field(default=0.0, title="信号置信度")
    adaptive_weights: Dict[str, float] = Field(default_factory=dict, title="自适应权重")

    # 新增：模块相关性状态
    module_correlations: Dict[str, float] = Field(default_factory=dict, title="模块间相关性")
    redundancy_score: float = Field(default=0.0, title="因子冗余度")

    # 新增：动态阈值状态
    current_signal_threshold: float = Field(default=0.6, title="当前信号阈值")
    current_confidence_threshold: float = Field(default=0.7, title="当前置信度阈值")
    threshold_adjustment_factor: float = Field(default=1.0, title="阈值调整因子")


# ==================== 模糊系统 ====================

class TrapezoidalFuzzyNumber:
    """梯形模糊数实现"""
    def __init__(self, a: float, b: float, c: float, d: float):
        """
        初始化梯形模糊数
        
        参数:
            a: 左下角点
            b: 左上角点
            c: 右上角点
            d: 右下角点
        """
        assert a <= b <= c <= d, "Invalid trapezoid parameters"
        self.a, self.b, self.c, self.d = a, b, c, d
        
    def membership(self, x: float) -> float:
        """
        计算隶属度
        
        参数:
            x: 输入值
            
        返回:
            隶属度值 (0-1)
        """
        if x < self.a:
            return 0.0
        elif self.a <= x < self.b:
            return (x - self.a) / (self.b - self.a)
        elif self.b <= x <= self.c:
            return 1.0
        elif self.c < x <= self.d:
            return (self.d - x) / (self.d - self.c)
        else:
            return 0.0
    
    def centroid(self) -> float:
        """
        计算重心去模糊化值
        
        返回:
            重心值
        """
        return (self.a + self.b + self.c + self.d) / 4.0


class FuzzySystem:
    """标准模糊推理系统"""
    def __init__(self, sensitivity: float = 0.5):
        """
        初始化模糊系统
        
        参数:
            sensitivity: 敏感度参数 (0-1)
        """
        self.sensitivity = sensitivity
        
        # 初始化模糊集
        self.stability_sets = self._init_stability_sets()
        self.volatility_sets = self._init_volatility_sets()
        self.profit_sets = self._init_profit_sets()
        
        # 初始化规则库
        self.rules = self._init_rules()
        
        # 风险和行动等级
        self.risk_levels = ["RiskLow", "RiskMedium", "RiskHigh"]
        self.action_levels = ["Conservative", "Normal", "Aggressive", "Stop"]
        
        # 规则性能跟踪
        self.rule_performance = {
            'total_activations': {},  # 规则激活总次数
            'correct_activations': {},  # 规则正确激活次数
            'accuracy': {}  # 规则准确率
        }
        
        # 初始化规则性能计数器
        for i, rule in enumerate(self.rules):
            rule_id = f"rule_{i}"
            self.rule_performance['total_activations'][rule_id] = 0
            self.rule_performance['correct_activations'][rule_id] = 0
            self.rule_performance['accuracy'][rule_id] = 0.5  # 初始准确率为0.5
    
    def _init_stability_sets(self) -> Dict[str, TrapezoidalFuzzyNumber]:
        """初始化稳定性模糊集"""
        return {
            "Low": TrapezoidalFuzzyNumber(0, 0, 0.3, 0.5),
            "Medium": TrapezoidalFuzzyNumber(0.3, 0.5, 0.5, 0.7),
            "High": TrapezoidalFuzzyNumber(0.5, 0.7, 1, 1)
        }
    
    def _init_volatility_sets(self) -> Dict[str, TrapezoidalFuzzyNumber]:
        """初始化波动性模糊集"""
        return {
            "Low": TrapezoidalFuzzyNumber(0, 0, 0.02, 0.05),
            "Medium": TrapezoidalFuzzyNumber(0.02, 0.05, 0.05, 0.1),
            "High": TrapezoidalFuzzyNumber(0.05, 0.1, 1, 1)
        }
    
    def _init_profit_sets(self) -> Dict[str, TrapezoidalFuzzyNumber]:
        """初始化利润模糊集"""
        return {
            "Negative": TrapezoidalFuzzyNumber(-1, -1, -0.02, 0),
            "Neutral": TrapezoidalFuzzyNumber(-0.02, 0, 0, 0.02),
            "Positive": TrapezoidalFuzzyNumber(0, 0.02, 1, 1)
        }
    
    def _init_rules(self) -> List[Dict]:
        """初始化扩展模糊规则库 (16条规则)"""
        return [
            # 原有规则 (添加学习率参数)
            {"stability": "Low", "volatility": "Low", "profit": "Neutral", 
             "risk": "RiskLow", "action": "Conservative", "weight": 1.0, "learn_rate": 0.01},
            
            {"stability": "Medium", "volatility": "Low", "profit": "Positive", 
             "risk": "RiskMedium", "action": "Normal", "weight": 1.0, "learn_rate": 0.01},
            
            {"stability": "High", "volatility": "Low", "profit": "Positive", 
             "risk": "RiskHigh", "action": "Aggressive", "weight": 1.0, "learn_rate": 0.01},
            
            {"stability": "High", "volatility": "High", "profit": "Negative", 
             "risk": "RiskHigh", "action": "Stop", "weight": 1.0, "learn_rate": 0.01},
            
            {"stability": "Medium", "volatility": "Medium", "profit": "Neutral", 
             "risk": "RiskMedium", "action": "Normal", "weight": 1.0, "learn_rate": 0.01},
            
            {"stability": "Low", "volatility": "High", "profit": "Negative", 
             "risk": "RiskLow", "action": "Stop", "weight": 1.0, "learn_rate": 0.01},
            
            # 新增规则 (覆盖更多市场情况)
            {"stability": "Low", "volatility": "Low", "profit": "Positive", 
             "risk": "RiskLow", "action": "Normal", "weight": 1.0, "learn_rate": 0.01},
            
            {"stability": "Low", "volatility": "Medium", "profit": "Positive", 
             "risk": "RiskMedium", "action": "Conservative", "weight": 1.0, "learn_rate": 0.01},
            
            {"stability": "Low", "volatility": "Medium", "profit": "Negative", 
             "risk": "RiskLow", "action": "Stop", "weight": 1.0, "learn_rate": 0.01},
            
            {"stability": "Medium", "volatility": "Low", "profit": "Negative", 
             "risk": "RiskMedium", "action": "Conservative", "weight": 1.0, "learn_rate": 0.01},
            
            {"stability": "Medium", "volatility": "High", "profit": "Positive", 
             "risk": "RiskMedium", "action": "Normal", "weight": 1.0, "learn_rate": 0.01},
            
            {"stability": "Medium", "volatility": "High", "profit": "Negative", 
             "risk": "RiskHigh", "action": "Stop", "weight": 1.0, "learn_rate": 0.01},
            
            {"stability": "High", "volatility": "Low", "profit": "Negative", 
             "risk": "RiskMedium", "action": "Normal", "weight": 1.0, "learn_rate": 0.01},
            
            {"stability": "High", "volatility": "Medium", "profit": "Positive", 
             "risk": "RiskHigh", "action": "Aggressive", "weight": 1.0, "learn_rate": 0.01},
            
            {"stability": "High", "volatility": "Medium", "profit": "Negative", 
             "risk": "RiskMedium", "action": "Conservative", "weight": 1.0, "learn_rate": 0.01},
            
            {"stability": "High", "volatility": "High", "profit": "Positive", 
             "risk": "RiskHigh", "action": "Normal", "weight": 1.0, "learn_rate": 0.01}
        ]
    
    def fuzzify(self, inputs: Dict[str, float], hull_trend_strength: float = 0, 
               volatility: float = 0, stc_values: List[float] = None) -> Dict[str, Dict[str, float]]:
        """
        模糊化输入（增强版）
        
        参数:
            inputs: 包含基础输入变量的字典
            hull_trend_strength: Hull趋势强度
            volatility: 波动率
            stc_values: STC历史值列表
            
        返回:
            各输入变量的隶属度字典
        """
        # 获取基础输入值（如果没有提供增强参数，则使用基础值）
        base_stability = inputs.get('stability', 0.5)
        base_volatility = inputs.get('volatility', 0.05)
        base_profit = inputs.get('profit', 0.0)
        
        # 增强稳定性计算：结合Hull趋势强度和波动率
        stability = base_stability
        if hull_trend_strength != 0 or volatility != 0:
            # 使用公式: stability = 0.6*hull_trend_strength + 0.4*(1-volatility)
            stability = 0.6 * abs(hull_trend_strength) + 0.4 * (1 - min(1, volatility * 10))
            stability = max(0, min(1, stability))  # 确保在[0,1]范围内
        
        # 增强波动性计算：直接使用提供的波动率，或使用基础值
        volatility_value = volatility if volatility != 0 else base_volatility
        
        # 增强利润趋势计算：使用STC的3周期斜率
        profit = base_profit
        if stc_values and len(stc_values) >= 4:
            # 使用公式: profit_trend = (stc_values[-1] - stc_values[-4])/3
            stc_slope = (stc_values[-1] - stc_values[-4]) / 3
            # 归一化到[-1,1]范围
            profit = max(-1, min(1, stc_slope / 10))
        
        # 计算隶属度
        memberships = {
            'stability': {k: f.membership(stability) for k, f in self.stability_sets.items()},
            'volatility': {k: f.membership(volatility_value) for k, f in self.volatility_sets.items()},
            'profit': {k: f.membership(profit) for k, f in self.profit_sets.items()}
        }
        return memberships
    def infer(self, memberships: Dict[str, Dict[str, float]], signal_correct: bool = None) -> Tuple[str, str, float]:
        """
        模糊推理
        
        参数:
            memberships: 模糊化后的隶属度字典
            signal_correct: 信号是否正确的反馈（用于自适应学习）
            
        返回:
            (风险等级, 行动等级, 置信度)
        """
        stability_mem = memberships['stability']
        volatility_mem = memberships['volatility']
        profit_mem = memberships['profit']
        
        # 计算综合风险等级
        risk_scores = {}
        for risk_level in self.risk_levels:
            score = 0.0
            for rule in self.rules:
                if rule['risk'] == risk_level:
                    s_match = stability_mem.get(rule['stability'], 0)
                    v_match = volatility_mem.get(rule['volatility'], 0)
                    p_match = profit_mem.get(rule['profit'], 0)
                    
                    # 应用敏感度调整
                    rule_strength = min(s_match, v_match, p_match) * rule.get('weight', 1.0)
                    rule_strength = rule_strength * (1 + (self.sensitivity - 0.5))
                    
                    score += rule_strength
            
            risk_scores[risk_level] = score
        
        # 计算综合行动等级
        action_scores = {}
        for action_level in self.action_levels:
            score = 0.0
            for rule in self.rules:
                if rule['action'] == action_level:
                    s_match = stability_mem.get(rule['stability'], 0)
                    v_match = volatility_mem.get(rule['volatility'], 0)
                    p_match = profit_mem.get(rule['profit'], 0)
                    
                    # 应用敏感度调整
                    rule_strength = min(s_match, v_match, p_match) * rule.get('weight', 1.0)
                    rule_strength = rule_strength * (1 + (self.sensitivity - 0.5))
                    
                    score += rule_strength
            
            action_scores[action_level] = score
        
        # 选择最高分数的风险等级和行动等级
        best_risk = max(risk_scores.items(), key=lambda x: x[1])[0]
        best_action = max(action_scores.items(), key=lambda x: x[1])[0]
        
        # 计算置信度
        confidence = max(max(risk_scores.values()), max(action_scores.values()))
        
        # 自适应规则权重调整（如果提供了信号正确性反馈）
        if signal_correct is not None:
            for i, rule in enumerate(self.rules):
                rule_id = f"rule_{i}"
                # 如果规则参与了决策（风险或行动等级匹配），则调整其权重
                if rule['risk'] == best_risk or rule['action'] == best_action:
                    # 更新规则性能统计
                    self.rule_performance['total_activations'][rule_id] += 1
                    if signal_correct:
                        self.rule_performance['correct_activations'][rule_id] += 1
                    
                    # 计算规则准确率
                    total = self.rule_performance['total_activations'][rule_id]
                    correct = self.rule_performance['correct_activations'][rule_id]
                    self.rule_performance['accuracy'][rule_id] = correct / total if total > 0 else 0.5
                    
                    # 调整规则权重
                    learn_rate = rule.get('learn_rate', 0.01)
                    if signal_correct:
                        # 信号正确，增加权重
                        rule['weight'] = min(1.5, rule['weight'] + learn_rate)
                    else:
                        # 信号错误，减少权重
                        rule['weight'] = max(0.5, rule['weight'] - learn_rate)
        
        return best_risk, best_action, confidence
# =============== 技术指标系统 ====================

class HullMovingAverage:
    """TradingView标准Hull移动平均线 (优化版)
    
    优化点:
    1. 使用deque替代list提高队列操作效率
    2. 实现增量计算和缓存机制
    3. 添加计算状态标志避免重复计算
    """
    def __init__(self, period: int = 9):
        self.period = period
        self.half_period = max(1, int(period / 2))
        self.sqrt_period = max(1, int(np.sqrt(period)))
        
        # 使用deque替代list提高效率
        self.prices = deque(maxlen=period*2)
        
        # 缓存机制
        self.wma_half_cache = deque(maxlen=100)
        self.wma_full_cache = deque(maxlen=100)
        self.raw_cache = deque(maxlen=100)
        self.hma_cache = deque(maxlen=100)
        
        # 状态标志
        self.cache_valid = False
        self._is_ready = False
        self._last_price = None

    def _wma(self, data, period):
        """计算加权移动平均"""
        if len(data) < period:
            return None
        weights = np.arange(1, period + 1)
        window = np.array(data[-period:])
        return np.dot(window, weights) / weights.sum()
    
    def _wma_incremental(self, prev_wma, oldest_value, newest_value, period):
        """增量计算WMA，避免重复计算整个序列"""
        if prev_wma is None:
            # 如果没有前值，则完整计算
            data = list(self.prices)[-period:]
            return self._wma(data, period)
        
        # 增量计算WMA
        weights = np.arange(1, period + 1)
        weight_sum = weights.sum()
        
        # 移除最旧的值的贡献并添加最新值的贡献
        return prev_wma + (newest_value * period - oldest_value) / weight_sum

    def update(self, price: float) -> None:
        """更新价格并标记缓存状态"""
        if not isinstance(price, (int, float)) or math.isnan(price) or math.isinf(price):
            return
        
        # 检查是否是重复价格
        if self._last_price == price and len(self.prices) > 0:
            return
            
        self._last_price = float(price)
        self.prices.append(self._last_price)
        
        if len(self.prices) >= self.period:
            self._is_ready = True
            
        # 标记缓存需要更新
        self.cache_valid = False

    def get_hull(self):
        """获取Hull值，优先使用缓存"""
        if not self._is_ready or len(self.prices) < self.period:
            return None, None
        
        # 如果缓存有效且有足够数据，直接返回缓存值
        if self.cache_valid and len(self.hma_cache) >= 2:
            return self.hma_cache[-1], self.hma_cache[-2]
        
        # 缓存无效，需要计算
        prices_list = list(self.prices)
        
        # 1. 计算半周期WMA
        if len(self.wma_half_cache) > 0 and len(prices_list) > self.half_period:
            # 增量计算
            oldest_price = prices_list[-self.half_period-1] if len(prices_list) > self.half_period else prices_list[0]
            newest_price = prices_list[-1]
            new_wma_half = self._wma_incremental(
                self.wma_half_cache[-1], 
                oldest_price, 
                newest_price, 
                self.half_period
            )
            self.wma_half_cache.append(new_wma_half)
        else:
            # 完整计算
            for i in range(self.half_period, len(prices_list)+1):
                wma = self._wma(prices_list[:i], self.half_period)
                if wma is not None:
                    self.wma_half_cache.append(wma)
        
        # 2. 计算全周期WMA
        if len(self.wma_full_cache) > 0 and len(prices_list) > self.period:
            # 增量计算
            oldest_price = prices_list[-self.period-1] if len(prices_list) > self.period else prices_list[0]
            newest_price = prices_list[-1]
            new_wma_full = self._wma_incremental(
                self.wma_full_cache[-1], 
                oldest_price, 
                newest_price, 
                self.period
            )
            self.wma_full_cache.append(new_wma_full)
        else:
            # 完整计算
            for i in range(self.period, len(prices_list)+1):
                wma = self._wma(prices_list[:i], self.period)
                if wma is not None:
                    self.wma_full_cache.append(wma)
        
        # 确保长度一致
        min_len = min(len(self.wma_half_cache), len(self.wma_full_cache))
        if min_len < 1:
            return None, None
            
        # 3. 计算raw值 (2*half - full)
        if len(self.raw_cache) == 0 or len(self.raw_cache) < min_len:
            # 需要重新计算raw值
            self.raw_cache.clear()
            for i in range(min_len):
                self.raw_cache.append(2 * self.wma_half_cache[i] - self.wma_full_cache[i])
        else:
            # 增量计算最新的raw值
            new_raw = 2 * self.wma_half_cache[-1] - self.wma_full_cache[-1]
            self.raw_cache.append(new_raw)
        
        # 4. 计算最终HMA值
        raw_list = list(self.raw_cache)
        if len(self.hma_cache) > 0 and len(raw_list) > self.sqrt_period:
            # 增量计算
            oldest_raw = raw_list[-self.sqrt_period-1] if len(raw_list) > self.sqrt_period else raw_list[0]
            newest_raw = raw_list[-1]
            new_hma = self._wma_incremental(
                self.hma_cache[-1], 
                oldest_raw, 
                newest_raw, 
                self.sqrt_period
            )
            self.hma_cache.append(new_hma)
        else:
            # 完整计算
            for i in range(self.sqrt_period, len(raw_list)+1):
                hma = self._wma(raw_list[:i], self.sqrt_period)
                if hma is not None:
                    self.hma_cache.append(hma)
        
        # 标记缓存有效
        self.cache_valid = True
        
        if len(self.hma_cache) < 2:
            return None, None
            
        return self.hma_cache[-1], self.hma_cache[-2]
    
    def is_ready(self) -> bool:
        """检查指标是否就绪"""
        return self._is_ready

    def get_trend(self):
        """
        获取趋势方向 ("BULLISH", "BEARISH", "NEUTRAL")
        """
        h1, h2 = self.get_hull()
        if h1 is None or h2 is None:
            return None
        if h1 > h2:
            return "BULLISH"
        elif h1 < h2:
            return "BEARISH"
        else:
            return "NEUTRAL"


class SchaffTrendCycle:
    """Schaff趋势周期指标"""
    def __init__(self, fast_period: int = 23, slow_period: int = 50, cycle_period: int = 10):
        """
        初始化Schaff趋势周期指标
        
        参数:
            fast_period: 快速周期
            slow_period: 慢速周期
            cycle_period: 循环周期
        """
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.cycle_period = cycle_period
        
        # 使用deque实现高效循环缓冲区
        self.prices = deque(maxlen=200)
        self.macd_line = deque(maxlen=100)
        self.stoch1 = deque(maxlen=50)
        self.stoch2 = deque(maxlen=50)
        self.stc_final = deque(maxlen=30)
        self.stc_signal = deque(maxlen=20)
        
    def ema(self, data: List[float], period: int) -> Optional[List[float]]:
        """
        指数移动平均
        
        参数:
            data: 价格数据
            period: 周期
            
        返回:
            EMA值列表
        """
        if len(data) < period:
            return None
        
        alpha = 2.0 / (period + 1)
        ema_values = [data[0]]
        
        for i in range(1, len(data)):
            ema = alpha * data[i] + (1 - alpha) * ema_values[-1]
            ema_values.append(ema)
        
        return ema_values
    
    def update(self, price: float) -> None:
        """
        更新STC值
        
        参数:
            price: 最新价格
        """
        self.prices.append(price)
        
        if len(self.prices) < self.slow_period:
            return
        
        # 计算MACD线
        fast_ema = self.ema(list(self.prices), self.fast_period)
        slow_ema = self.ema(list(self.prices), self.slow_period)
        
        if fast_ema and slow_ema:
            # 取最新值计算MACD
            macd_value = fast_ema[-1] - slow_ema[-1]
            self.macd_line.append(macd_value)
            
            # 计算Stoch1
            if len(self.macd_line) >= self.cycle_period:
                stoch1_value = self._calculate_stochastic_single(list(self.macd_line), self.cycle_period)
                if stoch1_value is not None:
                    self.stoch1.append(stoch1_value)
                    
                    # 计算Stoch2
                    if len(self.stoch1) >= self.cycle_period:
                        stoch2_value = self._calculate_stochastic_single(list(self.stoch1), self.cycle_period)
                        if stoch2_value is not None:
                            self.stoch2.append(stoch2_value)
                            
                            # 计算最终STC值
                            if len(self.stoch2) >= 3:
                                stc_smoothed = self.ema(list(self.stoch2), 3)
                                if stc_smoothed:
                                    self.stc_final.append(stc_smoothed[-1])
                                    
                                    # 计算STC信号线
                                    if len(self.stc_final) >= 3:
                                        signal_values = self.ema(list(self.stc_final), 3)
                                        if signal_values:
                                            self.stc_signal.append(signal_values[-1])
    
    def _calculate_stochastic_single(self, data: List[float], period: int) -> Optional[float]:
        """
        计算单个随机指标值
        
        参数:
            data: 价格数据
            period: 周期
            
        返回:
            随机值
        """
        if len(data) < period:
            return None
        
        window = data[-period:]
        highest = np.max(window)
        lowest = np.min(window)
        
        if highest == lowest:
            return 50.0
        else:
            return 100 * (data[-1] - lowest) / (highest - lowest)
    
    def get_stc(self) -> Tuple[Optional[float], Optional[float]]:
        """
        获取当前STC值
        
        返回:
            (STC值, 信号线值)
        """
        if len(self.stc_final) >= 2 and len(self.stc_signal) >= 1:
            return self.stc_final[-1], self.stc_signal[-1]
        return None, None
    
    def is_ready(self) -> bool:
        """
        检查指标是否就绪
        
        返回:
            是否就绪
        """
        return len(self.stc_final) > 2 and len(self.stc_signal) > 1
class CandlePatternRecognizer:
    """K线形态识别器（增强版）"""
    def __init__(self, window_size: int = 10):
        """
        初始化K线形态识别器
        
        参数:
            window_size: 窗口大小
        """
        self.window_size = window_size
        self.kline_buffer = deque(maxlen=window_size)
        
        # 动态阈值组件
        self.atr = deque(maxlen=14)  # ATR (Average True Range)
        self.avg_volume = deque(maxlen=30)  # 平均成交量
        
        # 形态强度跟踪
        self.pattern_success_rate = {
            'hammer': 0.5,
            'shooting_star': 0.5,
            'marubozu': 0.5,
            'bullish_engulfing': 0.5,
            'bearish_engulfing': 0.5,
            'three_white_soldiers': 0.5,
            'three_black_crows': 0.5,
            'gap_up': 0.5,
            'gap_down': 0.5,
            'morning_star': 0.5,
            'evening_star': 0.5,
            'harami': 0.5,
            'piercing': 0.5,
            'dark_cloud_cover': 0.5
        }
        
    def update(self, kline: KLineData) -> None:
        """
        更新K线数据
        
        参数:
            kline: K线数据
        """
        # 添加新K线
        candle = {
            'open': kline.open,
            'high': kline.high,
            'low': kline.low,
            'close': kline.close,
            'volume': kline.volume
        }
        self.kline_buffer.append(candle)
        
        # 更新ATR
        if len(self.kline_buffer) >= 2:
            prev_candle = self.kline_buffer[-2]
            tr = max(
                candle['high'] - candle['low'],  # 当前K线范围
                abs(candle['high'] - prev_candle['close']),  # 当前最高与前收盘
                abs(candle['low'] - prev_candle['close'])  # 当前最低与前收盘
            )
            
            # 使用EMA计算ATR
            if not self.atr:
                self.atr.append(tr)
            else:
                self.atr.append((self.atr[-1] * 13 + tr) / 14)  # 14周期EMA
        
        # 更新平均成交量
        if not self.avg_volume:
            self.avg_volume.append(kline.volume)
        else:
            self.avg_volume.append((self.avg_volume[-1] * 29 + kline.volume) / 30)  # 30周期EMA
    
    def recognize_patterns(self) -> Dict[str, float]:
        """
        识别关键K线形态
        
        返回:
            形态识别结果字典，值为形态强度 (0表示未识别，>0表示识别到的强度)
        """
        if len(self.kline_buffer) < 3:  # 至少需要3根K线
            return {}
        
        features = {}
        candles = list(self.kline_buffer)
        current = candles[-1]
        prev = candles[-2]
        prev2 = candles[-3]
        
        # 所有形态识别
        patterns = {
            # 基础形态
            'hammer': self._is_hammer(current),
            'shooting_star': self._is_shooting_star(current),
            'marubozu': self._is_marubozu(current),
            'bullish_engulfing': self._is_bullish_engulfing(prev, current),
            'bearish_engulfing': self._is_bearish_engulfing(prev, current),
            'three_white_soldiers': self._is_three_white_soldiers(prev2, prev, current),
            'three_black_crows': self._is_three_black_crows(prev2, prev, current),
            'gap_up': self._is_gap_up(prev, current),
            'gap_down': self._is_gap_down(prev, current),
            
            # 新增形态
            'morning_star': self._is_morning_star(candles[-3:]),
            'evening_star': self._is_evening_star(candles[-3:]),
            'harami': self._is_harami(prev, current),
            'piercing': self._is_piercing(prev, current),
            'dark_cloud_cover': self._is_dark_cloud_cover(prev, current)
        }
        
        # 计算形态强度
        for pattern_name, detected in patterns.items():
            if detected:
                strength = self.get_pattern_strength(pattern_name)
                features[pattern_name] = strength
            else:
                features[pattern_name] = 0.0
        
        return features
    
    def _is_hammer(self, candle: Dict) -> bool:
        """
        识别锤子线 (底部反转信号)
        
        参数:
            candle: K线数据
            
        返回:
            是否为锤子线
        """
        body = abs(candle['close'] - candle['open'])
        total_range = candle['high'] - candle['low']
        lower_shadow = min(candle['open'], candle['close']) - candle['low']
        upper_shadow = candle['high'] - max(candle['open'], candle['close'])
        
        if total_range == 0 or body == 0:
            return False
        
        # 动态阈值计算
        dynamic_factor = 0.7 * (self.atr[-1] / candle['close']) if self.atr else 0.01
        
        # 成交量确认
        volume_confirmed = candle['volume'] > (self.avg_volume[-1] * 0.8) if self.avg_volume else True
        
        # 锤子线条件：长下影线，短上影线，小实体
        return (lower_shadow >= (2 * body * (1 + dynamic_factor)) and 
                upper_shadow <= max(body * 0.5, total_range * 0.1) and
                body / total_range >= 0.05 and
                volume_confirmed)
    
    def _is_shooting_star(self, candle: Dict) -> bool:
        """
        识别射击之星 (顶部反转信号)
        
        参数:
            candle: K线数据
            
        返回:
            是否为射击之星
        """
        body = abs(candle['close'] - candle['open'])
        total_range = candle['high'] - candle['low']
        upper_shadow = candle['high'] - max(candle['open'], candle['close'])
        
        # 动态阈值计算
        dynamic_factor = 0.7 * (self.atr[-1] / candle['close']) if self.atr else 0.01
        
        # 成交量确认
        volume_confirmed = candle['volume'] > (self.avg_volume[-1] * 0.8) if self.avg_volume else True
        
        return (body > 0 and 
                upper_shadow >= (2 * body * (1 + dynamic_factor)) and 
                (min(candle['open'], candle['close']) - candle['low']) <= body * 0.3 and
                volume_confirmed)
    
    def _is_marubozu(self, candle: Dict) -> bool:
        """
        识别光头光脚大阳/阴线
        
        参数:
            candle: K线数据
            
        返回:
            是否为光头光脚线
        """
        body = abs(candle['close'] - candle['open'])
        total_range = candle['high'] - candle['low']
        
        # 动态阈值计算
        dynamic_factor = 0.7 * (self.atr[-1] / candle['close']) if self.atr else 0.01
        threshold = 0.9 - dynamic_factor  # 动态调整阈值
        
        # 成交量确认
        volume_confirmed = candle['volume'] > (self.avg_volume[-1] * 1.1) if self.avg_volume else True
        
        return body > 0 and (body / total_range) > threshold and volume_confirmed
    
    def _is_bullish_engulfing(self, prev: Dict, current: Dict) -> bool:
        """
        看涨吞没形态
        
        参数:
            prev: 前一根K线
            current: 当前K线
            
        返回:
            是否为看涨吞没形态
        """
        # 前一根是阴线，当前是阳线
        prev_is_bearish = prev['close'] < prev['open']
        current_is_bullish = current['close'] > current['open']
        
        # 当前K线完全吞没前一根K线
        engulfs_completely = (current['open'] <= prev['close'] and 
                             current['close'] >= prev['open'])
        
        # 动态容错机制 - 基于ATR调整容错范围
        tolerance_factor = 0.5 * (self.atr[-1] / prev['close']) if self.atr else 0.1
        tolerance = abs(prev['close'] - prev['open']) * tolerance_factor
        engulfs_with_tolerance = (current['open'] <= prev['close'] + tolerance and 
                                 current['close'] >= prev['open'] - tolerance)
        
        # 成交量确认
        volume_confirmed = current['volume'] > (prev['volume'] * 1.1)
        
        return (prev_is_bearish and current_is_bullish and 
                (engulfs_completely or engulfs_with_tolerance) and
                volume_confirmed)
    
    def _is_bearish_engulfing(self, prev: Dict, current: Dict) -> bool:
        """
        看跌吞没形态
        
        参数:
            prev: 前一根K线
            current: 当前K线
            
        返回:
            是否为看跌吞没形态
        """
        # 前一根是阳线，当前是阴线
        prev_is_bullish = prev['close'] > prev['open']
        current_is_bearish = current['close'] < current['open']
        
        # 当前K线完全吞没前一根K线
        engulfs_completely = (current['open'] >= prev['close'] and 
                             current['close'] <= prev['open'])
        
        # 动态容错机制 - 基于ATR调整容错范围
        tolerance_factor = 0.5 * (self.atr[-1] / prev['close']) if self.atr else 0.1
        tolerance = abs(prev['close'] - prev['open']) * tolerance_factor
        engulfs_with_tolerance = (current['open'] >= prev['close'] - tolerance and 
                                 current['close'] <= prev['open'] + tolerance)
        
        # 成交量确认
        volume_confirmed = current['volume'] > (prev['volume'] * 1.1)
        
        return (prev_is_bullish and current_is_bearish and 
                (engulfs_completely or engulfs_with_tolerance) and
                volume_confirmed)
    
    def _is_three_white_soldiers(self, prev2: Dict, prev: Dict, current: Dict) -> bool:
        """
        三白兵形态 (连续三根阳线)
        
        参数:
            prev2: 前前一根K线
            prev: 前一根K线
            current: 当前K线
            
        返回:
            是否为三白兵形态
        """
        # 基本条件：三根连续阳线，每根开盘价高于前一根收盘价
        basic_condition = (prev2['close'] > prev2['open'] and 
                          prev['close'] > prev['open'] and 
                          current['close'] > current['open'] and
                          prev['open'] > prev2['close'] and
                          current['open'] > prev['close'])
        
        if not basic_condition:
            return False
        
        # 动态条件：每根K线的实体应该足够大
        body1 = abs(prev2['close'] - prev2['open'])
        body2 = abs(prev['close'] - prev['open'])
        body3 = abs(current['close'] - current['open'])
        
        avg_price = (current['close'] + prev['close'] + prev2['close']) / 3
        min_body_size = 0.3 * self.atr[-1] if self.atr else 0.005 * avg_price
        
        body_condition = (body1 > min_body_size and 
                         body2 > min_body_size and 
                         body3 > min_body_size)
        
        # 成交量确认：成交量应该逐渐增加
        volume_condition = (prev['volume'] >= prev2['volume'] * 0.9 and 
                           current['volume'] >= prev['volume'] * 0.9)
        
        return basic_condition and body_condition and volume_condition
    
    def _is_three_black_crows(self, prev2: Dict, prev: Dict, current: Dict) -> bool:
        """
        三黑鸦形态 (连续三根阴线)
        
        参数:
            prev2: 前前一根K线
            prev: 前一根K线
            current: 当前K线
            
        返回:
            是否为三黑鸦形态
        """
        # 基本条件：三根连续阴线，每根开盘价低于前一根收盘价
        basic_condition = (prev2['close'] < prev2['open'] and 
                          prev['close'] < prev['open'] and 
                          current['close'] < current['open'] and
                          prev['open'] < prev2['close'] and
                          current['open'] < prev['close'])
        
        if not basic_condition:
            return False
        
        # 动态条件：每根K线的实体应该足够大
        body1 = abs(prev2['close'] - prev2['open'])
        body2 = abs(prev['close'] - prev['open'])
        body3 = abs(current['close'] - current['open'])
        
        avg_price = (current['close'] + prev['close'] + prev2['close']) / 3
        min_body_size = 0.3 * self.atr[-1] if self.atr else 0.005 * avg_price
        
        body_condition = (body1 > min_body_size and 
                         body2 > min_body_size and 
                         body3 > min_body_size)
        
        # 成交量确认：成交量应该逐渐增加
        volume_condition = (prev['volume'] >= prev2['volume'] * 0.9 and 
                           current['volume'] >= prev['volume'] * 0.9)
        
        return basic_condition and body_condition and volume_condition
    
    def _is_gap_up(self, prev: Dict, current: Dict) -> bool:
        """
        向上跳空识别
        
        参数:
            prev: 前一根K线
            current: 当前K线
            
        返回:
            是否为向上跳空
        """
        gap_size = current['low'] - prev['high']
        if gap_size <= 0:
            return False
        
        # 计算相对跳空幅度 - 使用ATR动态调整
        min_gap_size = 0.2 * self.atr[-1] if self.atr else 0
        
        # 成交量确认 - 跳空后成交量应该增加
        volume_confirmed = current['volume'] > (prev['volume'] * 1.2)
        
        return gap_size > min_gap_size and volume_confirmed
    
    def _is_gap_down(self, prev: Dict, current: Dict) -> bool:
        """
        向下跳空识别
        
        参数:
            prev: 前一根K线
            current: 当前K线
            
        返回:
            是否为向下跳空
        """
        gap_size = prev['low'] - current['high']
        if gap_size <= 0:
            return False
        
        # 计算相对跳空幅度 - 使用ATR动态调整
        min_gap_size = 0.2 * self.atr[-1] if self.atr else 0
        
        # 成交量确认 - 跳空后成交量应该增加
        volume_confirmed = current['volume'] > (prev['volume'] * 1.2)
        
        return gap_size > min_gap_size and volume_confirmed
    
    def is_ready(self) -> bool:
        """
        检查形态识别器是否就绪
        
        返回:
            是否就绪
        """
        return len(self.kline_buffer) >= 3

    def update_pattern_success(self, pattern_name: str, success: bool) -> None:
        """
        更新形态识别成功率
        
        参数:
            pattern_name: 形态名称
            success: 是否成功
        """
        if pattern_name not in self.pattern_success_rate:
            self.pattern_success_rate[pattern_name] = 0.5  # 初始值
            
        # 使用指数移动平均更新成功率
        alpha = 0.1  # 学习率
        current_rate = self.pattern_success_rate[pattern_name]
        success_value = 1.0 if success else 0.0
        self.pattern_success_rate[pattern_name] = current_rate * (1 - alpha) + success_value * alpha
    
    def get_pattern_strength(self, pattern_name: str) -> float:
        """
        获取形态强度
        
        参数:
            pattern_name: 形态名称
            
        返回:
            形态强度 (0-1)
        """
        # 基础强度来自历史成功率
        base_strength = self.pattern_success_rate.get(pattern_name, 0.5)
        
        # 根据当前市场条件调整强度
        adjustment = 0.0
        
        # 1. 成交量因子：当前成交量相对于平均成交量
        if self.kline_buffer and self.avg_volume:
            current_volume = self.kline_buffer[-1]['volume']
            avg_volume = self.avg_volume[-1]
            volume_factor = min(2.0, current_volume / avg_volume) if avg_volume > 0 else 1.0
            
            # 成交量高于平均时增强信号
            if volume_factor > 1.2:
                adjustment += 0.1
            elif volume_factor < 0.8:
                adjustment -= 0.1
        
        # 2. 波动率因子：当前ATR相对于平均价格
        if self.kline_buffer and self.atr:
            current_price = self.kline_buffer[-1]['close']
            current_atr = self.atr[-1]
            volatility_ratio = current_atr / current_price if current_price > 0 else 0
            
            # 高波动率环境下，某些形态更可靠
            high_volatility_patterns = ['hammer', 'shooting_star', 'bullish_engulfing', 'bearish_engulfing']
            low_volatility_patterns = ['three_white_soldiers', 'three_black_crows']
            
            if pattern_name in high_volatility_patterns and volatility_ratio > 0.01:
                adjustment += 0.1
            elif pattern_name in low_volatility_patterns and volatility_ratio < 0.005:
                adjustment += 0.1
        
        # 计算最终强度，确保在0-1范围内
        final_strength = max(0.1, min(0.9, base_strength + adjustment))
        return final_strength
    
    def _is_morning_star(self, candles: List[Dict]) -> bool:
        """
        识别晨星形态（底部反转信号）
        
        参数:
            candles: 至少3根K线数据
            
        返回:
            是否为晨星形态
        """
        if len(candles) < 3:
            return False
            
        c1, c2, c3 = candles[-3], candles[-2], candles[-1]
        
        # 第一根是阴线
        c1_bearish = c1['close'] < c1['open']
        c1_body = abs(c1['close'] - c1['open'])
        
        # 第二根是小实体（阴线或阳线）
        c2_body = abs(c2['close'] - c2['open'])
        c2_small_body = c2_body < c1_body * 0.5
        
        # 第三根是阳线，且收盘价至少进入第一根K线实体的一半
        c3_bullish = c3['close'] > c3['open']
        c3_body = abs(c3['close'] - c3['open'])
        c3_closes_into_c1 = c3['close'] > (c1['open'] + c1['close']) / 2
        
        # 第二根K线的最低价低于第一根和第三根
        c2_lowest = c2['low'] < min(c1['low'], c3['low'])
        
        # 成交量确认：第三根K线成交量大于前两根的平均值
        volume_confirmed = c3['volume'] > (c1['volume'] + c2['volume']) / 2
        
        return (c1_bearish and c2_small_body and c3_bullish and 
                c3_closes_into_c1 and c2_lowest and volume_confirmed)
    
    def _is_evening_star(self, candles: List[Dict]) -> bool:
        """
        识别暮星形态（顶部反转信号）
        
        参数:
            candles: 至少3根K线数据
            
        返回:
            是否为暮星形态
        """
        if len(candles) < 3:
            return False
            
        c1, c2, c3 = candles[-3], candles[-2], candles[-1]
        
        # 第一根是阳线
        c1_bullish = c1['close'] > c1['open']
        c1_body = abs(c1['close'] - c1['open'])
        
        # 第二根是小实体（阴线或阳线）
        c2_body = abs(c2['close'] - c2['open'])
        c2_small_body = c2_body < c1_body * 0.5
        
        # 第三根是阴线，且收盘价至少进入第一根K线实体的一半
        c3_bearish = c3['close'] < c3['open']
        c3_body = abs(c3['close'] - c3['open'])
        c3_closes_into_c1 = c3['close'] < (c1['open'] + c1['close']) / 2
        
        # 第二根K线的最高价高于第一根和第三根
        c2_highest = c2['high'] > max(c1['high'], c3['high'])
        
        # 成交量确认：第三根K线成交量大于前两根的平均值
        volume_confirmed = c3['volume'] > (c1['volume'] + c2['volume']) / 2
        
        return (c1_bullish and c2_small_body and c3_bearish and 
                c3_closes_into_c1 and c2_highest and volume_confirmed)
    
    def _is_harami(self, prev: Dict, current: Dict) -> bool:
        """
        识别孕线形态（反转信号）
        
        参数:
            prev: 前一根K线
            current: 当前K线
            
        返回:
            是否为孕线形态
        """
        # 计算实体大小
        prev_body = abs(prev['close'] - prev['open'])
        current_body = abs(current['close'] - current['open'])
        
        # 当前K线实体应该小于前一根K线实体
        body_size_condition = current_body < prev_body * 0.6
        
        # 当前K线实体应该完全包含在前一根K线实体内
        if prev['close'] > prev['open']:  # 前一根是阳线
            inside_condition = (current['open'] < prev['close'] and 
                               current['close'] > prev['open'] and
                               current['open'] > prev['open'] and
                               current['close'] < prev['close'])
        else:  # 前一根是阴线
            inside_condition = (current['open'] > prev['close'] and 
                               current['close'] < prev['open'] and
                               current['open'] < prev['open'] and
                               current['close'] > prev['close'])
        
        # 成交量确认：当前K线成交量应该小于前一根
        volume_condition = current['volume'] < prev['volume'] * 0.8
        
        return body_size_condition and inside_condition and volume_condition
    
    def _is_piercing(self, prev: Dict, current: Dict) -> bool:
        """
        识别刺透形态（底部反转信号）
        
        参数:
            prev: 前一根K线
            current: 当前K线
            
        返回:
            是否为刺透形态
        """
        # 前一根是阴线
        prev_bearish = prev['close'] < prev['open']
        
        # 当前K线是阳线
        current_bullish = current['close'] > current['open']
        
        # 当前K线开盘价低于前一根K线的最低价
        gap_down = current['open'] < prev['low']
        
        # 当前K线收盘价至少刺入前一根K线实体的50%
        pierce_level = (current['close'] - prev['close']) / (prev['open'] - prev['close']) if prev['open'] != prev['close'] else 0
        deep_pierce = pierce_level > 0.5
        
        # 成交量确认：当前K线成交量大于前一根
        volume_confirmed = current['volume'] > prev['volume'] * 1.2
        
        return prev_bearish and current_bullish and gap_down and deep_pierce and volume_confirmed
    
    def _is_dark_cloud_cover(self, prev: Dict, current: Dict) -> bool:
        """
        识别乌云盖顶形态（顶部反转信号）
        
        参数:
            prev: 前一根K线
            current: 当前K线
            
        返回:
            是否为乌云盖顶形态
        """
        # 前一根是阳线
        prev_bullish = prev['close'] > prev['open']
        
        # 当前K线是阴线
        current_bearish = current['close'] < current['open']
        
        # 当前K线开盘价高于前一根K线的最高价
        gap_up = current['open'] > prev['high']
        
        # 当前K线收盘价至少深入前一根K线实体的50%
        cover_level = (prev['close'] - current['close']) / (prev['close'] - prev['open']) if prev['close'] != prev['open'] else 0
        deep_cover = cover_level > 0.5
        
        # 成交量确认：当前K线成交量大于前一根
        volume_confirmed = current['volume'] > prev['volume'] * 1.2
        
        return prev_bullish and current_bearish and gap_up and deep_cover and volume_confirmed


class WavePatternRecognizer:
    """波段识别器（增强版）"""
    def __init__(self, lookback_period: int = 50):
        """
        初始化波段识别器
        
        参数:
            lookback_period: 回溯周期
        """
        self.lookback_period = lookback_period
        self.price_history = deque(maxlen=lookback_period)
        self.hull_history = deque(maxlen=lookback_period)
        self.stc_history = deque(maxlen=lookback_period)
        self.swing_points = deque(maxlen=20)  # 存储最近的20个摆动点
        
        # 增强波段识别
        self.min_swing_size = 0  # 最小摆动幅度，基于ATR动态计算
        self.confirmed_swings = deque(maxlen=5)  # 待确认的摆动点
        self.atr = deque(maxlen=14)  # ATR计算
        
        # 市场状态跟踪
        self.volatility_level = 'normal'  # 'low', 'normal', 'high'
        self.trend_strength = 0.0  # 趋势强度
        
        self.current_wave = {
            'type': 'unknown',  # 'uptrend', 'downtrend', 'consolidation', 'unknown'
            'start_price': 0,
            'start_time': None,
            'duration': 0,
            'strength': 0,
            'swing_count': 0
        }
        self.waves = deque(maxlen=10)  # 存储最近的10个波段
    
    def update(self, price: float, hull_value: float, stc_value: float, timestamp=None) -> None:
        """
        更新波段识别器
        
        参数:
            price: 价格
            hull_value: HULL值
            stc_value: STC值
            timestamp: 时间戳
        """
        # 添加新数据
        self.price_history.append(price)
        self.hull_history.append(hull_value)
        self.stc_history.append(stc_value)
        
        # 更新ATR
        if len(self.price_history) >= 2:
            tr = abs(price - self.price_history[-2])
            self.atr.append(tr if not self.atr else (self.atr[-1] * 13 + tr) / 14)
            
            # 更新最小摆动幅度，基于ATR
            self.min_swing_size = 0.5 * self.atr[-1] if self.atr else 0
        
        # 更新波动率水平
        if len(self.atr) > 0:
            avg_price = sum(list(self.price_history)[-5:]) / 5 if len(self.price_history) >= 5 else price
            volatility_ratio = self.atr[-1] / avg_price if avg_price > 0 else 0
            
            if volatility_ratio > 0.015:
                self.volatility_level = 'high'
            elif volatility_ratio < 0.005:
                self.volatility_level = 'low'
            else:
                self.volatility_level = 'normal'
        
        # 更新趋势强度，使用Hull斜率
        if len(self.hull_history) >= 5:
            hull_slope = (self.hull_history[-1] - self.hull_history[-5]) / 5
            avg_price = sum(list(self.price_history)[-5:]) / 5
            self.trend_strength = hull_slope / avg_price if avg_price > 0 else 0
        
        # 至少需要10个数据点才能开始识别
        if len(self.price_history) < 10:
            return
        
        # 识别摆动点
        self._identify_swing_points()
        
        # 识别当前波段
        self._identify_current_wave(timestamp)
    
    def _identify_swing_points(self) -> None:
        """识别价格摆动点（高点和低点）- 增强版"""
        # 需要至少5个点来识别摆动点
        if len(self.price_history) < 5:
            return
        
        prices = list(self.price_history)
        idx = -3  # 检查倒数第3个点
        
        # 局部高点: 前后两个点都比它低
        if prices[idx] > prices[idx-1] and prices[idx] > prices[idx-2] and \
           prices[idx] > prices[idx+1] and prices[idx] > prices[idx+2]:
            
            # 振幅过滤 - 确保摆动幅度足够大
            price_diff = min(prices[idx] - prices[idx-1], prices[idx] - prices[idx+2])
            if price_diff >= self.min_swing_size:
                
                # 时间确认 - 添加到待确认摆动点
                potential_high = {
                    'type': 'high',
                    'price': prices[idx],
                    'index': len(prices) + idx,
                    'confirmed': False
                }
                
                # 检查是否已经有这个点在待确认列表中
                is_new_point = True
                for point in self.confirmed_swings:
                    if abs(point['index'] - potential_high['index']) <= 2:
                        is_new_point = False
                        break
                
                if is_new_point:
                    self.confirmed_swings.append(potential_high)
                
                # 如果这个点已经在待确认列表中待了2个bar，则添加到摆动点列表
                for point in list(self.confirmed_swings):
                    if point['type'] == 'high' and point['index'] == potential_high['index'] - 2:
                        if not self.swing_points or self.swing_points[-1]['type'] != 'high':
                            self.swing_points.append({
                                'type': 'high',
                                'price': point['price'],
                                'index': point['index'],
                                'confirmed': True
                            })
        
        # 局部低点: 前后两个点都比它高
        elif prices[idx] < prices[idx-1] and prices[idx] < prices[idx-2] and \
             prices[idx] < prices[idx+1] and prices[idx] < prices[idx+2]:
            
            # 振幅过滤 - 确保摆动幅度足够大
            price_diff = min(prices[idx-1] - prices[idx], prices[idx+2] - prices[idx])
            if price_diff >= self.min_swing_size:
                
                # 时间确认 - 添加到待确认摆动点
                potential_low = {
                    'type': 'low',
                    'price': prices[idx],
                    'index': len(prices) + idx,
                    'confirmed': False
                }
                
                # 检查是否已经有这个点在待确认列表中
                is_new_point = True
                for point in self.confirmed_swings:
                    if abs(point['index'] - potential_low['index']) <= 2:
                        is_new_point = False
                        break
                
                if is_new_point:
                    self.confirmed_swings.append(potential_low)
                
                # 如果这个点已经在待确认列表中待了2个bar，则添加到摆动点列表
                for point in list(self.confirmed_swings):
                    if point['type'] == 'low' and point['index'] == potential_low['index'] - 2:
                        if not self.swing_points or self.swing_points[-1]['type'] != 'low':
                            self.swing_points.append({
                                'type': 'low',
                                'price': point['price'],
                                'index': point['index'],
                                'confirmed': True
                            })
    
    def _identify_current_wave(self, timestamp) -> None:
        """
        识别当前波段（增强版）
        
        参数:
            timestamp: 时间戳
        """
        if len(self.swing_points) < 2:
            return
        
        # 获取最近的摆动点
        recent_swings = list(self.swing_points)[-4:]
        
        # 计算波段类型
        if len(recent_swings) >= 2:
            # 检查最近的两个摆动点
            last_swing = recent_swings[-1]
            prev_swing = recent_swings[-2]
            
            # 基本波段类型识别
            if last_swing['type'] == 'high' and prev_swing['type'] == 'low':
                wave_type = 'uptrend'
                strength = (last_swing['price'] - prev_swing['price']) / prev_swing['price']
            elif last_swing['type'] == 'low' and prev_swing['type'] == 'high':
                wave_type = 'downtrend'
                strength = (prev_swing['price'] - last_swing['price']) / prev_swing['price']
            else:
                wave_type = 'consolidation'
                strength = abs(last_swing['price'] - prev_swing['price']) / prev_swing['price']
            
            # 应用市场状态调整
            
            # 1. 波动率过滤 - 高波动率环境下需要更强的波动才能确认趋势
            if self.volatility_level == 'high':
                # 高波动率环境下，要求更强的波动才能确认趋势
                strength_threshold = 0.02  # 2%最小波动
                if strength < strength_threshold:
                    wave_type = 'consolidation'  # 如果波动不够强，降级为盘整
            
            # 2. 趋势强度整合 - 使用Hull斜率确认趋势方向
            if abs(self.trend_strength) > 0.001:  # 有明显趋势
                # 如果波段方向与趋势方向相反，降低置信度
                if (wave_type == 'uptrend' and self.trend_strength < 0) or \
                   (wave_type == 'downtrend' and self.trend_strength > 0):
                    # 波段与趋势方向相反 - 降低强度
                    strength = strength * 0.7
                else:
                    # 波段与趋势方向一致 - 增强强度
                    strength = strength * 1.3
            
            # 3. 市场状态适应
            # 在低波动率环境下，更容易确认盘整
            if self.volatility_level == 'low' and strength < 0.01:
                wave_type = 'consolidation'
            
            # 更新当前波段
            if wave_type != self.current_wave['type']:
                # 保存前一个波段
                if self.current_wave['type'] != 'unknown':
                    self.waves.append(self.current_wave.copy())
                
                # 创建新波段
                self.current_wave = {
                    'type': wave_type,
                    'start_price': prev_swing['price'],
                    'start_time': timestamp,
                    'duration': last_swing['index'] - prev_swing['index'],
                    'strength': strength,
                    'swing_count': 1,
                    'trend_alignment': 1.0 if (wave_type == 'uptrend' and self.trend_strength > 0) or 
                                            (wave_type == 'downtrend' and self.trend_strength < 0) else 0.5
                }
            else:
                # 更新现有波段
                self.current_wave['duration'] = last_swing['index'] - prev_swing['index'] + self.current_wave['duration']
                self.current_wave['strength'] = (self.current_wave['strength'] + strength) / 2
                self.current_wave['swing_count'] += 1
    
    def get_wave_info(self) -> Dict:
        """
        获取当前波段信息（增强版）
        
        返回:
            波段信息字典
        """
        # 基础波段信息
        wave_info = {
            'type': self.current_wave['type'],
            'strength': self.current_wave['strength'],
            'duration': self.current_wave['duration'],
            'swing_count': self.current_wave['swing_count'],
            
            # 增强信息
            'market_regime': self.volatility_level,
            'trend_strength': self.trend_strength,
            'trend_alignment': self.current_wave.get('trend_alignment', 0.5)
        }
        
        # 根据市场状态调整波段置信度
        confidence = self.current_wave['strength']
        
        # 在高波动率环境下，需要更强的信号
        if self.volatility_level == 'high':
            if self.current_wave['type'] != 'consolidation':
                # 非盘整状态需要更高的强度
                confidence = confidence * 0.8
        
        # 在低波动率环境下，盘整信号更可靠
        elif self.volatility_level == 'low':
            if self.current_wave['type'] == 'consolidation':
                confidence = confidence * 1.2
        
        # 趋势对齐增强置信度
        if (self.current_wave['type'] == 'uptrend' and self.trend_strength > 0) or \
           (self.current_wave['type'] == 'downtrend' and self.trend_strength < 0):
            confidence = confidence * 1.2
        
        wave_info['confidence'] = min(1.0, max(0.1, confidence))
        return wave_info
    
    def is_ready(self) -> bool:
        """
        检查波段识别器是否就绪
        
        返回:
            是否就绪
        """
        return len(self.swing_points) >= 2# 
#==================== 自适应系统组件 ====================

class MarketRegimeClassifier:
    """市场状态分类器"""
    def __init__(self, lookback_period: int = 50):
        """
        初始化市场状态分类器
        
        参数:
            lookback_period: 回溯周期
        """
        self.lookback_period = lookback_period
        self.price_history = deque(maxlen=lookback_period)
        self.volume_history = deque(maxlen=lookback_period)
        self.regime_history = deque(maxlen=10)  # 存储最近的市场状态
        self.volatility_window = deque(maxlen=20)  # 波动率计算窗口
        
    def update(self, price: float, volume: float = 0) -> None:
        """
        更新市场数据
        
        参数:
            price: 价格
            volume: 成交量
        """
        self.price_history.append(price)
        self.volume_history.append(volume)
        
        # 计算最新波动率
        if len(self.price_history) >= 2:
            latest_return = abs(self.price_history[-1] / self.price_history[-2] - 1)
            self.volatility_window.append(latest_return)
    
    def classify_regime(self) -> Tuple[str, float, float]:
        """
        分类市场状态
        
        返回:
            (市场状态, 波动率水平, 趋势强度)
        """
        if len(self.price_history) < 20:
            return "unknown", 0.0, 0.0
        
        # 计算波动率
        volatility = np.std(list(self.volatility_window)) * np.sqrt(252) if self.volatility_window else 0.0
        
        # 计算趋势强度
        prices = np.array(list(self.price_history))
        x = np.arange(len(prices))
        if len(x) > 1:
            slope, _ = np.polyfit(x, prices, 1)
            trend_strength = abs(slope) / np.mean(prices) * 100  # 归一化趋势强度
        else:
            trend_strength = 0.0
        
        # 分类市场状态
        if volatility > 0.03:  # 高波动
            if trend_strength > 0.5:
                regime = "volatile_trending"
            else:
                regime = "volatile_ranging"
        else:  # 低波动
            if trend_strength > 0.3:
                regime = "trending"
            else:
                regime = "ranging"
        
        # 更新市场状态历史
        self.regime_history.append(regime)
        
        return regime, volatility, trend_strength
    
    def get_regime_stability(self) -> float:
        """
        获取市场状态稳定性
        
        返回:
            稳定性得分 (0-1)
        """
        if len(self.regime_history) < 5:
            return 0.5
        
        # 计算最近状态的一致性
        regimes = list(self.regime_history)
        most_common = max(set(regimes), key=regimes.count)
        stability = regimes.count(most_common) / len(regimes)
        
        return stability


class AdaptiveWeightAllocator:
    """自适应权重分配器"""
    def __init__(self, adaptation_speed: float = 0.1):
        """
        初始化自适应权重分配器
        
        参数:
            adaptation_speed: 适应速度 (0-1)
        """
        self.adaptation_speed = adaptation_speed
        self.base_weights = {
            'technical': 0.25, 
            'pattern': 0.25, 
            'wave': 0.25, 
            'fuzzy': 0.25
        }
        
        # 不同市场状态下的权重配置
        self.regime_specific_weights = {
            'trending': {'wave': 0.4, 'technical': 0.3, 'pattern': 0.2, 'fuzzy': 0.1},
            'ranging': {'pattern': 0.4, 'fuzzy': 0.3, 'technical': 0.2, 'wave': 0.1},
            'volatile_trending': {'technical': 0.4, 'fuzzy': 0.3, 'wave': 0.2, 'pattern': 0.1},
            'volatile_ranging': {'fuzzy': 0.4, 'pattern': 0.3, 'technical': 0.2, 'wave': 0.1},
            'unknown': self.base_weights
        }
        
        self.current_weights = self.base_weights.copy()
        self.performance_history = {
            'technical': deque(maxlen=50),
            'pattern': deque(maxlen=50),
            'wave': deque(maxlen=50),
            'fuzzy': deque(maxlen=50)
        }
    
    def update_weights(self, market_regime: str, module_performance: Dict[str, float]) -> Dict[str, float]:
        """
        更新权重
        
        参数:
            market_regime: 市场状态
            module_performance: 各模块性能
            
        返回:
            更新后的权重
        """
        # 获取市场状态特定权重
        target_weights = self.regime_specific_weights.get(market_regime, self.base_weights).copy()
        
        # 更新性能历史
        for module, performance in module_performance.items():
            if module in self.performance_history:
                self.performance_history[module].append(performance)
        
        # 基于性能调整权重
        performance_adjustments = {}
        total_performance = sum(module_performance.values())
        
        if total_performance > 0:
            for module, performance in module_performance.items():
                # 性能占比作为权重调整因子
                performance_adjustments[module] = performance / total_performance
        else:
            # 如果没有性能数据，使用均匀分布
            for module in module_performance:
                performance_adjustments[module] = 1.0 / len(module_performance)
        
        # 应用性能调整
        adjusted_weights = {}
        for module in target_weights:
            if module in performance_adjustments:
                # 混合市场状态权重和性能权重
                adjusted_weights[module] = (
                    target_weights[module] * 0.7 + 
                    performance_adjustments[module] * 0.3
                )
            else:
                adjusted_weights[module] = target_weights[module]
        
        # 归一化权重
        total_weight = sum(adjusted_weights.values())
        if total_weight > 0:
            for module in adjusted_weights:
                adjusted_weights[module] /= total_weight
        
        # 平滑过渡到新权重
        for module in self.current_weights:
            if module in adjusted_weights:
                self.current_weights[module] = (
                    (1 - self.adaptation_speed) * self.current_weights[module] + 
                    self.adaptation_speed * adjusted_weights[module]
                )
        
        return self.current_weights
    
    def get_current_weights(self) -> Dict[str, float]:
        """
        获取当前权重
        
        返回:
            当前权重
        """
        return self.current_weights


class SignalFusionEngine:
    """信号融合引擎"""
    def __init__(self):
        """初始化信号融合引擎"""
        self.signal_history = {
            'technical': deque(maxlen=10),
            'pattern': deque(maxlen=10),
            'wave': deque(maxlen=10),
            'fuzzy': deque(maxlen=10)
        }
        self.signal_decay_factor = 0.9  # 信号衰减因子
        self.consistency_threshold = 0.7  # 一致性阈值
    
    def fuse_signals(self, current_signals: Dict[str, float], weights: Dict[str, float]) -> Tuple[float, float]:
        """
        融合信号
        
        参数:
            current_signals: 当前各模块信号
            weights: 各模块权重
            
        返回:
            (综合信号强度, 置信度)
        """
        # 更新信号历史
        for module, signal in current_signals.items():
            if module in self.signal_history:
                self.signal_history[module].append(signal)
        
        # 计算信号一致性
        signal_directions = {}
        for module, signal in current_signals.items():
            signal_directions[module] = 1 if signal > 0 else (-1 if signal < 0 else 0)
        
        # 检查信号方向一致性
        directions = list(signal_directions.values())
        if not directions:
            return 0.0, 0.0
            
        positive_count = directions.count(1)
        negative_count = directions.count(-1)
        neutral_count = directions.count(0)
        
        total_count = len(directions)
        if total_count == 0:
            return 0.0, 0.0
            
        # 计算一致性得分
        if positive_count > negative_count:
            consistency = positive_count / total_count
            dominant_direction = 1
        elif negative_count > positive_count:
            consistency = negative_count / total_count
            dominant_direction = -1
        else:
            consistency = neutral_count / total_count
            dominant_direction = 0
        
        # 应用权重融合信号
        weighted_signal = 0.0
        total_weight = 0.0
        
        for module, signal in current_signals.items():
            if module in weights:
                module_weight = weights[module]
                # 如果信号方向与主导方向一致，增强权重
                if signal_directions[module] == dominant_direction:
                    module_weight *= 1.2
                
                weighted_signal += signal * module_weight
                total_weight += module_weight
        
        if total_weight > 0:
            final_signal = weighted_signal / total_weight
        else:
            final_signal = 0.0
        
        # 计算置信度
        confidence = consistency * min(1.0, total_weight)
        
        return final_signal, confidence
    
    def handle_conflicts(self, signals: Dict[str, float], performance: Dict[str, float]) -> Dict[str, float]:
        """
        处理信号冲突
        
        参数:
            signals: 各模块信号
            performance: 各模块性能
            
        返回:
            调整后的信号
        """
        # 检测信号冲突
        signal_directions = {}
        for module, signal in signals.items():
            signal_directions[module] = 1 if signal > 0 else (-1 if signal < 0 else 0)
        
        # 计算方向分布
        direction_counts = {1: 0, -1: 0, 0: 0}
        for direction in signal_directions.values():
            direction_counts[direction] += 1
        
        # 如果没有明显冲突，返回原始信号
        max_count = max(direction_counts.values())
        if max_count >= len(signals) * 0.7:  # 70%一致
            return signals
        
        # 处理冲突 - 基于历史表现调整权重
        adjusted_signals = {}
        for module, signal in signals.items():
            if module in performance:
                # 根据模块性能调整信号强度
                performance_factor = performance.get(module, 0.5)
                adjusted_signals[module] = signal * performance_factor
            else:
                adjusted_signals[module] = signal * 0.5  # 默认减半
        
        return adjusted_signals
    
    def calculate_signal_strength(self, raw_signal: float, confidence: float) -> float:
        """
        计算最终信号强度
        
        参数:
            raw_signal: 原始信号
            confidence: 置信度
            
        返回:
            调整后的信号强度
        """
        # 应用置信度调整信号强度
        adjusted_signal = raw_signal * confidence
        
        # 信号强度限制在[-1, 1]范围内
        return max(-1.0, min(1.0, adjusted_signal))


class ModuleCorrelationAnalyzer:
    """模块相关性分析器 - 避免因子冗余"""
    def __init__(self, window_size: int = 50):
        """
        初始化模块相关性分析器

        参数:
            window_size: 分析窗口大小
        """
        self.window_size = window_size
        self.signal_history = {
            'technical': deque(maxlen=window_size),
            'pattern': deque(maxlen=window_size),
            'wave': deque(maxlen=window_size),
            'fuzzy': deque(maxlen=window_size)
        }
        self.correlation_matrix = {}
        self.redundancy_threshold = 0.8  # 相关性阈值

    def update_signals(self, signals: Dict[str, float]) -> None:
        """
        更新信号历史

        参数:
            signals: 各模块信号
        """
        for module, signal in signals.items():
            if module in self.signal_history:
                self.signal_history[module].append(signal)

    def calculate_correlations(self) -> Dict[str, float]:
        """
        计算模块间相关性

        返回:
            相关性矩阵
        """
        modules = list(self.signal_history.keys())
        correlations = {}

        # 检查是否有足够的数据
        min_data_length = min(len(history) for history in self.signal_history.values())
        if min_data_length < 10:  # 至少需要10个数据点
            return correlations

        # 计算两两相关性
        for i, module1 in enumerate(modules):
            for j, module2 in enumerate(modules):
                if i < j:  # 避免重复计算
                    key = f"{module1}_{module2}"

                    # 获取信号数据
                    signals1 = list(self.signal_history[module1])
                    signals2 = list(self.signal_history[module2])

                    # 计算皮尔逊相关系数
                    correlation = self._calculate_correlation(signals1, signals2)
                    correlations[key] = correlation

        self.correlation_matrix = correlations
        return correlations

    def _calculate_correlation(self, x: List[float], y: List[float]) -> float:
        """
        计算皮尔逊相关系数

        参数:
            x: 第一个信号序列
            y: 第二个信号序列

        返回:
            相关系数
        """
        if len(x) != len(y) or len(x) < 2:
            return 0.0

        # 计算均值
        mean_x = sum(x) / len(x)
        mean_y = sum(y) / len(y)

        # 计算协方差和方差
        covariance = sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(len(x)))
        variance_x = sum((x[i] - mean_x) ** 2 for i in range(len(x)))
        variance_y = sum((y[i] - mean_y) ** 2 for i in range(len(y)))

        # 计算相关系数
        if variance_x == 0 or variance_y == 0:
            return 0.0

        correlation = covariance / (math.sqrt(variance_x) * math.sqrt(variance_y))
        return max(-1.0, min(1.0, correlation))  # 限制在[-1, 1]范围内

    def detect_redundancy(self) -> Tuple[float, List[str]]:
        """
        检测因子冗余

        返回:
            (冗余度得分, 冗余模块对列表)
        """
        correlations = self.calculate_correlations()
        redundant_pairs = []
        total_correlation = 0.0

        for pair, correlation in correlations.items():
            if abs(correlation) > self.redundancy_threshold:
                redundant_pairs.append(pair)
            total_correlation += abs(correlation)

        # 计算整体冗余度
        redundancy_score = total_correlation / len(correlations) if correlations else 0.0

        return redundancy_score, redundant_pairs

    def get_adjusted_weights(self, base_weights: Dict[str, float]) -> Dict[str, float]:
        """
        基于相关性调整权重，降低冗余模块的权重

        参数:
            base_weights: 基础权重

        返回:
            调整后的权重
        """
        adjusted_weights = base_weights.copy()
        correlations = self.correlation_matrix

        # 对高相关性的模块降低权重
        for pair, correlation in correlations.items():
            if abs(correlation) > self.redundancy_threshold:
                modules = pair.split('_')
                if len(modules) == 2:
                    module1, module2 = modules

                    # 降低权重，保持总权重不变
                    reduction_factor = 0.8  # 降低20%
                    if module1 in adjusted_weights:
                        adjusted_weights[module1] *= reduction_factor
                    if module2 in adjusted_weights:
                        adjusted_weights[module2] *= reduction_factor

        # 重新归一化权重
        total_weight = sum(adjusted_weights.values())
        if total_weight > 0:
            for module in adjusted_weights:
                adjusted_weights[module] /= total_weight

        return adjusted_weights


class DynamicThresholdOptimizer:
    """动态阈值优化器 - 根据市场波动率自动缩放阈值"""
    def __init__(self, base_signal_threshold: float = 0.6,
                 base_confidence_threshold: float = 0.7,
                 adaptation_speed: float = 0.05):
        """
        初始化动态阈值优化器

        参数:
            base_signal_threshold: 基础信号阈值
            base_confidence_threshold: 基础置信度阈值
            adaptation_speed: 适应速度
        """
        self.base_signal_threshold = base_signal_threshold
        self.base_confidence_threshold = base_confidence_threshold
        self.adaptation_speed = adaptation_speed

        # 当前阈值
        self.current_signal_threshold = base_signal_threshold
        self.current_confidence_threshold = base_confidence_threshold

        # 市场状态跟踪
        self.volatility_history = deque(maxlen=20)
        self.performance_history = deque(maxlen=50)

        # 阈值调整历史
        self.threshold_history = deque(maxlen=30)

    def update_market_state(self, volatility: float, trend_strength: float) -> None:
        """
        更新市场状态

        参数:
            volatility: 当前波动率
            trend_strength: 趋势强度
        """
        self.volatility_history.append(volatility)

        # 计算阈值调整因子
        adjustment_factor = self._calculate_adjustment_factor(volatility, trend_strength)

        # 平滑调整阈值
        target_signal_threshold = self.base_signal_threshold * adjustment_factor
        target_confidence_threshold = self.base_confidence_threshold * adjustment_factor

        # 使用指数移动平均平滑过渡
        self.current_signal_threshold = (
            (1 - self.adaptation_speed) * self.current_signal_threshold +
            self.adaptation_speed * target_signal_threshold
        )

        self.current_confidence_threshold = (
            (1 - self.adaptation_speed) * self.current_confidence_threshold +
            self.adaptation_speed * target_confidence_threshold
        )

        # 限制阈值范围
        self.current_signal_threshold = max(0.3, min(0.9, self.current_signal_threshold))
        self.current_confidence_threshold = max(0.4, min(0.9, self.current_confidence_threshold))

        # 记录阈值历史
        self.threshold_history.append({
            'signal_threshold': self.current_signal_threshold,
            'confidence_threshold': self.current_confidence_threshold,
            'adjustment_factor': adjustment_factor,
            'volatility': volatility
        })

    def _calculate_adjustment_factor(self, volatility: float, trend_strength: float) -> float:
        """
        计算阈值调整因子

        参数:
            volatility: 波动率
            trend_strength: 趋势强度

        返回:
            调整因子
        """
        # 基础调整因子
        base_factor = 1.0

        # 波动率调整：高波动率时提高阈值，低波动率时降低阈值
        if volatility > 0.03:  # 高波动率
            volatility_factor = 1.2 + (volatility - 0.03) * 5  # 最高1.5倍
        elif volatility < 0.01:  # 低波动率
            volatility_factor = 0.8 - (0.01 - volatility) * 10  # 最低0.5倍
        else:  # 正常波动率
            volatility_factor = 1.0

        # 趋势强度调整：强趋势时降低阈值，弱趋势时提高阈值
        if abs(trend_strength) > 0.02:  # 强趋势
            trend_factor = 0.9 - abs(trend_strength) * 5  # 最低0.7倍
        elif abs(trend_strength) < 0.005:  # 弱趋势
            trend_factor = 1.1 + (0.005 - abs(trend_strength)) * 20  # 最高1.2倍
        else:  # 中等趋势
            trend_factor = 1.0

        # 综合调整因子
        adjustment_factor = base_factor * volatility_factor * trend_factor

        # 限制调整范围
        return max(0.5, min(1.5, adjustment_factor))

    def update_performance(self, trade_result: float, signal_strength: float, confidence: float) -> None:
        """
        更新交易性能，用于阈值优化

        参数:
            trade_result: 交易结果
            signal_strength: 信号强度
            confidence: 置信度
        """
        self.performance_history.append({
            'result': trade_result,
            'signal_strength': signal_strength,
            'confidence': confidence,
            'signal_threshold': self.current_signal_threshold,
            'confidence_threshold': self.current_confidence_threshold
        })

        # 基于性能反馈微调阈值
        self._optimize_thresholds_by_performance()

    def _optimize_thresholds_by_performance(self) -> None:
        """基于历史性能优化阈值"""
        if len(self.performance_history) < 10:
            return

        # 分析最近的交易性能
        recent_trades = list(self.performance_history)[-10:]
        profitable_trades = [t for t in recent_trades if t['result'] > 0]

        if len(recent_trades) == 0:
            return

        success_rate = len(profitable_trades) / len(recent_trades)

        # 如果成功率过低，提高阈值
        if success_rate < 0.4:
            self.base_signal_threshold = min(0.8, self.base_signal_threshold + 0.01)
            self.base_confidence_threshold = min(0.8, self.base_confidence_threshold + 0.01)
        # 如果成功率很高，可以适当降低阈值以增加交易频率
        elif success_rate > 0.7:
            self.base_signal_threshold = max(0.4, self.base_signal_threshold - 0.005)
            self.base_confidence_threshold = max(0.5, self.base_confidence_threshold - 0.005)

    def get_current_thresholds(self) -> Tuple[float, float]:
        """
        获取当前阈值

        返回:
            (信号阈值, 置信度阈值)
        """
        return self.current_signal_threshold, self.current_confidence_threshold

    def should_trade(self, signal_strength: float, confidence: float) -> bool:
        """
        判断是否应该交易

        参数:
            signal_strength: 信号强度
            confidence: 置信度

        返回:
            是否应该交易
        """
        return (abs(signal_strength) >= self.current_signal_threshold and
                confidence >= self.current_confidence_threshold)


class PerformanceTracker:
    """性能跟踪器"""
    def __init__(self, memory_length: int = 100):
        """
        初始化性能跟踪器
        
        参数:
            memory_length: 记忆长度
        """
        self.memory_length = memory_length
        self.trade_history = deque(maxlen=memory_length)
        self.module_performance = {
            'technical': {'correct': 0, 'total': 0},
            'pattern': {'correct': 0, 'total': 0},
            'wave': {'correct': 0, 'total': 0},
            'fuzzy': {'correct': 0, 'total': 0}
        }
        self.market_performance = {
            'trending': {'correct': 0, 'total': 0},
            'ranging': {'correct': 0, 'total': 0},
            'volatile_trending': {'correct': 0, 'total': 0},
            'volatile_ranging': {'correct': 0, 'total': 0}
        }
    
    def record_trade(self, trade_result: float, signals: Dict[str, float], weights: Dict[str, float], market_regime: str) -> None:
        """
        记录交易结果
        
        参数:
            trade_result: 交易结果 (正值为盈利，负值为亏损)
            signals: 各模块信号
            weights: 各模块权重
            market_regime: 市场状态
        """
        # 记录交易
        self.trade_history.append({
            'result': trade_result,
            'signals': signals.copy(),
            'weights': weights.copy(),
            'market_regime': market_regime,
            'timestamp': time.time()
        })
        
        # 更新各模块表现
        is_profitable = trade_result > 0
        
        for module, signal in signals.items():
            if module in self.module_performance:
                # 信号方向与结果一致视为正确
                signal_correct = (signal > 0 and trade_result > 0) or (signal < 0 and trade_result < 0)
                
                self.module_performance[module]['total'] += 1
                if signal_correct:
                    self.module_performance[module]['correct'] += 1
        
        # 更新市场状态表现
        if market_regime in self.market_performance:
            self.market_performance[market_regime]['total'] += 1
            if is_profitable:
                self.market_performance[market_regime]['correct'] += 1
    
    def evaluate_module_performance(self) -> Dict[str, float]:
        """
        评估各模块表现
        
        返回:
            各模块准确率
        """
        performance = {}
        
        for module, stats in self.module_performance.items():
            if stats['total'] > 0:
                performance[module] = stats['correct'] / stats['total']
            else:
                performance[module] = 0.5  # 默认值
        
        return performance
    
    def evaluate_market_performance(self) -> Dict[str, float]:
        """
        评估各市场状态表现
        
        返回:
            各市场状态准确率
        """
        performance = {}
        
        for regime, stats in self.market_performance.items():
            if stats['total'] > 0:
                performance[regime] = stats['correct'] / stats['total']
            else:
                performance[regime] = 0.5  # 默认值
        
        return performance
    
    def get_optimization_suggestions(self) -> Dict[str, Any]:
        """
        获取优化建议
        
        返回:
            优化建议
        """
        module_performance = self.evaluate_module_performance()
        market_performance = self.evaluate_market_performance()
        
        # 找出表现最好和最差的模块
        best_module = max(module_performance.items(), key=lambda x: x[1])[0] if module_performance else None
        worst_module = min(module_performance.items(), key=lambda x: x[1])[0] if module_performance else None
        
        # 找出表现最好和最差的市场状态
        best_regime = max(market_performance.items(), key=lambda x: x[1])[0] if market_performance else None
        worst_regime = min(market_performance.items(), key=lambda x: x[1])[0] if market_performance else None
        
        suggestions = {
            'best_module': best_module,
            'worst_module': worst_module,
            'best_regime': best_regime,
            'worst_regime': worst_regime,
            'module_performance': module_performance,
            'market_performance': market_performance
        }
        
        return suggestions# =====
#=========== 主策略类 ==================

class Strategy1(BaseStrategy):
    """Strategy1 - 自适应综合交易策略"""
    def __init__(self) -> None:
        super().__init__()
        self.params_map = Params()
        self.state_map = State()

        # 信号标志
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False

        # 当前行情
        self.tick = None
        self.order_id = None
        self.signal_price = 0
        
        # 技术指标
        self.hull_calculators = {
            9: HullMovingAverage(9),
            21: HullMovingAverage(21),
            55: HullMovingAverage(55)
        }
        self.stc_calculator = None
        self.pattern_recognizer = None
        self.wave_recognizer = None
        self.fuzzy_system = None
        
        # 自适应系统组件
        self.market_classifier = None
        self.weight_allocator = None
        self.signal_fusion = None
        self.performance_tracker = None

        # 新增：模块相关性分析和动态阈值优化组件
        self.correlation_analyzer = None
        self.threshold_optimizer = None

        # 历史数据
        self.hull_prev = 0
        self.stc_prev = 0

        # 交易记录
        self.last_trade_price = 0
        self.last_trade_time = 0
        self.trade_count = 0

        # 技术指标历史
        self.technical_history = {
            'hull': deque(maxlen=10),
            'stc': deque(maxlen=10),
            'rsi': deque(maxlen=10)
        }
        self.avg_volume = deque(maxlen=30)

        # 新增：信号历史用于相关性分析
        self.signal_history = {
            'technical': deque(maxlen=50),
            'pattern': deque(maxlen=50),
            'wave': deque(maxlen=50),
            'fuzzy': deque(maxlen=50)
        }

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标，输出多周期HULL，兼容HULL_MA字段"""
        # 获取HULL值，确保返回有效数值而不是None
        hull9_value = 0.0
        hull21_value = 0.0
        hull55_value = 0.0

        if self.hull_calculators[9].is_ready():
            hull_result = self.hull_calculators[9].get_hull()
            hull9_value = hull_result[0] if hull_result[0] is not None else 0.0

        if self.hull_calculators[21].is_ready():
            hull_result = self.hull_calculators[21].get_hull()
            hull21_value = hull_result[0] if hull_result[0] is not None else 0.0

        if self.hull_calculators[55].is_ready():
            hull_result = self.hull_calculators[55].get_hull()
            hull55_value = hull_result[0] if hull_result[0] is not None else 0.0

        return {
            'HULL9': hull9_value,
            'HULL21': hull21_value,
            'HULL55': hull55_value,
            'HULL_MA': hull9_value,  # 兼容字段，使用HULL9的值
            'STC': float(self.state_map.stc_value) if self.state_map.stc_value is not None else 0.0,
            'STC_Signal': float(self.state_map.stc_signal) if self.state_map.stc_signal is not None else 0.0,
        }

    @property
    def main_indicator(self):
        """主图指标字段列表，供UI自动识别"""
        return ['HULL9', 'HULL21', 'HULL55', 'HULL_MA', 'STC', 'STC_Signal']

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标"""
        return {
            "Confidence": float(self.state_map.signal_confidence) if self.state_map.signal_confidence is not None else 0.0,
            "WaveStrength": float(self.state_map.wave_strength) if self.state_map.wave_strength is not None else 0.0,
            "PatternStrength": float(self.state_map.pattern_strength) if self.state_map.pattern_strength is not None else 0.0,
            "IntegratedSignal": float(self.state_map.integrated_signal) if self.state_map.integrated_signal is not None else 0.0,
            "SignalThreshold": float(self.state_map.current_signal_threshold) if self.state_map.current_signal_threshold is not None else 0.6,
            "ConfidenceThreshold": float(self.state_map.current_confidence_threshold) if self.state_map.current_confidence_threshold is not None else 0.7,
            "RedundancyScore": float(self.state_map.redundancy_score) if self.state_map.redundancy_score is not None else 0.0,
            "ThresholdAdjustment": float(self.state_map.threshold_adjustment_factor) if self.state_map.threshold_adjustment_factor is not None else 1.0
        }

    @property
    def sub_indicator(self):
        """副图指标字段列表，供UI自动识别"""
        return [
            "Confidence",
            "WaveStrength",
            "PatternStrength",
            "IntegratedSignal",
            "SignalThreshold",
            "ConfidenceThreshold",
            "RedundancyScore",
            "ThresholdAdjustment"
        ]



    def on_tick(self, tick: TickData) -> None:
        """
        Tick数据回调
        
        参数:
            tick: Tick数据
        """
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)

    def on_order_cancel(self, order: OrderData) -> None:
        """
        撤单推送回调
        
        参数:
            order: 订单数据
        """
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """
        成交回调
        
        参数:
            trade: 成交数据
            log: 是否记录日志
        """
        super().on_trade(trade, log)
        self.order_id = None
        
        # 记录交易
        trade_result = 0.0
        if self.last_trade_price > 0:
            if trade.direction == "buy":
                trade_result = self.last_trade_price - trade.price
            else:
                trade_result = trade.price - self.last_trade_price
        
        self.last_trade_price = trade.price
        self.last_trade_time = time.time()
        self.trade_count += 1
        
        # 记录性能
        if self.performance_tracker and trade_result != 0:
            # 获取当前模块信号
            signals = {
                'technical': 1.0 if self.hull_calculators[9].get_trend() == "BULLISH" else -1.0,
                'pattern': self.state_map.pattern_strength if self.state_map.pattern_detected in ['bullish_engulfing', 'hammer'] else -self.state_map.pattern_strength,
                'wave': 1.0 if self.state_map.wave_type == 'uptrend' else -1.0,
                'fuzzy': 1.0 if self.state_map.action_level == "Aggressive" else (-1.0 if self.state_map.action_level == "Stop" else 0.0)
            }
            
            # 记录交易结果
            self.performance_tracker.record_trade(
                trade_result,
                signals,
                self.state_map.adaptive_weights,
                self.state_map.market_regime
            )
            
            # 更新模块性能
            module_performance = self.performance_tracker.evaluate_module_performance()
            self.state_map.technical_accuracy = module_performance.get('technical', 0.5)
            self.state_map.pattern_accuracy = module_performance.get('pattern', 0.5)
            self.state_map.wave_accuracy = module_performance.get('wave', 0.5)
            self.state_map.fuzzy_accuracy = module_performance.get('fuzzy', 0.5)

        # 新增：更新动态阈值优化器
        if self.threshold_optimizer and trade_result != 0:
            self.threshold_optimizer.update_performance(
                trade_result,
                self.state_map.integrated_signal,
                self.state_map.signal_confidence
            )

    def on_start(self) -> None:
        """策略启动"""
        # 先初始化技术指标和自适应系统组件
        self.hull_calculators = {
            9: HullMovingAverage(9),
            21: HullMovingAverage(21),
            55: HullMovingAverage(55)
        }
        self.stc_calculator = SchaffTrendCycle(
            self.params_map.stc_fast,
            self.params_map.stc_slow,
            self.params_map.stc_cycle
        )
        self.pattern_recognizer = CandlePatternRecognizer()
        self.wave_recognizer = WavePatternRecognizer()
        self.fuzzy_system = FuzzySystem(self.params_map.fuzzy_sensitivity)
        self.market_classifier = MarketRegimeClassifier()
        self.weight_allocator = AdaptiveWeightAllocator(self.params_map.adaptation_speed)
        self.signal_fusion = SignalFusionEngine()
        self.performance_tracker = PerformanceTracker()

        # 新增：初始化模块相关性分析器和动态阈值优化器
        self.correlation_analyzer = ModuleCorrelationAnalyzer()
        self.threshold_optimizer = DynamicThresholdOptimizer(
            self.params_map.base_signal_threshold,
            self.params_map.confidence_threshold,
            self.params_map.threshold_adaptation_speed
        )

        # 初始化K线生成器
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()

        super().on_start()

        # 重置信号
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False
        self.tick = None
        
        # 初始化自适应权重
        self.state_map.adaptive_weights = {
            'technical': self.params_map.technical_weight,
            'pattern': self.params_map.pattern_weight,
            'wave': self.params_map.wave_weight,
            'fuzzy': self.params_map.fuzzy_weight
        }

        # 初始化动态阈值状态
        self.state_map.current_signal_threshold = self.params_map.base_signal_threshold
        self.state_map.current_confidence_threshold = self.params_map.confidence_threshold
        self.state_map.threshold_adjustment_factor = 1.0

        # 初始化模块相关性状态
        self.state_map.module_correlations = {}
        self.state_map.redundancy_score = 0.0

        self.update_status_bar()
        print("多模块融合信号自适应交易策略已启动")

        # 重置技术指标历史
        self.technical_history = {
            'hull': deque(maxlen=10),
            'stc': deque(maxlen=10),
            'rsi': deque(maxlen=10)
        }
        self.avg_volume = deque(maxlen=30)

        # 重置信号历史
        self.signal_history = {
            'technical': deque(maxlen=50),
            'pattern': deque(maxlen=50),
            'wave': deque(maxlen=50),
            'fuzzy': deque(maxlen=50)
        }

        # 多周期HULL重置
        self.hull_calculators = {
            9: HullMovingAverage(9),
            21: HullMovingAverage(21),
            55: HullMovingAverage(55)
        }

        # 启动时打印HULL各周期ready状态
        for period, hull in self.hull_calculators.items():
            print(f"[启动] HULL{period} ready: {hull.is_ready()} 当前值: {hull.get_hull()[0]}")

    def on_stop(self) -> None:
        """策略停止"""
        super().on_stop()
        print("多模块融合信号自适应交易策略已停止")

        # 输出性能统计
        if self.performance_tracker:
            suggestions = self.performance_tracker.get_optimization_suggestions()
            print(f"策略优化建议:")
            print(f"- 最佳模块: {suggestions['best_module']}")
            print(f"- 最差模块: {suggestions['worst_module']}")
            print(f"- 最佳市场状态: {suggestions['best_regime']}")
            print(f"- 最差市场状态: {suggestions['worst_regime']}")
            print(f"- 模块性能: {suggestions['module_performance']}")

        # 输出模块相关性分析结果
        if self.correlation_analyzer:
            redundancy_score, redundant_pairs = self.correlation_analyzer.detect_redundancy()
            print(f"模块相关性分析:")
            print(f"- 整体冗余度: {redundancy_score:.3f}")
            print(f"- 冗余模块对: {redundant_pairs}")

        # 输出动态阈值优化结果
        if self.threshold_optimizer:
            signal_threshold, confidence_threshold = self.threshold_optimizer.get_current_thresholds()
            print(f"动态阈值优化:")
            print(f"- 最终信号阈值: {signal_threshold:.3f}")
            print(f"- 最终置信度阈值: {confidence_threshold:.3f}")

    def callback(self, kline: KLineData) -> None:
        """
        接受 K 线回调
        
        参数:
            kline: K线数据
        """
        # 更新技术指标
        self.update_indicators(kline)
        
        # 更新市场状态
        self.update_market_state()
        
        # 更新自适应权重
        self.update_adaptive_weights()
        
        # 计算信号
        self.calc_signal(kline)
        
        # 信号执行
        self.exec_signal()
        
        # 线图更新
        if hasattr(self, 'widget') and self.widget is not None:
            try:
                self.widget.recv_kline({
                    "kline": kline,
                    "signal_price": self.signal_price,
                    **self.main_indicator_data,
                    **self.sub_indicator_data
                })
            except Exception as e:
                print(f"[K线回调] 技术指标窗口更新失败: {e}")
                # 打印调试信息
                for period, hull in self.hull_calculators.items():
                    hull_value = hull.get_hull()[0] if hull.is_ready() and hull.get_hull()[0] is not None else "未就绪"
                    print(f"  HULL{period} ready: {hull.is_ready()} 当前值: {hull_value}")
        else:
            print("[K线回调] 技术指标窗口未初始化")
        
        if self.trading:
            self.update_status_bar()

    def real_time_callback(self, kline: KLineData) -> None:
        """
        实时K线回调
        
        参数:
            kline: K线数据
        """
        # 更新技术指标
        self.update_indicators(kline)
        
        # 更新市场状态
        self.update_market_state()
        
        # 线图更新
        if hasattr(self, 'widget') and self.widget is not None:
            try:
                self.widget.recv_kline({
                    "kline": kline,
                    **self.main_indicator_data,
                    **self.sub_indicator_data
                })
            except Exception as e:
                print(f"[实时K线] 技术指标窗口更新失败: {e}")
                # 打印调试信息
                for period, hull in self.hull_calculators.items():
                    hull_value = hull.get_hull()[0] if hull.is_ready() and hull.get_hull()[0] is not None else "未就绪"
                    print(f"  HULL{period} ready: {hull.is_ready()} 当前值: {hull_value}")
        else:
            print("[实时K线] 技术指标窗口未初始化")
        
        self.update_status_bar()

    def update_status_bar(self):
        """
        状态栏热更新，支持窗口和图表标题
        """
        # 构建状态文本
        trend_symbol = "↑" if self.state_map.trend_strength > 0 else "↓"
        position_info = ""
        try:
            position = self.get_position(self.params_map.instrument_id)
            if position and hasattr(position, 'net_position') and position.net_position != 0:
                position_info = f" | 持仓:{position.net_position}手"
        except Exception:
            pass

        status = (
            f"合约: {self.params_map.instrument_id} | "
            f"趋势: {abs(self.state_map.trend_strength):.2f}{trend_symbol} | "
            f"波动: {self.state_map.volatility_level:.4f} | "
            f"信号: {self.state_map.integrated_signal:.2f} | "
            f"置信度: {self.state_map.signal_confidence:.2f} | "
            f"形态: {self.state_map.pattern_detected} | "
            f"波段: {self.state_map.wave_type} | "
            f"冗余度: {self.state_map.redundancy_score:.2f}" + position_info
        )

        if hasattr(self, 'widget') and self.widget is not None:
            # 更新窗口标题
            if hasattr(self.widget, 'setWindowTitle'):
                self.widget.setWindowTitle(f"Strategy1 - 多模块融合信号策略 | {status[:50]}...")
            # 更新图表标题显示状态
            if hasattr(self.widget, 'kline_widget') and self.widget.kline_widget is not None:
                display_title = f"{self.params_map.instrument_id} | {status[:30]}..."
                if hasattr(self.widget.kline_widget, 'set_title'):
                    self.widget.kline_widget.set_title(display_title)

    def update_indicators(self, kline: KLineData) -> None:
        """
        更新技术指标
        
        参数:
            kline: K线数据
        """
        # 多周期HULL同步更新
        for hull in self.hull_calculators.values():
            hull.update(kline.close)
        # 选用主周期（如9）作为state_map.hull_value
        hull_value, hull_prev = self.hull_calculators[9].get_hull()
        if hull_value is not None:
            self.state_map.hull_prev = self.state_map.hull_value
            self.state_map.hull_value = hull_value
            self.hull_prev = hull_prev
        
        # 更新STC指标
        self.stc_calculator.update(kline.close)
        stc_value, stc_signal = self.stc_calculator.get_stc()
        
        if stc_value is not None:
            self.stc_prev = self.state_map.stc_value
            self.state_map.stc_value = stc_value
            self.state_map.stc_signal = stc_signal
        
        # 更新K线形态识别
        self.pattern_recognizer.update(kline)
        if self.pattern_recognizer.is_ready():
            patterns = self.pattern_recognizer.recognize_patterns()
            
            # 找出最强的形态
            strongest_pattern = None
            max_strength = 0
            
            for pattern, is_present in patterns.items():
                if is_present:
                    # 简单的形态强度计算
                    strength = 0
                    if pattern in ['bullish_engulfing', 'bearish_engulfing']:
                        strength = 0.8
                    elif pattern in ['hammer', 'shooting_star']:
                        strength = 0.7
                    elif pattern in ['three_white_soldiers', 'three_black_crows']:
                        strength = 0.9
                    elif pattern in ['gap_up', 'gap_down']:
                        strength = 0.6
                    elif pattern == 'marubozu':
                        strength = 0.5
                    
                    if strength > max_strength:
                        max_strength = strength
                        strongest_pattern = pattern
            
            if strongest_pattern:
                self.state_map.pattern_detected = strongest_pattern
                self.state_map.pattern_strength = max_strength
            else:
                self.state_map.pattern_detected = ""
                self.state_map.pattern_strength = 0
        
        # 更新波段识别
        if hull_value is not None and stc_value is not None:
            timestamp = getattr(kline, "timestamp", None)
            self.wave_recognizer.update(kline.close, hull_value, stc_value, timestamp)
            
            if self.wave_recognizer.is_ready():
                wave_info = self.wave_recognizer.get_wave_info()
                self.state_map.wave_type = wave_info['type']
                self.state_map.wave_strength = wave_info['strength']
        
        # 更新模糊系统
        if self.hull_calculators[9].is_ready() and self.stc_calculator.is_ready():
            # 计算稳定性 (基于HULL趋势)
            hull_trend = self.hull_calculators[9].get_trend()
            stability = 0.5  # 默认中等稳定性
            
            if hull_trend == "BULLISH":
                stability = 0.7
            elif hull_trend == "BEARISH":
                stability = 0.3
            
            # 计算波动性 (基于价格变化)
            if len(self.wave_recognizer.price_history) >= 10:
                prices = list(self.wave_recognizer.price_history)[-10:]
                price_changes = [abs(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
                volatility = sum(price_changes) / len(price_changes)
            else:
                volatility = 0.05  # 默认中等波动性
            
            # 计算利润趋势 (基于STC方向)
            if len(self.stc_calculator.stc_final) >= 3:
                stc_values = list(self.stc_calculator.stc_final)[-3:]
                profit_trend = (stc_values[-1] - stc_values[0]) / 100.0  # 归一化到[-1,1]范围
            else:
                profit_trend = 0.0  # 默认中性
            
            # 模糊推理
            fuzzy_inputs = {
                'stability': stability,
                'volatility': volatility,
                'profit': profit_trend
            }
            
            memberships = self.fuzzy_system.fuzzify(fuzzy_inputs)
            risk_level, action_level, confidence = self.fuzzy_system.infer(memberships)
            
            # 更新状态
            self.state_map.risk_level = risk_level
            self.state_map.action_level = action_level
            self.state_map.confidence = confidence
        
        # 更新市场分类器
        self.market_classifier.update(kline.close, kline.volume)

        # 更新技术指标历史
        self.technical_history['hull'].append(float(self.state_map.hull_value))
        self.technical_history['stc'].append(float(self.state_map.stc_value))

        # 更新平均成交量
        self.avg_volume.append(kline.volume)

    def update_market_state(self) -> None:
        """更新市场状态"""
        # 分类市场状态
        regime, volatility, trend_strength = self.market_classifier.classify_regime()

        # 更新状态
        self.state_map.market_regime = regime
        self.state_map.volatility_level = volatility
        self.state_map.trend_strength = trend_strength

        # 更新动态阈值优化器
        if self.threshold_optimizer:
            self.threshold_optimizer.update_market_state(volatility, trend_strength)
            signal_threshold, confidence_threshold = self.threshold_optimizer.get_current_thresholds()
            self.state_map.current_signal_threshold = signal_threshold
            self.state_map.current_confidence_threshold = confidence_threshold

            # 计算阈值调整因子
            base_signal = self.params_map.base_signal_threshold
            self.state_map.threshold_adjustment_factor = signal_threshold / base_signal if base_signal > 0 else 1.0

        # 打印市场状态变化
        if hasattr(self, '_last_regime') and self._last_regime != regime:
            print(f"市场状态变化: {self._last_regime} -> {regime} (波动率: {volatility:.4f}, 趋势强度: {trend_strength:.4f})")
            print(f"动态阈值调整: 信号阈值={self.state_map.current_signal_threshold:.3f}, 置信度阈值={self.state_map.current_confidence_threshold:.3f}")

        self._last_regime = regime

    def update_adaptive_weights(self) -> None:
        """更新自适应权重"""
        if not self.params_map.learning_enabled:
            return

        # 获取模块性能
        module_performance = {
            'technical': self.state_map.technical_accuracy,
            'pattern': self.state_map.pattern_accuracy,
            'wave': self.state_map.wave_accuracy,
            'fuzzy': self.state_map.fuzzy_accuracy
        }

        # 更新权重
        updated_weights = self.weight_allocator.update_weights(
            self.state_map.market_regime,
            module_performance
        )

        # 应用模块相关性分析调整权重
        if self.correlation_analyzer:
            # 基于相关性调整权重，降低冗余模块的影响
            correlation_adjusted_weights = self.correlation_analyzer.get_adjusted_weights(updated_weights)

            # 更新相关性状态
            redundancy_score, _ = self.correlation_analyzer.detect_redundancy()
            self.state_map.redundancy_score = redundancy_score
            self.state_map.module_correlations = self.correlation_analyzer.correlation_matrix

            # 使用相关性调整后的权重
            updated_weights = correlation_adjusted_weights

        # 更新状态
        self.state_map.adaptive_weights = updated_weights

    def calc_signal(self, kline: KLineData) -> None:
        """
        计算交易信号
        
        参数:
            kline: K线数据
        """
        # 检查指标是否就绪
        if not self.hull_calculators[9].is_ready() or not self.stc_calculator.is_ready():
            return
        
        # 生成各模块信号
        module_signals = self._generate_module_signals()
        
        # 处理信号冲突
        module_performance = {
            'technical': self.state_map.technical_accuracy,
            'pattern': self.state_map.pattern_accuracy,
            'wave': self.state_map.wave_accuracy,
            'fuzzy': self.state_map.fuzzy_accuracy
        }
        adjusted_signals = self.signal_fusion.handle_conflicts(module_signals, module_performance)
        
        # 融合信号
        integrated_signal, confidence = self.signal_fusion.fuse_signals(
            adjusted_signals,
            self.state_map.adaptive_weights
        )
        
        # 计算最终信号强度
        final_signal = self.signal_fusion.calculate_signal_strength(integrated_signal, confidence)

        # 更新模块相关性分析器
        if self.correlation_analyzer:
            self.correlation_analyzer.update_signals(adjusted_signals)

        # 更新信号历史
        for module, signal in adjusted_signals.items():
            if module in self.signal_history:
                self.signal_history[module].append(signal)

        # 设置最终信号和置信度
        self.state_map.integrated_signal = final_signal
        self.state_map.signal_confidence = confidence

        # 使用动态阈值生成交易信号
        if self.threshold_optimizer:
            should_trade = self.threshold_optimizer.should_trade(abs(final_signal), confidence)

            if should_trade:
                self.buy_signal = final_signal > 0
                self.sell_signal = final_signal < 0
            else:
                self.buy_signal = False
                self.sell_signal = False
        else:
            # 回退到固定阈值
            signal_threshold = self.params_map.base_signal_threshold
            confidence_threshold = self.params_map.confidence_threshold

            if abs(final_signal) >= signal_threshold and confidence >= confidence_threshold:
                self.buy_signal = final_signal > 0
                self.sell_signal = final_signal < 0
            else:
                self.buy_signal = False
                self.sell_signal = False
        
        # 根据交易方向调整信号
        if self.params_map.trade_direction == "buy":
            self.short_signal = self.sell_signal
            self.cover_signal = self.buy_signal
        else:
            self.short_signal = self.buy_signal
            self.cover_signal = self.sell_signal
        
        # 设置价格
        self.long_price = self.short_price = kline.close
        
        if self.tick:
            self.long_price = self.tick.ask_price1
            self.short_price = self.tick.bid_price1
            
            if self.params_map.price_type == "D2":
                self.long_price = self.tick.ask_price2
                self.short_price = self.tick.bid_price2

    def _generate_module_signals(self) -> Dict[str, float]:
        """
        生成各模块信号
        
        返回:
            各模块信号
        """
        signals = {}
        
        # 技术指标信号
        hull_current = self.state_map.hull_value
        hull_prev = self.hull_prev
        stc_current = self.state_map.stc_value
        stc_prev = self.stc_prev
        
        hull_up = hull_current > hull_prev
        stc_up = stc_current > stc_prev
        
        if hull_up and stc_up:
            signals['technical'] = 1.0
        elif not hull_up and not stc_up:
            signals['technical'] = -1.0
        else:
            signals['technical'] = 0.0
        
        # 形态识别信号
        pattern = self.state_map.pattern_detected
        strength = self.state_map.pattern_strength
        
        if pattern in ['bullish_engulfing', 'hammer', 'three_white_soldiers', 'gap_up']:
            signals['pattern'] = strength
        elif pattern in ['bearish_engulfing', 'shooting_star', 'three_black_crows', 'gap_down']:
            signals['pattern'] = -strength
        else:
            signals['pattern'] = 0.0
        
        # 波段分析信号
        wave_type = self.state_map.wave_type
        wave_strength = self.state_map.wave_strength
        
        if wave_type == 'uptrend':
            signals['wave'] = wave_strength
        elif wave_type == 'downtrend':
            signals['wave'] = -wave_strength
        else:
            signals['wave'] = 0.0
        
        # 模糊推理信号
        action_level = self.state_map.action_level
        confidence = self.state_map.confidence
        
        if action_level == "Aggressive":
            signals['fuzzy'] = confidence
        elif action_level == "Normal":
            signals['fuzzy'] = confidence * 0.5
        elif action_level == "Conservative":
            signals['fuzzy'] = confidence * 0.2
        elif action_level == "Stop":
            signals['fuzzy'] = -confidence
        else:
            signals['fuzzy'] = 0.0
        
        return signals

    def exec_signal(self) -> None:
        """执行交易信号"""
        self.signal_price = 0
        
        position = self.get_position(self.params_map.instrument_id)
        
        if self.order_id is not None:
            # 挂单未成交，撤单
            self.cancel_order(self.order_id)
        
        # 平仓逻辑
        if position.net_position > 0 and self.sell_signal:
            self.signal_price = -self.short_price
            
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.short_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
                print(f"执行卖出平仓: 价格={self.short_price}, 数量={position.net_position}, 信号强度={self.state_map.integrated_signal:.2f}, 置信度={self.state_map.signal_confidence:.2f}")
        elif position.net_position < 0 and self.cover_signal:
            self.signal_price = self.long_price
            
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=abs(position.net_position),
                    order_direction="buy"
                )
                print(f"执行买入平仓: 价格={self.long_price}, 数量={abs(position.net_position)}, 信号强度={self.state_map.integrated_signal:.2f}, 置信度={self.state_map.signal_confidence:.2f}")
        
        # 开仓逻辑
        if self.short_signal:
            self.signal_price = -self.short_price
            
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.short_price,
                    order_direction="sell"
                )
                print(f"执行卖出开仓: 价格={self.short_price}, 数量={self.params_map.order_volume}, 信号强度={self.state_map.integrated_signal:.2f}, 置信度={self.state_map.signal_confidence:.2f}")
        elif self.buy_signal:
            self.signal_price = self.long_price
            
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.long_price,
                    order_direction="buy"
                )
                print(f"执行买入开仓: 价格={self.long_price}, 数量={self.params_map.order_volume}, 信号强度={self.state_map.integrated_signal:.2f}, 置信度={self.state_map.signal_confidence:.2f}")
# ==================== 性能测试函数 ====================

def benchmark_hull_performance(price_data, period=9, iterations=100):
    """
    对HullMovingAverage进行性能基准测试
    
    参数:
        price_data: 价格数据列表
        period: Hull周期
        iterations: 测试迭代次数
    
    返回:
        性能测试结果字典
    """
    import time
    
    # 定义原始Hull实现（用于比较）
    class OriginalHull:
        def __init__(self, period):
            self.period = period
            self.half_period = max(1, int(period / 2))
            self.sqrt_period = max(1, int(np.sqrt(period)))
            self.prices = []
            self._is_ready = False
            
        def _wma(self, data, period):
            if len(data) < period:
                return None
            weights = np.arange(1, period + 1)
            window = np.array(data[-period:])
            return np.dot(window, weights) / weights.sum()
            
        def update(self, price):
            if not isinstance(price, (int, float)) or math.isnan(price) or math.isinf(price):
                return
            self.prices.append(float(price))
            if len(self.prices) >= self.period:
                self._is_ready = True
                
        def get_hull(self):
            if not self._is_ready or len(self.prices) < self.period:
                return None, None
            wma_half = [self._wma(self.prices[:i], self.half_period) for i in range(self.half_period, len(self.prices)+1)]
            wma_full = [self._wma(self.prices[:i], self.period) for i in range(self.period, len(self.prices)+1)]
            min_len = min(len(wma_half), len(wma_full))
            wma_half = wma_half[-min_len:]
            wma_full = wma_full[-min_len:]
            raw = [2*h - f for h, f in zip(wma_half, wma_full)]
            hma = [self._wma(raw[:i], self.sqrt_period) for i in range(self.sqrt_period, len(raw)+1)]
            if len(hma) < 2:
                return None, None
            return hma[-1], hma[-2]
    
    # 测试原始实现
    original_hull = OriginalHull(period)
    start_time = time.time()
    
    for _ in range(iterations):
        for price in price_data:
            original_hull.update(price)
            original_hull.get_hull()
    
    original_time = time.time() - start_time
    
    # 测试优化实现
    optimized_hull = HullMovingAverage(period)
    start_time = time.time()
    
    for _ in range(iterations):
        for price in price_data:
            optimized_hull.update(price)
            optimized_hull.get_hull()
    
    optimized_time = time.time() - start_time
    
    # 计算性能提升
    speedup = original_time / optimized_time if optimized_time > 0 else float('inf')
    
    return {
        'original_time': original_time,
        'optimized_time': optimized_time,
        'speedup': speedup,
        'iterations': iterations,
        'data_points': len(price_data)
    }

# 示例使用
# import random
# price_data = [random.uniform(100, 200) for _ in range(1000)]
# result = benchmark_hull_performance(price_data)
# print(f"性能提升: {result['speedup']:.2f}倍")    def