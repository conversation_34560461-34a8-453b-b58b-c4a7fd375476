# encoding: UTF-8

"""
HULL+STC策略（KC架构重组版）
last update: 2024年12月19日
"""

from typing import Literal
import numpy as np

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K线周期")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    # HULL+STC参数
    hull_period: int = Field(default=9, title="HULL周期", ge=5, le=21)
    stc_fast: int = Field(default=23, title="STC快周期", ge=12, le=30)
    stc_slow: int = Field(default=50, title="STC慢周期", ge=30, le=100)
    stc_cycle: int = Field(default=10, title="STC循环长度", ge=5, le=20)
    stop_mult: float = Field(default=2.2, title="止损倍数", ge=1.5, le=2.5)
    profit_mult: float = Field(default=3.0, title="止盈倍数", ge=2.5, le=3.5)
    trail_step: float = Field(default=1.0, title="追踪步长", ge=0.5, le=1.5)
    atr_period: int = Field(default=14, title="ATR周期", ge=10, le=20)
    
    # 止盈止损倍数
    stop_mult: float = Field(default=2.2, title="止损倍数", ge=1.5, le=2.5)
    profit_mult: float = Field(default=3.0, title="止盈倍数", ge=2.5, le=3.5)
    trail_step: float = Field(default=1.0, title="追踪步长", ge=0.5, le=1.5)
    
    # 自适应止盈止损参数
    enable_atr_stops: bool = Field(default=True, title="启用ATR自适应止盈止损")
    atr_multiplier_stop: float = Field(default=2.0, title="ATR止损倍数", ge=1.0, le=3.0)
    atr_multiplier_profit: float = Field(default=3.0, title="ATR止盈倍数", ge=2.0, le=5.0)
    
    # 智能追踪止损参数
    enable_smart_trailing: bool = Field(default=True, title="启用智能追踪止损")
    trailing_activation: float = Field(default=1.5, title="追踪激活倍数", ge=1.0, le=2.0)
    trailing_distance: float = Field(default=1.0, title="追踪距离倍数", ge=0.5, le=2.0)
    
    # 盈亏回撤保护参数
    enable_profit_protection: bool = Field(default=True, title="启用盈亏回撤保护")
    profit_lock_threshold: float = Field(default=2.0, title="盈利锁定阈值", ge=1.5, le=3.0)
    profit_lock_distance: float = Field(default=0.5, title="盈利锁定距离", ge=0.3, le=1.0)
    
    # 市场波动率自适应参数
    volatility_lookback: int = Field(default=20, title="波动率回望期", ge=10, le=50)
    volatility_threshold_high: float = Field(default=0.03, title="高波动率阈值", ge=0.02, le=0.05)
    volatility_threshold_low: float = Field(default=0.01, title="低波动率阈值", ge=0.005, le=0.02)
    
    # 手动模式固定止盈止损设置
    fixed_stop_loss: float = Field(default=0, title="固定止损价", ge=0)
    fixed_take_profit: float = Field(default=0, title="固定止盈价", ge=0)
    use_fixed_stops: bool = Field(default=False, title="使用固定止盈止损")
    
    # 堆栈优化参数
    enable_adaptive_params: bool = Field(default=True, title="启用参数自适应")
    enable_multi_timeframe: bool = Field(default=True, title="启用多时间框架确认")
    enable_market_regime: bool = Field(default=True, title="启用市场制度识别")
    
    # 多时间框架设置
    confirm_timeframe: str = Field(default="M5", title="确认时间框架")
    long_timeframe: str = Field(default="M15", title="长周期时间框架")
    
    # 自适应参数调整范围
    adaptive_sensitivity: float = Field(default=0.3, title="自适应敏感度", ge=0.1, le=0.5)
    regime_lookback: int = Field(default=50, title="制度识别回望期", ge=20, le=100)
    
    # 可选过滤参数
    disable_mtf_filter: bool = Field(default=True, title="关闭多时间框架过滤")
    disable_regime_filter: bool = Field(default=True, title="关闭市场制度过滤")

    # 新增最佳实践模式
    signal_mode: Literal["manual", "auto", "best_practice"] = Field(default="best_practice", title="信号模式")
    min_signal_interval: int = Field(default=5, title="信号冷却间隔", ge=3, le=20)
    atr_signal_threshold: float = Field(default=0.8, title="ATR信号过滤阈值", ge=0.5, le=3.0)
    
    # 新增信号敏感度参数
    long_signal_sensitivity: float = Field(default=1.5, title="做多信号敏感度", ge=0.5, le=2.0)
    short_signal_sensitivity: float = Field(default=1.0, title="做空信号敏感度", ge=0.5, le=2.0)
    use_simple_signal: bool = Field(default=True, title="使用简单信号")
    use_nested_signal: bool = Field(default=True, title="使用嵌套信号")


class State(BaseState):
    """状态映射模型"""
    # HULL指标
    hull_ma: float = Field(default=0, title="Hull移动平均线")
    hull_signal: str = Field(default="等待", title="HULL信号")
    hull_price_relation: str = Field(default="中性", title="HULL与价格关系")
    
    # STC指标
    stc_value: float = Field(default=0, title="STC值")
    stc_signal: float = Field(default=0, title="STC信号线")
    stc_trend: str = Field(default="中性", title="STC趋势")
    
    # 趋势状态
    trend_type: Literal["A", "B"] = Field(default="A", title="行情模式")
    is_trending: bool = Field(default=False, title="是否趋势")
    trend_strength: float = Field(default=0, title="趋势强度", ge=0, le=1)
    trend_duration: int = Field(default=0, title="趋势持续周期")
    volatility: float = Field(default=0, title="波动率")
    
    # 止损止盈
    stop_loss: float = Field(default=0, title="止损价")
    take_profit: float = Field(default=0, title="止盈价")
    trailing_stop: float = Field(default=0, title="追踪止损价")
    
    # 动态止盈止损
    highest_price: float = Field(default=0, title="最高价")
    lowest_price: float = Field(default=0, title="最低价")
    current_profit: float = Field(default=0, title="当前盈亏")
    max_profit: float = Field(default=0, title="最大盈亏")
    
    # 自适应止盈止损状态
    atr_value: float = Field(default=0, title="ATR值")
    adaptive_stop_loss: float = Field(default=0, title="自适应止损价")
    adaptive_take_profit: float = Field(default=0, title="自适应止盈价")
    smart_trailing_stop: float = Field(default=0, title="智能追踪止损价")
    profit_lock_stop: float = Field(default=0, title="盈利锁定止损价")
    
    # 智能追踪状态
    trailing_activated: bool = Field(default=False, title="追踪止损已激活")
    profit_locked: bool = Field(default=False, title="盈利已锁定")
    trailing_start_price: float = Field(default=0, title="追踪起始价格")
    profit_lock_price: float = Field(default=0, title="盈利锁定价格")
    
    # 市场波动率状态
    current_volatility: float = Field(default=0, title="当前波动率")
    volatility_regime: str = Field(default="中等", title="波动率制度")
    volatility_multiplier: float = Field(default=1.0, title="波动率乘数", ge=0.5, le=2.0)
    
    # 堆栈优化状态
    market_regime: str = Field(default="震荡", title="市场制度")
    regime_strength: float = Field(default=0, title="制度强度", ge=0, le=1)
    multi_tf_confirmation: bool = Field(default=False, title="多时间框架确认")
    adaptive_multiplier: float = Field(default=1.0, title="自适应乘数", ge=0.5, le=2.0)
    
    # 多时间框架状态
    htf_trend: str = Field(default="中性", title="高时间框架趋势")
    htf_hull_signal: str = Field(default="等待", title="高时间框架HULL信号")
    htf_stc_value: float = Field(default=0, title="高时间框架STC值")
    
    # 新增指标状态
    ma5: float = Field(default=0, title="MA5")
    ma10: float = Field(default=0, title="MA10")
    ma20: float = Field(default=0, title="MA20")
    atr: float = Field(default=0, title="ATR")
    k: float = Field(default=0, title="KDJ_K")
    d: float = Field(default=0, title="KDJ_D")
    j: float = Field(default=0, title="KDJ_J")
    macd: float = Field(default=0, title="MACD")
    macd_signal: float = Field(default=0, title="MACD_SIGNAL")
    macd_hist: float = Field(default=0, title="MACD_HIST")


class Strategy2(BaseStrategy):
    """HULL+STC策略（KC架构重组版）"""
    
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        """参数表"""
        
        self.state_map = State()
        """状态表"""
        
        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        
        # 行情判断相关
        self.trend_period = 15  # 趋势判断周期
        self.trend_count = 0
        self.price_history = []
        self.trend_history = []  # 记录历史趋势状态
        self.volatility_history = []  # 记录历史波动率
        
        # 趋势判断参数
        self.trend_threshold = 0.6  # 趋势判断阈值
        self.volatility_threshold = 0.5  # 波动率阈值
        self.min_trend_duration = 5  # 最小趋势持续周期
        self.max_trend_duration = 30  # 最大趋势持续周期
        
        # 参数组合
        self.param_sets = {
            "A": {  # 趋势型参数
                "hull_period": 9,
                "stc_fast": 23,
                "stc_slow": 50,
                "stc_cycle": 10,
                "stop_mult": 2.2,
                "profit_mult": 3.0,
                "trail_step": 1.0
            },
            "B": {  # 震荡型参数
                "hull_period": 14,
                "stc_fast": 21,
                "stc_slow": 45,
                "stc_cycle": 8,
                "stop_mult": 1.8,
                "profit_mult": 2.5,
                "trail_step": 0.5
            }
        }
        
        self.current_params = None
        self.tick: TickData = None
        self.order_id = None
        self.signal_price = 0
        self.long_price = 0  # 添加long_price初始化
        self.short_price = 0  # 添加short_price初始化
        
        # 动态止盈止损相关变量
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        
        # 指标计算相关历史数据
        self.close_prices = []
        self.max_history = 100  # 保存历史数据长度
        
        # 堆栈优化相关变量
        self.mtf_generators = {}  # 多时间框架K线生成器
        self.regime_history = []  # 市场制度历史
        self.adaptive_params_history = []  # 自适应参数历史
        self.correlation_buffer = []  # 相关性缓冲区
        
        # HULL与价格关系历史
        self.hull_price_history = []
        self.price_hull_cross_signals = []
        
        # 自适应止盈止损相关变量
        self.atr_history = []  # ATR历史数据
        self.high_low_history = []  # 高低点历史数据
        self.true_range_history = []  # 真实波幅历史数据
        self.profit_history = []  # 盈亏历史数据
        self.volatility_history_long = []  # 长期波动率历史
        
        # 智能追踪相关变量
        self.trailing_high = 0  # 追踪期间最高价
        self.trailing_low = 0   # 追踪期间最低价
        self.trailing_start_time = 0  # 追踪开始时间
        self.profit_lock_start_time = 0  # 盈利锁定开始时间

        # 新增多周期HMA缓存
        self.hma_short_history = []
        self.hma_mid_history = []
        self.hma_long_history = []
        self.last_signal_bar = -1000
        self.bar_count = 0
        
        # 平台适应性变量
        self.platform_initialized = False
        self.widget_available = False
        self.kline_generator_ready = False

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标"""
        try:
            return {
                "HULL_MA": self.state_map.hull_ma,
                "STC_VALUE": self.state_map.stc_value,
                "STC_SIGNAL": self.state_map.stc_signal,
                "ATR": self.state_map.atr,
                "MA5": self.state_map.ma5,
                "MA10": self.state_map.ma10,
                "MA20": self.state_map.ma20
            }
        except Exception:
            return {"HULL_MA": 0, "STC_VALUE": 0, "STC_SIGNAL": 0}

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标"""
        try:
            return {
                "STOP_LOSS": self.state_map.stop_loss,
                "TAKE_PROFIT": self.state_map.take_profit,
                "TRAILING_STOP": self.state_map.trailing_stop
            }
        except Exception:
            return {"STOP_LOSS": 0, "TAKE_PROFIT": 0, "TRAILING_STOP": 0}

    def on_tick(self, tick: TickData):
        """收到行情TICK推送 - 平台适应性版本"""
        try:
            super().on_tick(tick)
            self.tick = tick
            
            # 确保K线生成器已初始化
            if hasattr(self, 'kline_generator') and self.kline_generator:
                self.kline_generator.tick_to_kline(tick)
        except Exception as e:
            # 平台适应性错误处理
            pass

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        try:
            super().on_order_cancel(order)
            self.order_id = None
        except Exception:
            pass

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """成交推送回调"""
        try:
            super().on_trade(trade, log)
            self.order_id = None
        except Exception:
            pass

    def on_start(self):
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()
        super().on_start()
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.tick = None
        self.update_status_bar()

    def on_stop(self):
        super().on_stop()

    def callback(self, kline: KLineData) -> None:
        self.calc_indicator()
        self.calc_signal(kline)
        self.exec_signal()
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })
        if self.trading:
            self.update_status_bar()

    def real_time_callback(self, kline: KLineData) -> None:
        self.calc_indicator()
        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })
        self.update_status_bar()

    def calc_trend(self, kline: KLineData) -> None:
        """计算趋势状态"""
        if len(self.close_prices) < self.trend_period:
            return
        
        # 计算趋势强度
        recent_prices = self.close_prices[-self.trend_period:]
        price_change = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
        
        # 计算波动率
        returns = []
        for i in range(1, len(recent_prices)):
            ret = (recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1]
            returns.append(ret)
        
        volatility = np.std(returns) if returns else 0
        
        # 判断是否为趋势
        is_trending = abs(price_change) > self.trend_threshold and volatility < self.volatility_threshold
        
        # 更新趋势状态
        if is_trending:
            self.trend_count += 1
        else:
            self.trend_count = 0
        
        # 限制趋势持续周期
        self.trend_count = max(0, min(self.trend_count, self.max_trend_duration))
        
        # 更新状态
        self.state_map.is_trending = is_trending
        self.state_map.trend_strength = min(1.0, abs(price_change) / self.trend_threshold)
        self.state_map.trend_duration = self.trend_count
        self.state_map.volatility = volatility
        self.state_map.trend_type = "A" if is_trending else "B"
        
        # 记录历史
        self.trend_history.append(is_trending)
        self.volatility_history.append(volatility)
        
        # 限制历史记录长度
        if len(self.trend_history) > 50:
            self.trend_history.pop(0)
        if len(self.volatility_history) > 50:
            self.volatility_history.pop(0)
        
        # 根据趋势类型选择参数
        if self.state_map.trend_type == "A":
            # 趋势型参数
            self.current_params = self.param_sets["A"].copy()
        else:
            # 震荡型参数
            self.current_params = self.param_sets["B"].copy()

    def calc_wma(self, prices: list, period: int) -> float:
        """计算加权移动平均线"""
        if len(prices) < period:
            return 0.0
        
        weights = np.arange(1, period + 1)
        weighted_prices = np.array(prices[-period:]) * weights
        return round(weighted_prices.sum() / weights.sum(), 4)

    def calc_hull_ma(self, prices: list, period: int) -> float:
        """计算Hull Moving Average指标"""
        if len(prices) < period:
            return 0.0
        
        # Step 1: 计算2周期WMA
        wma_half = self.calc_wma(prices, period // 2)
        
        # Step 2: 计算N周期WMA
        wma_full = self.calc_wma(prices, period)
        
        # Step 3: 计算差值
        diff = 2 * wma_half - wma_full
        
        # Step 4: 对差值进行WMA平滑（周期为sqrt(N)）
        sqrt_period = int(np.sqrt(period))
        
        # 保存差值历史用于最终WMA计算
        if not hasattr(self, 'hull_diff_history'):
            self.hull_diff_history = []
        
        self.hull_diff_history.append(diff)
        if len(self.hull_diff_history) > sqrt_period:
            self.hull_diff_history.pop(0)
        
        # 计算最终的HULL值
        if len(self.hull_diff_history) >= sqrt_period:
            hull_value = self.calc_wma(self.hull_diff_history, sqrt_period)
            return hull_value
        else:
            return diff

    def calc_ema(self, prices: list, period: int) -> float:
        """计算指数移动平均线"""
        if len(prices) < 2:
            return prices[-1] if prices else 0.0
        
        alpha = 2.0 / (period + 1)
        ema = prices[0]
        
        for price in prices[1:]:
            ema = alpha * price + (1 - alpha) * ema
        
        return round(ema, 4)

    def calc_true_range(self, high: float, low: float, prev_close: float) -> float:
        """计算真实波幅"""
        tr1 = high - low  # 当日最高价 - 当日最低价
        tr2 = abs(high - prev_close)  # 当日最高价 - 前日收盘价
        tr3 = abs(low - prev_close)   # 当日最低价 - 前日收盘价
        return max(tr1, tr2, tr3)

    def calc_atr(self, high_prices: list, low_prices: list, close_prices: list, period: int) -> float:
        """计算平均真实波幅(ATR)"""
        # 检查数据长度是否足够
        if len(high_prices) < 2 or len(low_prices) < 2 or len(close_prices) < 2:
            return 0.0
        
        # 确保所有数组长度一致
        min_length = min(len(high_prices), len(low_prices), len(close_prices))
        if min_length < 2:
            return 0.0
        
        # 截取相同长度的数据
        high_prices = high_prices[-min_length:]
        low_prices = low_prices[-min_length:]
        close_prices = close_prices[-min_length:]
        
        # 计算真实波幅
        true_ranges = []
        for i in range(1, min_length):
            tr = self.calc_true_range(high_prices[i], low_prices[i], close_prices[i-1])
            true_ranges.append(tr)
        
        # 使用EMA计算ATR
        if len(true_ranges) >= period:
            atr = self.calc_ema(true_ranges, period)
            return round(atr, 4)
        else:
            return round(np.mean(true_ranges) if true_ranges else 0.0, 4)

    def calc_adaptive_volatility(self, prices: list, lookback: int) -> tuple:
        """计算自适应波动率"""
        if len(prices) < lookback:
            return 0.0, "NORMAL"
        
        # 计算收益率
        returns = []
        for i in range(1, len(prices)):
            if prices[i-1] != 0:
                ret = (prices[i] - prices[i-1]) / prices[i-1]
                returns.append(ret)
        
        if len(returns) < lookback:
            return 0.0, "NORMAL"
        
        # 计算波动率
        volatility = np.std(returns[-lookback:])
        
        # 判断波动率制度
        if volatility > self.params_map.volatility_threshold_high:
            regime = "HIGH"
        elif volatility < self.params_map.volatility_threshold_low:
            regime = "LOW"
        else:
            regime = "NORMAL"
        
        return volatility, regime

    def calc_stochastic_k(self, values: list, period: int) -> float:
        """计算随机指标K值"""
        period = int(period)
        if len(values) < period:
            return 50.0  # 默认中性值
        
        recent_values = values[-period:]
        current_value = values[-1]
        highest = max(recent_values)
        lowest = min(recent_values)
        
        if highest == lowest:
            return 50.0
        return 100 * (current_value - lowest) / (highest - lowest)

    def calc_stc(self, prices: list, fast_period: int, slow_period: int, cycle_period: int) -> tuple:
        """计算Schaff Trend Cycle指标"""
        if len(prices) < max(fast_period, slow_period, cycle_period):
            return 50.0, 50.0  # 返回默认值
        
        # Step 1: 计算MACD值
        ema_fast = self.calc_ema(prices, fast_period)
        ema_slow = self.calc_ema(prices, slow_period)
        macd_value = ema_fast - ema_slow
        
        # 保存MACD历史
        if not hasattr(self, 'macd_history'):
            self.macd_history = []
        
        self.macd_history.append(macd_value)
        if len(self.macd_history) > cycle_period * 2:
            self.macd_history.pop(0)
        
        # Step 2: 计算MACD的K值
        if len(self.macd_history) >= cycle_period:
            k1_value = self.calc_stochastic_k(self.macd_history, cycle_period)
        else:
            k1_value = 50.0
        
        # Step 3: 对K值进行3周期SMA平滑得到D值
        if not hasattr(self, 'k1_history'):
            self.k1_history = []
        
        self.k1_history.append(k1_value)
        if len(self.k1_history) > 3:
            self.k1_history.pop(0)
        
        if len(self.k1_history) >= 3:
            d1_value = sum(self.k1_history) / len(self.k1_history)
        else:
            d1_value = k1_value
        
        # Step 4: 对D值再次应用随机指标
        if not hasattr(self, 'd1_history'):
            self.d1_history = []
        
        self.d1_history.append(d1_value)
        if len(self.d1_history) > cycle_period * 2:
            self.d1_history.pop(0)
        
        if len(self.d1_history) >= cycle_period:
            k2_value = self.calc_stochastic_k(self.d1_history, cycle_period)
        else:
            k2_value = d1_value
        
        # Step 5: 最终STC值为K2的3周期SMA
        if not hasattr(self, 'k2_history'):
            self.k2_history = []
        
        self.k2_history.append(k2_value)
        if len(self.k2_history) > 3:
            self.k2_history.pop(0)
        
        if len(self.k2_history) >= 3:
            stc_value = sum(self.k2_history) / len(self.k2_history)
        else:
            stc_value = k2_value
        
        # 信号线为STC的简单移动平均
        if not hasattr(self, 'stc_history'):
            self.stc_history = []
        
        self.stc_history.append(stc_value)
        if len(self.stc_history) > 3:
            self.stc_history.pop(0)
        
        signal_value = sum(self.stc_history) / len(self.stc_history)
        
        return round(stc_value, 2), round(signal_value, 2)

    def detect_market_regime(self) -> None:
        """市场制度识别 - 堆栈优化核心功能"""
        if not self.params_map.enable_market_regime or len(self.close_prices) < self.params_map.regime_lookback:
            # 数据不足时设置默认值
            self.state_map.market_regime = "震荡"
            self.state_map.regime_strength = 0.5
            return
        
        recent_prices = self.close_prices[-self.params_map.regime_lookback:]
        
        # 1. 计算市场特征指标
        returns = np.diff(recent_prices) / recent_prices[:-1]
        volatility = np.std(returns)
        skewness = self._calculate_skewness(returns)
        kurtosis = self._calculate_kurtosis(returns)
        
        # 2. 趋势持续性分析
        trend_consistency = self._calculate_trend_consistency(recent_prices)
        
        # 3. 波动率制度分类
        vol_percentile = self._calculate_percentile(self.volatility_history[-20:] if len(self.volatility_history) >= 20 else [volatility], volatility)
        
        # 4. 综合制度识别
        if volatility < 0.01 and trend_consistency > 0.7:
            regime = "低波动趋势"
            strength = 0.8
        elif volatility > 0.03 and trend_consistency < 0.3:
            regime = "高波动震荡"
            strength = 0.9
        elif trend_consistency > 0.6:
            regime = "趋势"
            strength = trend_consistency
        elif vol_percentile > 0.7:
            regime = "高波动"
            strength = vol_percentile
        elif vol_percentile < 0.3:
            regime = "低波动"
            strength = 1 - vol_percentile
        else:
            regime = "震荡"
            strength = 0.5
        
        # 5. 更新状态
        self.state_map.market_regime = regime
        self.state_map.regime_strength = strength
        
        # 6. 记录历史
        self.regime_history.append(regime)
        if len(self.regime_history) > 20:
            self.regime_history.pop(0)

    def _calculate_skewness(self, data: np.ndarray) -> float:
        """计算偏度"""
        if len(data) < 3:
            return 0.0
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0.0
        return np.mean(((data - mean) / std) ** 3)

    def _calculate_kurtosis(self, data: np.ndarray) -> float:
        """计算峰度"""
        if len(data) < 4:
            return 0.0
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0.0
        return np.mean(((data - mean) / std) ** 4) - 3

    def _calculate_trend_consistency(self, prices: list) -> float:
        """计算趋势一致性"""
        if len(prices) < 10:
            return 0.0
        
        # 使用多个周期EMA的方向一致性
        ema_periods = [5, 10, 20]
        directions = []
        
        for period in ema_periods:
            if len(prices) >= period + 1:
                ema_current = self.calc_ema(prices, period)
                ema_previous = self.calc_ema(prices[:-1], period)
                directions.append(1 if ema_current > ema_previous else -1)
        
        if not directions:
            return 0.0
        
        # 计算方向一致性
        consistency = abs(sum(directions)) / len(directions)
        return consistency

    def _calculate_percentile(self, data: list, value: float) -> float:
        """计算百分位数"""
        if not data:
            return 0.5
        sorted_data = sorted(data)
        rank = sum(1 for x in sorted_data if x <= value)
        return rank / len(sorted_data)

    def adaptive_parameter_optimization(self) -> None:
        """参数自适应优化 - 堆栈优化核心功能"""
        if not self.params_map.enable_adaptive_params or not self.current_params:
            return
        
        # 1. 基于市场制度调整参数
        regime_multiplier = self._get_regime_multiplier()
        
        # 2. 基于历史表现调整参数
        performance_multiplier = self._get_performance_multiplier()
        
        # 3. 基于波动率调整参数
        volatility_multiplier = self._get_volatility_multiplier()
        
        # 4. 基于持仓方向调整参数
        position_multiplier = self._get_position_multiplier()
        
        # 5. 综合自适应乘数
        adaptive_mult = (regime_multiplier * performance_multiplier * volatility_multiplier * position_multiplier) ** (1/4)
        adaptive_mult = max(0.5, min(2.0, adaptive_mult))  # 限制调整范围
        
        # 6. 应用自适应调整
        sensitivity = self.params_map.adaptive_sensitivity
        self.state_map.adaptive_multiplier = (1 - sensitivity) * self.state_map.adaptive_multiplier + sensitivity * adaptive_mult
        
        # 7. 调整核心参数
        if self.state_map.adaptive_multiplier != 1.0:
            self.current_params["stop_mult"] *= self.state_map.adaptive_multiplier
            self.current_params["profit_mult"] *= self.state_map.adaptive_multiplier
            self.current_params["trail_step"] *= self.state_map.adaptive_multiplier

    def _get_regime_multiplier(self) -> float:
        """根据市场制度获取参数乘数"""
        regime = self.state_map.market_regime
        strength = self.state_map.regime_strength
        
        multipliers = {
            "LOW_VOL_TREND": 1.2 * strength + 0.8,
            "HIGH_VOL_RANGE": 0.8 * strength + 1.0,
            "TRENDING": 1.1 * strength + 0.9,
            "HIGH_VOLATILITY": 0.7 * strength + 1.0,
            "LOW_VOLATILITY": 1.3 * strength + 0.7,
            "RANGING": 0.9 * strength + 1.0
        }
        
        return multipliers.get(regime, 1.0)

    def _get_performance_multiplier(self) -> float:
        """根据历史表现获取参数乘数"""
        if len(self.adaptive_params_history) < 5:
            return 1.0
        
        # 简化的表现评估：基于最近的盈亏
        recent_performance = self.state_map.current_profit
        max_recent_profit = self.state_map.max_profit
        
        if max_recent_profit > 0:
            performance_ratio = recent_performance / max_recent_profit
            if performance_ratio > 0.8:
                return 1.1  # 表现好，适当放宽参数
            elif performance_ratio < 0.2:
                return 0.9  # 表现差，收紧参数
        
        return 1.0

    def _get_volatility_multiplier(self) -> float:
        """根据波动率获取参数乘数"""
        if len(self.volatility_history) < 10:
            return 1.0
        
        current_vol = self.state_map.volatility
        avg_vol = np.mean(self.volatility_history[-10:])
        
        if avg_vol > 0:
            vol_ratio = current_vol / avg_vol
            if vol_ratio > 1.5:
                return 0.8  # 高波动率，收紧参数
            elif vol_ratio < 0.5:
                return 1.2  # 低波动率，放宽参数
        
        return 1.0

    def _get_position_multiplier(self) -> float:
        """根据持仓方向获取参数乘数"""
        if self.position_size > 0:  # 做多仓位
            # 做多时根据趋势强度调整
            if self.state_map.trend_strength > 0.7:
                return 1.1  # 强趋势时适当放宽参数
            else:
                return 1.0
        elif self.position_size < 0:  # 做空仓位
            # 做空时根据趋势强度调整
            if self.state_map.trend_strength > 0.7:
                return 0.9  # 强趋势时适当收紧参数
            else:
                return 1.0
        else:  # 无仓位
            return 1.0

    def multi_timeframe_confirmation(self) -> None:
        """多时间框架确认 - 堆栈优化核心功能"""
        if not self.params_map.enable_multi_timeframe:
            self.state_map.multi_tf_confirmation = True  # 默认确认
            return
        
        # 模拟多时间框架分析（实际应用中需要真实的多时间框架数据）
        # 这里基于当前数据的不同周期分析来模拟
        
        # 1. 长周期趋势确认
        htf_trend_confirmed = self._analyze_higher_timeframe_trend()
        
        # 2. 中周期动量确认
        mtf_momentum_confirmed = self._analyze_medium_timeframe_momentum()
        
        # 3. 综合确认 - 放宽条件
        # 只要有一个确认就通过，或者数据不足时默认确认
        if len(self.close_prices) < 30:
            # 数据不足时默认确认
            self.state_map.multi_tf_confirmation = True
        else:
            # 放宽确认条件：只要不是明确反对就确认
            self.state_map.multi_tf_confirmation = htf_trend_confirmed or mtf_momentum_confirmed

    def _analyze_higher_timeframe_trend(self) -> bool:
        """分析高时间框架趋势"""
        if len(self.close_prices) < 30:
            # 数据不足时设置默认值
            self.state_map.htf_trend = "中性"
            self.state_map.htf_hull_signal = "等待"
            return True  # 数据不足时默认确认
        
        # 使用更长周期的HULL来模拟高时间框架
        long_period = self.current_params["hull_period"] * 2  # 减少倍数，降低要求
        if len(self.close_prices) >= long_period:
            long_hull = self.calc_hull_ma(self.close_prices, long_period)
            current_price = self.close_prices[-1]
            
            # 高时间框架趋势方向
            if long_hull > 0:
                if current_price > long_hull:
                    self.state_map.htf_trend = "看多"
                    self.state_map.htf_hull_signal = "买入"
                else:
                    self.state_map.htf_trend = "看空"
                    self.state_map.htf_hull_signal = "卖出"
            else:
                self.state_map.htf_trend = "中性"
                self.state_map.htf_hull_signal = "等待"
            
            # 放宽趋势确认逻辑
            if self.state_map.hull_signal == "买入":
                # 只要不是明确看空就确认
                return self.state_map.htf_trend != "看空"
            elif self.state_map.hull_signal == "卖出":
                # 只要不是明确看多就确认
                return self.state_map.htf_trend != "看多"
        
        return True

    def _analyze_medium_timeframe_momentum(self) -> bool:
        """分析中等时间框架动量"""
        if len(self.close_prices) < 20:
            return True
        
        # 使用中等周期的STC来模拟中时间框架
        mid_period_fast = self.current_params["stc_fast"] * 1.5  # 减少倍数
        mid_period_slow = self.current_params["stc_slow"] * 1.5
        mid_period_cycle = self.current_params["stc_cycle"] * 1.5
        
        if len(self.close_prices) >= mid_period_slow:
            htf_stc, htf_stc_signal = self.calc_stc(
                self.close_prices, mid_period_fast, mid_period_slow, mid_period_cycle
            )
            self.state_map.htf_stc_value = htf_stc
            
            # 放宽动量确认逻辑
            if self.state_map.stc_value > 80:  # 当前极度超买
                return htf_stc < 90  # 高时间框架未极度超买
            elif self.state_map.stc_value < 20:  # 当前极度超卖
                return htf_stc > 10  # 高时间框架未极度超卖
            else:
                # 中间区域默认确认
                return True
        
        return True

    def calc_indicator(self) -> None:
        close = self.kline_generator.producer.close
        high = self.kline_generator.producer.high
        low = self.kline_generator.producer.low
        if hasattr(close, 'tolist'): close = close.tolist()
        if hasattr(high, 'tolist'): high = high.tolist()
        if hasattr(low, 'tolist'): low = low.tolist()
        self.close_prices = close
        self.high_prices = high
        self.low_prices = low
        ma5 = self.kline_generator.producer.sma(5, array=True)
        ma10 = self.kline_generator.producer.sma(10, array=True)
        ma20 = self.kline_generator.producer.sma(20, array=True)
        self.state_map.ma5 = float(ma5[-1]) if len(ma5) else 0
        self.state_map.ma10 = float(ma10[-1]) if len(ma10) else 0
        self.state_map.ma20 = float(ma20[-1]) if len(ma20) else 0
        atr, _ = self.kline_generator.producer.atr(self.params_map.atr_period)
        self.state_map.atr = float(atr)
        self.state_map.hull_ma = self.calc_hull_ma(self.close_prices, self.params_map.hull_period)
        stc, stc_signal = self.calc_stc(self.close_prices, self.params_map.stc_fast, self.params_map.stc_slow, self.params_map.stc_cycle)
        self.state_map.stc_value = stc
        self.state_map.stc_signal = stc_signal
        # ====== KDJ指标 ======
        kdj_period = 9
        if len(self.close_prices) >= kdj_period:
            low_list = self.low_prices[-kdj_period:]
            high_list = self.high_prices[-kdj_period:]
            close_list = self.close_prices[-kdj_period:]
            low_min = min(low_list)
            high_max = max(high_list)
            rsv = (close_list[-1] - low_min) / (high_max - low_min + 1e-9) * 100
            k = (2/3) * self.state_map.k + (1/3) * rsv if self.state_map.k else rsv
            d = (2/3) * self.state_map.d + (1/3) * k if self.state_map.d else k
            j = 3 * k - 2 * d
            self.state_map.k = round(k, 2)
            self.state_map.d = round(d, 2)
            self.state_map.j = round(j, 2)
        else:
            self.state_map.k = self.state_map.d = self.state_map.j = 0
        # ====== MACD指标 ======
        def ema(arr, period):
            arr = np.array(arr)
            if len(arr) < period:
                return np.zeros_like(arr)
            ema_arr = np.zeros_like(arr)
            alpha = 2 / (period + 1)
            ema_arr[0] = arr[0]
            for i in range(1, len(arr)):
                ema_arr[i] = alpha * arr[i] + (1 - alpha) * ema_arr[i-1]
            return ema_arr
        if len(self.close_prices) >= 26:
            ema12 = ema(self.close_prices, 12)
            ema26 = ema(self.close_prices, 26)
            macd_line = ema12 - ema26
            signal = ema(macd_line, 9)
            hist = macd_line - signal
            self.state_map.macd = round(macd_line[-1], 4)
            self.state_map.macd_signal = round(signal[-1], 4)
            self.state_map.macd_hist = round(hist[-1], 4)
        else:
            self.state_map.macd = self.state_map.macd_signal = self.state_map.macd_hist = 0
        # ====== 高时间框架STC ======
        # 采用1.5倍stc参数周期
        mid_period_fast = self.params_map.stc_fast * 1.5
        mid_period_slow = self.params_map.stc_slow * 1.5
        mid_period_cycle = self.params_map.stc_cycle * 1.5
        if len(self.close_prices) >= mid_period_slow:
            htf_stc, _ = self.calc_stc(self.close_prices, mid_period_fast, mid_period_slow, mid_period_cycle)
            self.state_map.htf_stc_value = htf_stc
        else:
            self.state_map.htf_stc_value = 0
        if self.tick:
            price = self.tick.last_price if hasattr(self.tick, 'last_price') else self.close_prices[-1]
            self.state_map.stop_loss = round(price - self.state_map.atr * self.params_map.stop_mult, 2)
            self.state_map.take_profit = round(price + self.state_map.atr * self.params_map.profit_mult, 2)
            self.state_map.trailing_stop = round(price - self.state_map.atr * self.params_map.trail_step, 2)

    def update_dynamic_stops(self, current_price: float) -> None:
        """更新动态止盈止损 - 集成自适应逻辑"""
        if self.position_size > 0:
            # 如果使用固定止盈止损，则不更新动态止损
            if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
                return
            
            # 更新最高价和最低价
            if current_price > self.state_map.highest_price:
                self.state_map.highest_price = current_price
            if current_price < self.state_map.lowest_price:
                self.state_map.lowest_price = current_price
            
            # 计算当前盈亏
            self.state_map.current_profit = (current_price - self.entry_price) * self.position_size
            
            # 更新最大盈亏
            if self.state_map.current_profit > self.state_map.max_profit:
                self.state_map.max_profit = self.state_map.current_profit
            
            # 如果启用了自适应止盈止损，则使用新的逻辑
            if self.params_map.enable_atr_stops:
                # 重新计算自适应止盈止损
                self.calc_adaptive_stops(current_price)
                self.calc_smart_trailing_stop(current_price)
                self.calc_profit_protection_stop(current_price)
            else:
                # 使用原有逻辑
                # 计算价格波动率
                if len(self.close_prices) >= 20:
                    price_volatility = np.std(self.close_prices[-20:])
                else:
                    price_volatility = abs(current_price * 0.02)  # 默认2%波动率
                
                # 动态调整止损
                if self.state_map.current_profit > 0:
                    # 当盈利超过波动率的1倍时，启动追踪止损
                    if not self.is_trailing and self.state_map.current_profit > price_volatility:
                        self.is_trailing = True
                    
                    if self.is_trailing:
                        # 使用最高价回撤波动率的倍数作为止损
                        self.state_map.stop_loss = round(
                            self.state_map.highest_price - price_volatility * self.current_params["stop_mult"], 2
                        )
                else:
                    # 未盈利时使用初始止损
                    self.state_map.stop_loss = round(
                        self.entry_price - price_volatility * self.current_params["stop_mult"], 2
                    )

    def calc_adaptive_stops(self, current_price: float) -> None:
        """计算自适应止盈止损"""
        if not self.params_map.enable_atr_stops:
            return
        
        # 计算ATR
        if (hasattr(self, 'high_prices') and hasattr(self, 'low_prices') and 
            hasattr(self, 'close_prices') and 
            len(self.high_prices) >= 2 and len(self.low_prices) >= 2 and len(self.close_prices) >= 2):
            atr = self.calc_atr(self.high_prices, self.low_prices, self.close_prices, self.params_map.atr_period)
            self.state_map.atr_value = atr
        else:
            # 如果没有高低价数据，使用价格波动率估算ATR
            if len(self.close_prices) >= 20:
                atr = np.std(self.close_prices[-20:]) * 1.5  # 估算ATR
            else:
                atr = current_price * 0.02  # 默认2%
            self.state_map.atr_value = atr
        
        # 计算自适应波动率
        volatility, regime = self.calc_adaptive_volatility(self.close_prices, self.params_map.volatility_lookback)
        self.state_map.current_volatility = volatility
        
        # 确保regime不为空
        if not regime or regime == "":
            regime = "中等"
        self.state_map.volatility_regime = regime
        
        # 根据波动率制度调整乘数
        if regime == "高":
            self.state_map.volatility_multiplier = 1.5  # 高波动率时放宽止损
        elif regime == "低":
            self.state_map.volatility_multiplier = 0.8  # 低波动率时收紧止损
        else:
            self.state_map.volatility_multiplier = 1.0
        
        # 计算自适应止损止盈 - 支持多空双向
        adaptive_stop_distance = atr * self.params_map.atr_multiplier_stop * self.state_map.volatility_multiplier
        adaptive_profit_distance = atr * self.params_map.atr_multiplier_profit * self.state_map.volatility_multiplier
        
        # 根据持仓方向计算止盈止损
        if self.position_size > 0:  # 做多仓位
            self.state_map.adaptive_stop_loss = round(current_price - adaptive_stop_distance, 2)
            self.state_map.adaptive_take_profit = round(current_price + adaptive_profit_distance, 2)
        elif self.position_size < 0:  # 做空仓位
            self.state_map.adaptive_stop_loss = round(current_price + adaptive_stop_distance, 2)
            self.state_map.adaptive_take_profit = round(current_price - adaptive_profit_distance, 2)
        else:  # 无仓位，基于当前价格计算
            self.state_map.adaptive_stop_loss = round(current_price - adaptive_stop_distance, 2)
            self.state_map.adaptive_take_profit = round(current_price + adaptive_profit_distance, 2)
        
        # 更新主止损止盈
        if self.params_map.enable_atr_stops:
            self.state_map.stop_loss = self.state_map.adaptive_stop_loss
            self.state_map.take_profit = self.state_map.adaptive_take_profit

    def calc_smart_trailing_stop(self, current_price: float) -> None:
        """计算智能追踪止损"""
        if not self.params_map.enable_smart_trailing or self.position_size == 0:
            return
        
        # 计算当前盈亏
        current_profit = (current_price - self.entry_price) * self.position_size
        self.state_map.current_profit = current_profit
        
        # 更新最大盈亏
        if current_profit > self.state_map.max_profit:
            self.state_map.max_profit = current_profit
        
        # 计算追踪激活条件
        atr = self.state_map.atr_value
        activation_distance = atr * self.params_map.trailing_activation
        
        # 检查是否激活追踪止损
        if not self.state_map.trailing_activated and current_profit > activation_distance:
            self.state_map.trailing_activated = True
            self.state_map.trailing_start_price = current_price
            self.trailing_high = current_price
            self.trailing_low = current_price
        
        # 如果追踪已激活，更新追踪止损
        if self.state_map.trailing_activated:
            trailing_distance = atr * self.params_map.trailing_distance * self.state_map.volatility_multiplier
            
            if self.position_size > 0:  # 做多仓位
                # 更新追踪期间最高价
                if current_price > self.trailing_high:
                    self.trailing_high = current_price
                
                # 计算追踪止损价
                self.state_map.smart_trailing_stop = round(self.trailing_high - trailing_distance, 2)
                
                # 更新主止损价（取较大值，保护盈利）
                if self.state_map.smart_trailing_stop > self.state_map.stop_loss:
                    self.state_map.stop_loss = self.state_map.smart_trailing_stop
                    
            elif self.position_size < 0:  # 做空仓位
                # 更新追踪期间最低价
                if current_price < self.trailing_low:
                    self.trailing_low = current_price
                
                # 计算追踪止损价
                self.state_map.smart_trailing_stop = round(self.trailing_low + trailing_distance, 2)
                
                # 更新主止损价（取较小值，保护盈利）
                if self.state_map.smart_trailing_stop < self.state_map.stop_loss or self.state_map.stop_loss == 0:
                    self.state_map.stop_loss = self.state_map.smart_trailing_stop

    def calc_profit_protection_stop(self, current_price: float) -> None:
        """计算盈亏回撤保护"""
        if not self.params_map.enable_profit_protection or self.position_size == 0:
            return
        
        current_profit = (current_price - self.entry_price) * self.position_size
        atr = self.state_map.atr_value
        
        # 计算盈利锁定条件
        lock_threshold = atr * self.params_map.profit_lock_threshold
        lock_distance = atr * self.params_map.profit_lock_distance
        
        # 检查是否激活盈利锁定
        if not self.state_map.profit_locked and current_profit > lock_threshold:
            self.state_map.profit_locked = True
            self.state_map.profit_lock_price = current_price
            self.profit_lock_start_time = len(self.close_prices)
        
        # 如果盈利已锁定，计算盈利保护止损
        if self.state_map.profit_locked:
            if self.position_size > 0:  # 做多仓位
                # 计算盈利保护止损价（从锁定价格回撤一定距离）
                self.state_map.profit_lock_stop = round(self.state_map.profit_lock_price - lock_distance, 2)
                
                # 更新主止损价（取较大值，保护已锁定盈利）
                if self.state_map.profit_lock_stop > self.state_map.stop_loss:
                    self.state_map.stop_loss = self.state_map.profit_lock_stop
                    
            elif self.position_size < 0:  # 做空仓位
                # 计算盈利保护止损价（从锁定价格回撤一定距离）
                self.state_map.profit_lock_stop = round(self.state_map.profit_lock_price + lock_distance, 2)
                
                # 更新主止损价（取较小值，保护已锁定盈利）
                if self.state_map.profit_lock_stop < self.state_map.stop_loss or self.state_map.stop_loss == 0:
                    self.state_map.stop_loss = self.state_map.profit_lock_stop

    def reset_adaptive_stops(self) -> None:
        """重置自适应止盈止损状态"""
        self.state_map.trailing_activated = False
        self.state_map.profit_locked = False
        self.state_map.trailing_start_price = 0
        self.state_map.profit_lock_price = 0
        self.trailing_high = 0
        self.trailing_low = 0
        self.state_map.smart_trailing_stop = 0
        self.state_map.profit_lock_stop = 0

    def calc_signal(self, kline: KLineData):
        """计算交易信号并更新字符串状态"""
        self.buy_signal = False
        self.sell_signal = False
        
        # 更新HULL信号状态
        if self.state_map.stc_value < 25 and self.state_map.hull_ma > self.state_map.ma20:
            self.buy_signal = True
            self.state_map.hull_signal = "买入"
        elif self.state_map.stc_value > 75 and self.state_map.hull_ma < self.state_map.ma20:
            self.sell_signal = True
            self.state_map.hull_signal = "卖出"
        else:
            self.state_map.hull_signal = "等待"
        
        # 更新STC趋势状态
        if self.state_map.stc_value > 70:
            self.state_map.stc_trend = "超买"
        elif self.state_map.stc_value < 30:
            self.state_map.stc_trend = "超卖"
        else:
            self.state_map.stc_trend = "中性"
        
        # 更新HULL与价格关系
        if self.state_map.hull_ma > kline.close:
            self.state_map.hull_price_relation = "价格在HULL下方"
        elif self.state_map.hull_ma < kline.close:
            self.state_map.hull_price_relation = "价格在HULL上方"
        else:
            self.state_map.hull_price_relation = "价格与HULL接近"
        
        self.long_price = self.short_price = kline.close
        if self.tick:
            self.long_price = self.tick.ask_price1
            self.short_price = self.tick.bid_price1
            if self.params_map.price_type == "D2":
                self.long_price = self.tick.ask_price2
                self.short_price = self.tick.bid_price2

    def exec_signal(self):
        self.signal_price = 0
        position = self.get_position(self.params_map.instrument_id)
        if self.order_id is not None:
            self.cancel_order(self.order_id)
        if position.net_position > 0 and self.sell_signal:
            self.signal_price = -self.short_price
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.short_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
        elif position.net_position < 0 and self.buy_signal:
            self.signal_price = self.long_price
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=abs(position.net_position),
                    order_direction="buy"
                )
        if self.sell_signal and position.net_position == 0:
            self.signal_price = -self.short_price
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.short_price,
                    order_direction="sell"
                )
        elif self.buy_signal and position.net_position == 0:
            self.signal_price = self.long_price
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.long_price,
                    order_direction="buy"
                )

