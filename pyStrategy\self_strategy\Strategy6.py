from typing import Literal, Callable, Optional, Dict, Any, List, Tuple
import numpy as np
from datetime import datetime
import math
from scipy.linalg import solve
from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator

# 直觉梯形模糊数类（核心）
class IntuitiveTrapezoidalFuzzyNumber:
    """终极版直觉梯形模糊数（ITrFN）"""
    def __init__(self, a: float, b: float, c: float, d: float, 
                 mu: float, nu: float, a1: Optional[float] = None, 
                 d1: Optional[float] = None):
        self.a = a
        self.b = b
        self.c = c
        self.d = d
        self.mu = mu
        self.nu = nu
        self.a1 = a1 if a1 is not None else a
        self.d1 = d1 if d1 is not None else d
        
        # 参数验证
        assert a <= b <= c <= d, "Invalid membership parameters"
        assert self.a1 <= b <= c <= self.d1, "Invalid non-membership parameters"
        assert 0 <= mu <= 1, "Membership peak must be in [0,1]"
        assert 0 <= nu <= 1, "Non-membership valley must be in [0,1]"
        assert mu + nu <= 1, "Membership and non-membership sum must <=1"
    
    def membership(self, x: float) -> float:
        """计算x的隶属度"""
        if x < self.a or x > self.d:
            return 0.0
        elif self.a <= x < self.b:
            return self.mu * (x - self.a) / (self.b - self.a)
        elif self.b <= x <= self.c:
            return self.mu
        else:  # self.c < x <= self.d
            return self.mu * (self.d - x) / (self.d - self.c)
    
    def non_membership(self, x: float) -> float:
        """计算x的非隶属度"""
        if x < self.a1 or x > self.d1:
            return 1.0
        elif self.a1 <= x < self.b:
            return self.nu + (1 - self.nu) * (x - self.a1) / (self.b - self.a1)
        elif self.b <= x <= self.c:
            return self.nu
        else:  # self.c < x <= self.d1
            return self.nu + (1 - self.nu) * (self.d1 - x) / (self.d1 - self.c)
    
    def hesitation(self, x: float) -> float:
        """计算x的犹豫度"""
        return 1 - self.membership(x) - self.non_membership(x)
    
    def centroid_x(self) -> float:
        """计算重心横坐标期望值（简化计算）"""
        # 隶属函数区域重心
        area_mu = self.mu * (self.d - self.a + self.c - self.b) / 2
        if area_mu > 1e-6:
            x_l = (self.a + self.b + self.c + self.d) / 4
        else:
            x_l = (self.a + self.d) / 2
        
        # 非隶属函数区域重心
        area_nu = (1 - self.nu) * (self.d1 - self.a1 + self.c - self.b) / 2
        if area_nu > 1e-6:
            x_f = (self.a1 + self.b + self.c + self.d1) / 4
        else:
            x_f = (self.a1 + self.d1) / 2
        
        # 犹豫度区域重心 (简化计算)
        x_d = (self.a + self.b + self.c + self.d) / 4.0
        
        # 加权平均
        return (x_l + x_f + x_d) / 3.0
    
    def expected_value(self) -> float:
        """计算期望值（0-1范围）"""
        return min(1.0, max(0.0, (self.centroid_x() - self.a) / (self.d - self.a)))

# 混合模糊系统类
class HybridFuzzySystem:
    """终极混合模糊决策系统"""
    def __init__(self):
        """
        初始化混合模糊系统，包含五大市场维度的模糊集定义和智能规则库
        
        维度说明：
        1. 波动率(volatility): 市场波动程度，低波动有利
        2. 趋势强度(trend_strength): 市场趋势强度，绝对值大有利
        3. 流动性(liquidity): 市场流动性，高流动有利
        4. 系统稳定性(stability): 策略系统稳定性
        5. 盈利水平(profit): 当前策略盈利水平
        """
        # 市场状态模糊集定义 - 使用ITrFN
        # 波动率模糊集 (低波动有利)
        self.volatility_sets = {
            "Low": IntuitiveTrapezoidalFuzzyNumber(0.0, 0.01, 0.03, 0.05, 0.9, 0.1),
            "Medium": IntuitiveTrapezoidalFuzzyNumber(0.03, 0.05, 0.07, 0.09, 0.7, 0.2),
            "High": IntuitiveTrapezoidalFuzzyNumber(0.07, 0.09, 0.12, 0.15, 0.1, 0.9, 0.05, 0.17)
        }
        
        # 趋势强度模糊集 (绝对值大有利)
        self.trend_sets = {
            "StrongDown": IntuitiveTrapezoidalFuzzyNumber(-1.0, -0.8, -0.6, -0.4, 0.9, 0.1),
            "WeakDown": IntuitiveTrapezoidalFuzzyNumber(-0.5, -0.4, -0.2, 0.0, 0.6, 0.3),
            "Neutral": IntuitiveTrapezoidalFuzzyNumber(-0.2, -0.1, 0.1, 0.2, 0.1, 0.8),
            "WeakUp": IntuitiveTrapezoidalFuzzyNumber(0.0, 0.2, 0.4, 0.5, 0.6, 0.3),
            "StrongUp": IntuitiveTrapezoidalFuzzyNumber(0.4, 0.6, 0.8, 1.0, 0.9, 0.1)
        }
        
        # 其他模糊集保持不变
        self.liquidity_sets = {
            "Low": IntuitiveTrapezoidalFuzzyNumber(0.5, 0.6, 0.8, 1.0, 0.1, 0.9),
            "Medium": IntuitiveTrapezoidalFuzzyNumber(0.8, 1.0, 1.2, 1.5, 0.7, 0.2),
            "High": IntuitiveTrapezoidalFuzzyNumber(1.2, 1.5, 1.8, 2.0, 0.9, 0.1)
        }
        
        # 系统稳定性模糊集
        self.stability_sets = {
            "Low": IntuitiveTrapezoidalFuzzyNumber(0.0, 0.1, 0.3, 0.4, 0.1, 0.9),
            "Medium": IntuitiveTrapezoidalFuzzyNumber(0.3, 0.4, 0.6, 0.7, 0.7, 0.2),
            "High": IntuitiveTrapezoidalFuzzyNumber(0.6, 0.7, 0.9, 1.0, 0.9, 0.1)
        }
        
        # 盈利水平模糊集 (策略2)
        self.profit_sets = {
            "Negative": IntuitiveTrapezoidalFuzzyNumber(-0.1, -0.08, -0.03, 0.0, 0.1, 0.9),
            "Low": IntuitiveTrapezoidalFuzzyNumber(-0.02, 0.0, 0.02, 0.04, 0.6, 0.3),
            "Medium": IntuitiveTrapezoidalFuzzyNumber(0.02, 0.04, 0.06, 0.08, 0.7, 0.2),
            "High": IntuitiveTrapezoidalFuzzyNumber(0.06, 0.08, 0.12, 0.15, 0.9, 0.1)
        }
        
        # 模糊规则库 (整合两个策略的规则)
        self.rules = [
            # 高风险规则
            {"condition": lambda v, t, l, s, p: (
                v["High"].expected_value() > 0.7 or
                s["Low"].expected_value() > 0.7),
             "action": lambda: ("RiskHigh", "Stop", 0.9)},
            
            # 高盈利+强趋势规则
            {"condition": lambda v, t, l, s, p: (
                (t["StrongUp"].expected_value() > 0.8 or 
                 t["StrongDown"].expected_value() > 0.8) and 
                p["High"].expected_value() > 0.7 and
                l["High"].expected_value() > 0.6),
             "action": lambda: ("RiskLow", "Aggressive", 0.95)},
            
            # 中性市场规则
            {"condition": lambda v, t, l, s, p: (
                (t["Neutral"].expected_value() > 0.6 or
                 t["WeakUp"].expected_value() > 0.6 or
                 t["WeakDown"].expected_value() > 0.6) and 
                v["Medium"].expected_value() > 0.6 and
                l["Medium"].expected_value() > 0.6),
             "action": lambda: ("RiskMedium", "Normal", 0.75)},
            
            # 低波动+稳定系统规则
            {"condition": lambda v, t, l, s, p: (
                v["Low"].expected_value() > 0.8 and 
                s["High"].expected_value() > 0.8),
             "action": lambda: ("RiskLow", "Aggressive", 0.85)}
        ]
        
        # 规则初始强度
        self.rule_strength = [1.0] * len(self.rules)
        self.learning_rate = 0.05  # 规则学习率
        self.rule_history = []     # 规则激活历史记录
    
    def fuzzify(self, volatility: float, trend_strength: float, 
                liquidity: float, stability: float, profit: float) -> Tuple[dict, dict, dict, dict, dict]:
        """模糊化输入值"""
        v_membership = {k: f.membership(volatility) for k, f in self.volatility_sets.items()}
        t_membership = {k: f.membership(trend_strength) for k, f in self.trend_sets.items()}
        l_membership = {k: f.membership(liquidity) for k, f in self.liquidity_sets.items()}
        s_membership = {k: f.membership(stability) for k, f in self.stability_sets.items()}
        p_membership = {k: f.membership(profit) for k, f in self.profit_sets.items()}
        
        return v_membership, t_membership, l_membership, s_membership, p_membership
    
    def infer(self, v_mem: dict, t_mem: dict, l_mem: dict, s_mem: dict, p_mem: dict) -> Tuple[str, str, float]:
        """基于ITrFN的模糊推理"""
        activated_rules = []
        
        # 应用规则强度
        for i, rule in enumerate(self.rules):
            strength = rule["condition"](self.volatility_sets, self.trend_sets, 
                                         self.liquidity_sets, self.stability_sets, self.profit_sets)
            if strength > 0:
                action = rule["action"]()
                # 应用当前规则强度
                activated_rules.append((action, strength * self.rule_strength[i]))
                # 记录激活的规则
                self.rule_history.append((i, strength))
                if len(self.rule_history) > 100:
                    self.rule_history.pop(0)
        
        if not activated_rules:
            # 默认规则
            return ("RiskMedium", "Normal", 0.5)
        
        # 计算加权决策
        risk_sum = 0.0
        action_sum = 0.0
        confidence_sum = 0.0
        total_strength = sum(strength for _, strength in activated_rules)
        
        for (risk, action, conf), strength in activated_rules:
            risk_value = self._risk_to_value(risk)
            action_value = self._action_to_value(action)
            
            risk_sum += risk_value * strength
            action_sum += action_value * strength
            confidence_sum += conf * strength
        
        # 归一化
        risk_value = risk_sum / total_strength
        action_value = action_sum / total_strength
        confidence_value = confidence_sum / total_strength
        
        # 映射回语义值
        risk_level = self._value_to_risk(risk_value)
        action_level = self._value_to_action(action_value)
        
        return risk_level, action_level, confidence_value
    
    def update_rule_strength(self, profit_change: float):
        """根据盈亏表现动态调整规则强度
        
        参数说明:
            - profit_change: 盈亏变化值，正数表示盈利，负数表示亏损
            - 规则强度范围限制在[0.2, 1.5]之间
            - 盈利时增强最近激活的规则，亏损时减弱最近激活的规则
        """
        if not self.rule_history:  # 无激活规则历史则跳过
            return
        
        # 获取最近激活的规则索引和激活强度
        last_activated = self.rule_history[-1]
        rule_index, activation_strength = last_activated
        
        # 根据盈亏方向调整规则强度
        if profit_change > 0:
            # 盈利时增强规则(最大不超过1.5)
            new_strength = self.rule_strength[rule_index] + \
                          (self.learning_rate * activation_strength)
            self.rule_strength[rule_index] = min(1.5, new_strength)
        else:
            # 亏损时减弱规则(最小不低于0.2)
            new_strength = self.rule_strength[rule_index] - \
                          (self.learning_rate * activation_strength)
            self.rule_strength[rule_index] = max(0.2, new_strength)
    
    def _risk_to_value(self, risk: str) -> float:
        risk_mapping = {
            "无风险": 0.0, 
            "低风险": 0.3, 
            "中等风险": 0.6, 
            "高风险": 0.9
        }
        return risk_mapping.get(risk, 0.6)  # 默认中等风险
    
    def _action_to_value(self, action: str) -> float:
        action_mapping = {
            "停止": 0.0, 
            "保守": 0.3, 
            "正常": 0.6, 
            "激进": 0.9
        }
        return action_mapping.get(action, 0.6)  # 默认正常
    
    def _value_to_risk(self, value: float) -> str:
        if value < 0.15: return "无风险"
        elif value < 0.45: return "低风险"
        elif value < 0.75: return "中等风险"
        else: return "高风险"
    
    def _value_to_action(self, value: float) -> str:
        if value < 0.15: return "停止"
        elif value < 0.45: return "保守"
        elif value < 0.75: return "正常"
        else: return "激进"

# 控制中心类
class ControlCenter:
    """终极控制中心"""
    def __init__(self, strategy):
        self.strategy = strategy
        self.fuzzy_system = HybridFuzzySystem()
        self.decision_history = []
        self.last_decision = ("RiskMedium", "Normal", 0.5)
        
        # 稳定性分析参数
        self.A_matrix = np.array([[-0.2, 0.1], [0.05, -0.15]])
        self.P_matrix = self.solve_lyapunov_equation()
        self.lyapunov_history = []
    
    def solve_lyapunov_equation(self) -> np.ndarray:
        """解李雅普诺夫方程: A'P + PA = -I"""
        n = self.A_matrix.shape[0]
        A = self.A_matrix
        I = np.eye(n)
        A_kron = np.kron(A.T, np.eye(n)) + np.kron(np.eye(n), A.T)
        b = -I.flatten()
        p = np.linalg.solve(A_kron, b)
        P = p.reshape(n, n)
        return (P + P.T) / 2
    
    def check_stability(self, state: np.ndarray) -> float:
        """基于控制理论的稳定性分析
        
        参数:
            state: 系统状态向量(2维)
            
        返回:
            float: 李雅普诺夫函数值，用于评估系统稳定性
            
        功能:
            1. 计算当前状态的李雅普诺夫函数值 V = x^T P x
            2. 记录历史值用于趋势分析
            3. 返回标准化后的稳定性指标
        """
        # 计算李雅普诺夫函数值
        V = state.T @ self.P_matrix @ state
        
        # 记录历史值(保留最近100个)
        self.lyapunov_history.append(V)
        if len(self.lyapunov_history) > 100:
            self.lyapunov_history.pop(0)
        
        # 标准化处理(0-1范围)
        normalized_V = min(1.0, max(0.0, V / 10.0))  # 假设最大值为10
        
        return float(normalized_V)
    
    def trapezoidal_fuzzy_decision(self, volatility: float, trend_strength: float, 
                                  liquidity: float, stability: float, profit: float) -> Tuple[str, str, float]:
        """直觉梯形模糊决策"""
        # 模糊化输入
        v_mem, t_mem, l_mem, s_mem, p_mem = self.fuzzy_system.fuzzify(
            volatility, trend_strength, liquidity, stability, profit
        )
        
        # 执行推理
        decision = self.fuzzy_system.infer(v_mem, t_mem, l_mem, s_mem, p_mem)
        self.last_decision = decision
        
        # 记录决策历史
        self.decision_history.append({
            "volatility": volatility,
            "trend_strength": trend_strength,
            "liquidity": liquidity,
            "stability": stability,
            "profit": profit,
            "decision": decision
        })
        if len(self.decision_history) > 100:
            self.decision_history.pop(0)
        
        return decision
    
    def update_rule_strength(self, profit_change: float):
        """更新规则强度"""
        self.fuzzy_system.update_rule_strength(profit_change)

# 策略参数和状态
class Params(BaseParams):
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K 线周期")
    tech_type: Literal["STC_HULL"] = Field(default="STC_HULL", title="技术指标")
    trade_direction: Literal["buy", "sell", "auto"] = Field(default="auto", title="交易方向")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位") 
    order_volume: int = Field(default=1, title="报单数量")
    trail_profit_start: float = Field(default=0.03, title="追踪止盈启动(3%)")
    trail_profit_stop: float = Field(default=0.01, title="追踪回撤平仓(1%)")
    quick_stop_loss: float = Field(default=0.02, title="快速止损(2%)")
    volatility_threshold: float = Field(default=0.05, title="波动率阈值")
    stability_margin: float = Field(default=0.7, title="稳定裕度")
    trend_period: int = Field(default=14, title="趋势强度周期")
    liquidity_period: int = Field(default=5, title="流动性评估周期")

class State(BaseState):
    stc_value: float = Field(default=0, title="STC值")
    stc_signal: float = Field(default=0, title="STC信号线")
    hull_value: float = Field(default=0, title="HULL值")
    hull_prev: float = Field(default=0, title="HULL前值")
    position_cost: float = Field(default=0.0, title="持仓成本价")
    max_profit: float = Field(default=0.0, title="最高盈利比例")
    stop_triggered: bool = Field(default=False, title="止损触发标志")
    system_stability: float = Field(default=1.0, title="系统稳定指数")
    filtered_price: float = Field(default=0.0, title="滤波后价格")
    volatility_index: float = Field(default=0.0, title="波动率指数")
    trend_strength: float = Field(default=0.0, title="趋势强度")
    liquidity_index: float = Field(default=1.0, title="流动性指数")
    fuzzy_risk: str = Field(default="中等风险", title="模糊风险等级")
    fuzzy_action: str = Field(default="正常", title="模糊行动级别")
    fuzzy_confidence: float = Field(default=0.5, title="模糊决策置信度")
    lyapunov_value: float = Field(default=0.0, title="李雅普诺夫值")

# 终极策略类
class Strategy6(BaseStrategy):
    """终极整合策略"""
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()
        self.control_center = ControlCenter(self)
        
        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        self.cover_signal: bool = False
        self.short_signal: bool = False
        
        # 市场数据
        self.tick: TickData = None
        self.price_history = []
        self.volume_history = []
        
        # 技术指标
        self.bars = []
        
        # 订单管理
        self.order_id = None
        self.signal_price = 0
        
        # 系统状态
        self.last_update = datetime.now()
        self.last_profit = 0.0

    def on_tick(self, tick: TickData):
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)
        
        # 更新价格和成交量历史
        mid_price = (tick.ask_price1 + tick.bid_price1) / 2
        self.price_history.append(mid_price)
        self.volume_history.append(tick.volume)
        
        # 保留最近1000个数据点
        if len(self.price_history) > 1000:
            self.price_history.pop(0)
        if len(self.volume_history) > 1000:
            self.volume_history.pop(0)
        
        # 每分钟执行模糊决策
        current_time = datetime.now()
        if (current_time - self.last_update).seconds > 60:
            self.execute_fuzzy_decision()
            self.last_update = current_time

    def calc_market_indicators(self):
        """计算市场关键指标"""
        if len(self.price_history) < 10:
            return
            
        # 波动率计算
        returns = np.diff(np.log(self.price_history[-10:]))
        self.state_map.volatility_index = np.std(returns) * math.sqrt(252)
        
        # 趋势强度计算
        highs = np.array([max(self.price_history[i-9:i+1]) for i in range(9, len(self.price_history))])
        lows = np.array([min(self.price_history[i-9:i+1]) for i in range(9, len(self.price_history))])
        
        plus_dm = highs[1:] - highs[:-1]
        minus_dm = lows[:-1] - lows[1:]
        plus_dm[plus_dm <= minus_dm] = 0
        minus_dm[minus_dm <= plus_dm] = 0
        
        tr = np.maximum(highs[1:] - lows[1:], 
                       np.abs(highs[1:] - np.array(self.price_history[9:-1])), 
                       np.abs(lows[1:] - np.array(self.price_history[9:-1])))
        
        plus_di = 100 * np.mean(plus_dm) / np.mean(tr)
        minus_di = 100 * np.mean(minus_dm) / np.mean(tr)
        dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di + 1e-9)
        self.state_map.trend_strength = dx / 100
        
        # 流动性计算
        if len(self.volume_history) > self.params_map.liquidity_period:
            volumes = np.array(self.volume_history[-self.params_map.liquidity_period:])
            avg_volume = np.mean(volumes[:-1])
            current_volume = volumes[-1]
            self.state_map.liquidity_index = min(2.0, max(0.5, current_volume / avg_volume))
        
        # 系统稳定性(使用夏普比率)
        returns = [self.price_history[i]/self.price_history[i-1] - 1 for i in range(1, len(self.price_history))]
        if returns:
            sharpe_ratio = np.mean(returns) / (np.std(returns) + 1e-9)
            self.state_map.system_stability = max(0.0, min(1.0, sharpe_ratio * 0.5 + 0.5))
        
        # 当前盈利计算
        current_profit = 0.0
        if self.state_map.position_cost > 0:
            current_profit = (self.state_map.filtered_price - self.state_map.position_cost) / self.state_map.position_cost

    def execute_fuzzy_decision(self):
        """执行直觉梯形模糊决策"""
        # 更新市场指标
        self.calc_market_indicators()
        
        # 当前盈利计算
        current_profit = 0.0
        if self.state_map.position_cost > 0:
            current_profit = (self.state_map.filtered_price - self.state_map.position_cost) / self.state_map.position_cost
        
        # 执行模糊决策
        decision = self.control_center.trapezoidal_fuzzy_decision(
            volatility=self.state_map.volatility_index,
            trend_strength=self.state_map.trend_strength,
            liquidity=self.state_map.liquidity_index,
            stability=self.state_map.system_stability,
            profit=current_profit
        )
        
        # 应用决策到状态
        self.state_map.fuzzy_risk = decision[0]
        self.state_map.fuzzy_action = decision[1]
        self.state_map.fuzzy_confidence = decision[2]
        
        # 更新状态栏
        self.update_status_bar()
        
        # 更新规则强度
        profit_change = current_profit - self.last_profit if hasattr(self, 'last_profit') else 0
        self.control_center.update_rule_strength(profit_change)
        self.last_profit = current_profit

    def calc_stc_hull_signal(self):
        """计算STC-HULL信号，根据模糊决策动态调整阈值
        
        参数说明:
            - 根据fuzzy_action和fuzzy_confidence动态调整交易阈值
            - 激进模式: 降低阈值，更敏感
            - 保守模式: 提高阈值，更保守
            - 正常模式: 使用默认阈值
        """
        # 根据模糊决策动态调整阈值
        if self.state_map.fuzzy_action == "激进":
            stc_threshold = 0.5 - (self.state_map.fuzzy_confidence * 0.2)
            hull_threshold = 0.3 - (self.state_map.fuzzy_confidence * 0.1)
        elif self.state_map.fuzzy_action == "保守":
            stc_threshold = 0.7 + (self.state_map.fuzzy_confidence * 0.1)
            hull_threshold = 0.5 + (self.state_map.fuzzy_confidence * 0.1)
        else:  # 正常模式
            stc_threshold = 0.6
            hull_threshold = 0.4
        
        # 计算STC指标信号
        stc_signal = False
        if self.state_map.stc_value > stc_threshold and \
           self.state_map.stc_signal > stc_threshold:
            stc_signal = True
        
        # 计算HULL指标信号
        hull_signal = False
        if self.state_map.hull_value > hull_threshold and \
           self.state_map.hull_value > self.state_map.hull_prev:
            hull_signal = True
        
        # 综合信号
        return stc_signal and hull_signal

    def calc_signal(self, kline: KLineData):
        self.calc_stc_hull_signal()
        self.long_price = self.short_price = kline.close
        
        if self.tick:
            self.long_price = max(self.state_map.filtered_price, self.tick.ask_price1)
            self.short_price = min(self.state_map.filtered_price, self.tick.bid_price1)
            
            if self.params_map.price_type == "D2":
                self.long_price = max(self.state_map.filtered_price, self.tick.ask_price2)
                self.short_price = min(self.state_map.filtered_price, self.tick.bid_price2)

    def on_order(self, order: OrderData):
        """订单回调"""
        if order.status == "filled":
            if order.offset == "open":
                # 开仓时记录成本价
                self.state_map.position_cost = order.traded * (1.0003 if order.direction == "long" else 0.9997)
                self.state_map.max_profit = 0.0
                self.state_map.stop_triggered = False
                self.write_log(f"开仓成功: {order.direction} {order.volume}手 @ {order.traded:.2f}")
            else:
                # 平仓时重置状态
                self.state_map.position_cost = 0.0
                self.state_map.max_profit = 0.0
                self.state_map.stop_triggered = False
                self.write_log(f"平仓成功: {order.direction} {order.volume}手 @ {order.traded:.2f}")
                
            # 更新李雅普诺夫稳定性
            state_vector = np.array([
                self.state_map.volatility_index,
                self.state_map.trend_strength,
                self.state_map.system_stability
            ])
            self.state_map.lyapunov_value = self.control_center.check_stability(state_vector)

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """成交回调"""
        super().on_trade(trade, log)
        self.order_id = None
        
        # 计算本次交易利润
        if trade.offset == "close":
            if self.state_map.position_cost > 0:
                profit = (trade.price - self.state_map.position_cost) / self.state_map.position_cost
                self.write_log(f"平仓利润: {profit:.2%}")
                # 更新规则强度
                self.control_center.fuzzy_system.update_rule_strength(profit)

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单回调"""
        super().on_order_cancel(order)
        self.order_id = None

    def calc_stc_hull_signal(self):
        """终极信号生成（含止盈止损）"""
        # 1. 检查止盈止损条件
        current_price = self.state_map.filtered_price
        if self.state_map.position_cost > 0:
            profit_ratio = (current_price - self.state_map.position_cost) / self.state_map.position_cost
            
            # 追踪止盈逻辑
            if profit_ratio >= self.params_map.trail_profit_start:
                self.state_map.max_profit = max(self.state_map.max_profit, profit_ratio)
                if profit_ratio < (self.state_map.max_profit - self.params_map.trail_profit_stop):
                    self.cover_signal = True
                    self.sell_signal = True
                    self.state_map.stop_triggered = True
                    return
            
            # 快速止损逻辑
            if profit_ratio <= -self.params_map.quick_stop_loss:
                self.cover_signal = True
                self.sell_signal = True
                self.state_map.stop_triggered = True
                return
        
        # 2. 根据模糊决策调整信号阈值
        if self.state_map.fuzzy_action == "激进":
            stc_threshold = 0.5 - (self.state_map.fuzzy_confidence * 0.2)
            hull_threshold = 0.3 - (self.state_map.fuzzy_confidence * 0.1)
        elif self.state_map.fuzzy_action == "保守":
            stc_threshold = 0.7 + (self.state_map.fuzzy_confidence * 0.1)
            hull_threshold = 0.5 + (self.state_map.fuzzy_confidence * 0.1)
        else:  # 正常
            stc_threshold = 0.6
            hull_threshold = 0.4
        
        # 3. 原始指标信号
        stc_bull = self.state_map.stc_value > self.state_map.stc_signal + stc_threshold
        hull_bull = self.state_map.hull_value > self.state_map.hull_prev + hull_threshold
        
        # 4. 综合信号处理
        if self.params_map.trade_direction == "auto":
            self.buy_signal = stc_bull and hull_bull and not self.state_map.stop_triggered
            self.short_signal = not stc_bull and not hull_bull and not self.state_map.stop_triggered
            self.cover_signal = self.buy_signal
            self.sell_signal = self.short_signal
        else:
            self.buy_signal = stc_bull and hull_bull and not self.state_map.stop_triggered
            self.short_signal = not stc_bull and not hull_bull and not self.state_map.stop_triggered
            
            if self.params_map.trade_direction == "buy":
                self.buy_signal, self.short_signal = self.short_signal, self.buy_signal
            
            self.cover_signal = self.buy_signal
            self.sell_signal = self.short_signal
        
        # 5. 如果决策是"停止"，覆盖所有信号
        if self.state_map.fuzzy_action == "停止":
            self.buy_signal = False
            self.short_signal = False
            self.state_map.stop_triggered = True

    def exec_signal(self):
        """终极决策执行（增强版）"""
        self.signal_price = 0
        position = self.get_position(self.params_map.instrument_id)
        
        # 根据模糊风险等级调整交易量
        risk_factor = {
            "无风险": 0.0,
            "低风险": 0.3 + (self.state_map.fuzzy_confidence * 0.2),
            "中等风险": 0.6 + (self.state_map.fuzzy_confidence * 0.1),
            "高风险": 0.9 - ((1 - self.state_map.fuzzy_confidence) * 0.2)
        }
        
        # 获取当前风险等级对应的交易量因子
        current_risk_factor = risk_factor.get(self.state_map.fuzzy_risk, 0.6)
        adjusted_volume = int(self.params_map.order_volume * current_risk_factor)
        
        # 确保最小交易量为1
        adjusted_volume = max(1, adjusted_volume)
        
        # 如果风险等级为"无风险"或行动为"停止"，则不交易
        if self.state_map.fuzzy_risk == "无风险" or self.state_map.fuzzy_action == "停止":
            return
        
        if self.order_id is not None:
            self.cancel_order(self.order_id)
        
        # 平仓逻辑
        if position.net_position > 0 and self.sell_signal:
            self.execute_close_position(position, "sell")
        elif position.net_position < 0 and self.cover_signal:
            self.execute_close_position(position, "buy")
        
        # 开仓逻辑 - 仅在未持仓时开仓
        if position.net_position == 0:
            if self.short_signal:
                self.execute_open_position("sell", adjusted_volume)
            elif self.buy_signal:
                self.execute_open_position("buy", adjusted_volume)
        
        # 更新状态栏
        self.update_status_bar()

    def execute_close_position(self, position, direction):
        """执行平仓操作（增强版）"""
        if self.tick:
            # 根据价格档位选择最优价格
            if self.params_map.price_type == "D1":
                price = self.tick.bid_price1 if direction == "sell" else self.tick.ask_price1
            else:
                price = self.tick.bid_price2 if direction == "sell" else self.tick.ask_price2
            
            self.signal_price = -price if direction == "sell" else price
            
            self.order_id = self.auto_close_position(
                exchange=self.params_map.exchange,
                instrument_id=self.params_map.instrument_id,
                price=price,
                volume=abs(position.net_position),
                order_direction=direction
            )

    def execute_open_position(self, direction, volume):
        """执行开仓操作（增强版）"""
        if self.tick:
            # 根据价格档位选择最优价格
            if self.params_map.price_type == "D1":
                price = self.tick.ask_price1 if direction == "buy" else self.tick.bid_price1
            else:
                price = self.tick.ask_price2 if direction == "buy" else self.tick.bid_price2
            
            self.signal_price = price if direction == "buy" else -price
            
            self.order_id = self.send_order(
                exchange=self.params_map.exchange,
                instrument_id=self.params_map.instrument_id,
                volume=volume,
                price=price,
                order_direction=direction
            )

    def update_status_bar(self):
        """增强版状态栏"""
        risk_symbol = {
            "无风险": "🚫", 
            "低风险": "🟢", 
            "中等风险": "🟡", 
            "高风险": "🔴"
        }[self.state_map.fuzzy_risk]
        
        action_symbol = {
            "停止": "⏹️", 
            "保守": "🚸", 
            "正常": "➡️", 
            "激进": "💥"
        }[self.state_map.fuzzy_action]
        
        trend_symbol = "↑" if self.state_map.trend_strength > 0 else "↓"
        
        position_info = ""
        position = self.get_position(self.params_map.instrument_id)
        if position.net_position != 0:
            profit = (self.state_map.filtered_price - self.state_map.position_cost) / self.state_map.position_cost
            position_info = f" | 持仓:{position.net_position}手 盈亏:{profit:.2%}"
        
        status_text = (
            f"趋势: {abs(self.state_map.trend_strength):.2f}{trend_symbol} | "
            f"波动: {self.state_map.volatility_index:.4f} | "
            f"流动: {self.state_map.liquidity_index:.2f} | "
            f"稳定: {self.state_map.system_stability:.2f} | "
            f"决策: {risk_symbol}{action_symbol} {self.state_map.fuzzy_confidence:.0%}"
            f"{position_info}"
        )
        # 添加安全检查，避免 widget 为 None 的情况，使用正确的方法更新标题
        if hasattr(self, 'widget') and self.widget is not None:
            # 更新窗口标题
            self.widget.setWindowTitle(f"策略 - {self.strategy_name} | {status_text[:50]}...")
            # 更新图表标题显示状态
            if hasattr(self.widget, 'kline_widget') and self.widget.kline_widget is not None:
                display_title = f"{self.params_map.instrument_id} | {status_text[:30]}..."
                self.widget.kline_widget.set_title(display_title)

    # 以下为框架要求的标准方法实现
    def on_start(self):
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()
        super().on_start()
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False
        self.tick = None
        self.price_history = []
        self.volume_history = []
        self.update_status_bar()

    def on_stop(self):
        super().on_stop()

    def callback(self, kline: KLineData) -> None:
        self.calc_indicator()
        self.calc_signal(kline)
        self.exec_signal()
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            "STC_FAST": self.state_map.stc_value,
            "STC_SLOW": self.state_map.stc_signal,
            f"HULL": self.state_map.hull_value
        })

    def real_time_callback(self, kline: KLineData) -> None:
        self.calc_indicator()
        self.widget.recv_kline({
            "kline": kline,
            "STC_FAST": self.state_map.stc_value,
            "STC_SLOW": self.state_map.stc_signal,
            f"HULL": self.state_map.hull_value
        })
        self.update_status_bar()

    def calc_indicator(self) -> None:
        """计算STC和HULL指标"""
        if len(self.kline_generator.producer.close) < 30:
            return
            
        # STC指标计算
        fast_period = 5
        slow_period = 20
        cycle_period = 10
        signal_period = 3
        
        fast_ema = self.kline_generator.producer.ema(fast_period, array=True)
        slow_ema = self.kline_generator.producer.ema(slow_period, array=True)
        macd_line = fast_ema - slow_ema
        
        lowest = np.minimum.accumulate(macd_line[-cycle_period:])
        highest = np.maximum.accumulate(macd_line[-cycle_period:])
        stoch = 100 * (macd_line[-1] - lowest[-1]) / (highest[-1] - lowest[-1] + 1e-9)
        
        stc_value = self.kline_generator.producer.ema(signal_period, array=True, prices=stoch)[-1]
        stc_signal = self.kline_generator.producer.ema(signal_period, array=True, prices=stc_value)[-1]
        
        self.state_map.stc_value, self.state_map.stc_signal = np.round((stc_value, stc_signal), 2)

        # HULL指标计算
        hull_period = 9
        half_period = hull_period // 2
        wma_half = self.kline_generator.producer.wma(half_period, array=True)
        wma_full = self.kline_generator.producer.wma(hull_period, array=True)
        raw_hull = 2 * wma_half - wma_full
        sqrt_period = int(np.sqrt(hull_period))
        hull_ma = self.kline_generator.producer.wma(sqrt_period, array=True, prices=raw_hull)
        
        self.state_map.hull_prev, self.state_map.hull_value = np.round((hull_ma[-2], hull_ma[-1]), 2)

