更新时间：2023 年 06 月 01 日
生效时间：2023 年 06 月 01 日
　　感谢您对无限易的信赖和支持，本告知仅适用于量投科技（上海）股份有限公司（以下简称“<b>我们</b>”/“<b>我司</b>”）的无限易产品中 PythonGO 相关功能模块（以下简称“<b>PythonGO</b>”）。
　　交易有风险。当您使用 PythonGO 时，可能获得投资收益，但同时也面临着投资风险。您在做出投资决策之前，请仔细阅读本告知，<b>一旦您开始使用 PythonGO ，即表示您充分理解并同意本告知</b>。
 　 根据我国有关法律规定，现将 PythonGO 相关的风险告知如下：
　　1. 使用前自身<b>必须具备 Python 语言应用能力</b>，<b>且熟悉交易业务知识与规则</b>，本功能模块不适合新手或小白使用。
　　2. <b>PythonGO 中所有 DEMO 策略仅供学习</b>，<b>不代表能够一定会获得投资收益</b>，我司不对 DEMO 策略运行结果负责。
　　3. 使用 PythonGO 功能，代表您已认识到量化带来的风险，包括不限于:市场风险、技术风险、程序错误等。
　　4. 您具有金融交易的知识及技术，认真考虑交易存在的各项风险因素，并充分考虑自身的风险承受能力，理性判断并谨慎做出投资决策
　　使用或无法使用无限易进行交易造成的盈亏、直接或间接引起的赔偿、损失、债务或是任何交易中止，我司均不承担责任和义务。