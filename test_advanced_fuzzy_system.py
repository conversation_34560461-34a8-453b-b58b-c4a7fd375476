#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
先进模糊推理系统测试
测试基于2024年学术界最佳实践的模糊推理和决策系统
"""

import sys
import os
import numpy as np
import time
from typing import Dict, List

# 添加策略路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'pyStrategy', 'self_strategy'))

def test_type2_fuzzy_set():
    """测试二型模糊集合"""
    print("=== 测试二型模糊集合 ===")
    
    try:
        from OptionStrategy2 import Type2FuzzySet
        
        # 创建二型模糊集合
        universe = np.linspace(0, 1, 101)
        fuzzy_set = Type2FuzzySet('test_set', universe)
        
        # 设置梯形二型模糊集合
        fuzzy_set.set_trapezoidal_type2(0.2, 0.4, 0.6, 0.8, 0.1)
        
        # 测试隶属度区间
        test_values = [0.1, 0.3, 0.5, 0.7, 0.9]
        for val in test_values:
            lower, upper = fuzzy_set.get_membership_interval(val)
            print(f"值 {val}: 隶属度区间 [{lower:.3f}, {upper:.3f}]")
        
        print("✓ 二型模糊集合测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 二型模糊集合测试失败: {str(e)}")
        return False

def test_anfis_controller():
    """测试ANFIS控制器"""
    print("\n=== 测试ANFIS控制器 ===")
    
    try:
        from OptionStrategy2 import ANFISController
        
        # 创建ANFIS控制器
        anfis = ANFISController(input_dims=3, num_rules=27)
        
        # 测试前向传播
        test_inputs = np.array([0.3, 0.7, 0.5])
        output, strengths, rule_outputs = anfis.forward_pass(test_inputs)
        
        print(f"输入: {test_inputs}")
        print(f"输出: {output:.4f}")
        print(f"规则强度数量: {len(strengths)}")
        print(f"规则输出数量: {len(rule_outputs)}")
        
        # 测试自适应学习
        target = 0.8
        anfis.adapt_parameters(test_inputs, target, output, strengths)
        
        # 再次前向传播，检查是否有改进
        new_output, _, _ = anfis.forward_pass(test_inputs)
        print(f"学习后输出: {new_output:.4f}")
        print(f"学习率: {anfis.learning_rate:.4f}")
        print(f"自适应次数: {anfis.adaptation_count}")
        
        print("✓ ANFIS控制器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ ANFIS控制器测试失败: {str(e)}")
        return False

def test_market_regime_detector():
    """测试市场状态检测器"""
    print("\n=== 测试市场状态检测器 ===")
    
    try:
        from OptionStrategy2 import MarketRegimeDetector
        
        detector = MarketRegimeDetector()
        
        # 模拟不同市场状态的数据
        test_scenarios = [
            # 趋势市场
            {'price': 100 + i, 'volume': 1000, 'volatility': 0.02} 
            for i in range(50)
        ] + [
            # 震荡市场
            {'price': 100 + 5 * np.sin(i * 0.1), 'volume': 1000, 'volatility': 0.01} 
            for i in range(50)
        ] + [
            # 高波动市场
            {'price': 100 + np.random.normal(0, 5), 'volume': 1000, 'volatility': 0.08} 
            for i in range(50)
        ]
        
        regimes = []
        for data in test_scenarios:
            regime = detector.detect_regime(data)
            regimes.append(regime)
        
        # 统计不同状态的出现次数
        regime_counts = {}
        for regime in regimes:
            regime_counts[regime] = regime_counts.get(regime, 0) + 1
        
        print("检测到的市场状态分布:")
        for regime, count in regime_counts.items():
            print(f"  {regime}: {count} 次")
        
        print("✓ 市场状态检测器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 市场状态检测器测试失败: {str(e)}")
        return False

def test_advanced_fuzzy_decision_engine():
    """测试先进模糊决策引擎"""
    print("\n=== 测试先进模糊决策引擎 ===")
    
    try:
        from OptionStrategy2 import AdvancedFuzzyDecisionEngine
        
        engine = AdvancedFuzzyDecisionEngine()
        
        # 测试不同的信号数据
        test_signals = [
            {
                'hull_strength': 0.8,
                'stc_momentum': 0.7,
                'volatility': 0.3,
                'signal_quality': 0.9,
                'market_pressure': 0.4,
                'price': 100,
                'volume': 1000,
                'stability': 0.8,
                'drawdown': 0.02,
                'correlation_risk': 0.2,
                'liquidity_risk': 0.1
            },
            {
                'hull_strength': 0.3,
                'stc_momentum': 0.2,
                'volatility': 0.8,
                'signal_quality': 0.4,
                'market_pressure': 0.9,
                'price': 100,
                'volume': 500,
                'stability': 0.3,
                'drawdown': 0.05,
                'correlation_risk': 0.6,
                'liquidity_risk': 0.4
            }
        ]
        
        for i, signal_data in enumerate(test_signals):
            print(f"\n测试场景 {i+1}:")
            decision = engine.make_advanced_decision(signal_data)
            
            print(f"  应该交易: {decision['should_trade']}")
            print(f"  交易方向: {decision['trade_direction']}")
            print(f"  入场置信度: {decision['entry_confidence']:.3f}")
            print(f"  仓位大小: {decision['position_size']}")
            print(f"  市场状态: {decision['market_regime']}")
            print(f"  不确定性水平: {decision['uncertainty_level']:.3f}")
            print(f"  风险水平: {decision['risk_level']:.3f}")
            print(f"  自适应性评分: {decision['adaptability_score']:.3f}")
        
        print("\n✓ 先进模糊决策引擎测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 先进模糊决策引擎测试失败: {str(e)}")
        return False

def test_decision_engine_integration():
    """测试决策引擎集成"""
    print("\n=== 测试决策引擎集成 ===")
    
    try:
        from OptionStrategy2 import DecisionEngine
        
        engine = DecisionEngine()
        
        # 测试不同决策模式
        modes = ['advanced', 'traditional', 'hybrid']
        
        for mode in modes:
            print(f"\n测试 {mode} 模式:")
            engine.set_decision_mode(mode)
            
            # 模拟信号数据
            processed_signal = {
                'hull_strength': 0.7,
                'stc_momentum': 0.6,
                'volatility': 0.4,
                'signal_quality': 0.8,
                'market_pressure': 0.3
            }
            
            control_output = {
                'stability': 0.7,
                'stability_index': 0.7,
                'drawdown': 0.02,
                'correlation_risk': 0.3,
                'liquidity_risk': 0.2
            }
            
            if mode == 'hybrid':
                decision = engine.make_hybrid_decision(processed_signal, control_output)
            else:
                decision = engine.make_trading_decision(processed_signal, control_output)
            
            print(f"  决策类型: {decision.get('decision_type', 'unknown')}")
            print(f"  应该交易: {decision['should_trade']}")
            print(f"  交易置信度: {decision['entry_confidence']:.3f}")
            
            if mode == 'hybrid':
                print(f"  传统置信度: {decision.get('traditional_confidence', 0):.3f}")
                print(f"  先进置信度: {decision.get('advanced_confidence', 0):.3f}")
                print(f"  方向一致性: {decision.get('direction_consensus', False)}")
        
        # 测试性能摘要
        print(f"\n性能摘要:")
        summary = engine.get_performance_summary()
        for key, value in summary.items():
            print(f"  {key}: {value}")
        
        print("\n✓ 决策引擎集成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 决策引擎集成测试失败: {str(e)}")
        return False

def test_performance_comparison():
    """测试性能对比"""
    print("\n=== 测试性能对比 ===")
    
    try:
        from OptionStrategy2 import DecisionEngine
        
        engine = DecisionEngine()
        
        # 生成测试数据
        num_tests = 100
        results = {'traditional': [], 'advanced': [], 'hybrid': []}
        
        for i in range(num_tests):
            # 随机生成信号数据
            processed_signal = {
                'hull_strength': np.random.random(),
                'stc_momentum': np.random.random(),
                'volatility': np.random.random(),
                'signal_quality': np.random.random(),
                'market_pressure': np.random.random()
            }
            
            control_output = {
                'stability': np.random.random(),
                'stability_index': np.random.random(),
                'drawdown': np.random.random() * 0.1,
                'correlation_risk': np.random.random(),
                'liquidity_risk': np.random.random()
            }
            
            # 测试不同模式的性能
            for mode in ['traditional', 'advanced', 'hybrid']:
                start_time = time.time()
                
                engine.set_decision_mode(mode)
                if mode == 'hybrid':
                    decision = engine.make_hybrid_decision(processed_signal, control_output)
                else:
                    decision = engine.make_trading_decision(processed_signal, control_output)
                
                end_time = time.time()
                processing_time = (end_time - start_time) * 1000  # 毫秒
                
                results[mode].append({
                    'time': processing_time,
                    'confidence': decision['entry_confidence'],
                    'should_trade': decision['should_trade']
                })
        
        # 分析结果
        print("性能对比结果:")
        for mode, data in results.items():
            avg_time = np.mean([d['time'] for d in data])
            avg_confidence = np.mean([d['confidence'] for d in data])
            trade_ratio = np.mean([d['should_trade'] for d in data])
            
            print(f"  {mode}模式:")
            print(f"    平均处理时间: {avg_time:.3f} ms")
            print(f"    平均置信度: {avg_confidence:.3f}")
            print(f"    交易信号比例: {trade_ratio:.3f}")
        
        print("\n✓ 性能对比测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 性能对比测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始测试先进模糊推理系统...")
    print("基于2024年学术界最佳实践")
    print("=" * 50)
    
    test_results = []
    
    # 运行所有测试
    test_functions = [
        test_type2_fuzzy_set,
        test_anfis_controller,
        test_market_regime_detector,
        test_advanced_fuzzy_decision_engine,
        test_decision_engine_integration,
        test_performance_comparison
    ]
    
    for test_func in test_functions:
        try:
            result = test_func()
            test_results.append(result)
        except Exception as e:
            print(f"测试 {test_func.__name__} 发生异常: {str(e)}")
            test_results.append(False)
    
    # 总结测试结果
    print("\n" + "=" * 50)
    print("测试总结:")
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"通过测试: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！先进模糊推理系统运行正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 