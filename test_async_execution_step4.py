#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第四步：异步执行模块测试
验证异步执行队列和任务启动功能
"""

import asyncio
import sys
import os

# 添加策略路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'pyStrategy'))

def test_action_queue_initialization():
    """测试action_queue初始化"""
    print("=== 测试action_queue初始化 ===")
    
    try:
        from pyStrategy.self_strategy.Strategy3 import Strategy3
        
        # 创建策略实例
        strategy = Strategy3()
        
        # 检查action_queue是否已初始化
        if hasattr(strategy, 'action_queue'):
            print("✅ action_queue已正确初始化")
            print(f"   action_queue类型: {type(strategy.action_queue)}")
        else:
            print("❌ action_queue未初始化")
            return False
            
        # 检查其他队列
        queues = ['tick_queue', 'indicator_queue', 'decision_queue', 'action_queue']
        for queue_name in queues:
            if hasattr(strategy, queue_name):
                print(f"✅ {queue_name}已初始化")
            else:
                print(f"❌ {queue_name}未初始化")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_execute_action_async_method():
    """测试execute_action_async方法是否存在"""
    print("\n=== 测试execute_action_async方法 ===")
    
    try:
        from pyStrategy.self_strategy.Strategy3 import Strategy3
        
        strategy = Strategy3()
        
        # 检查方法是否存在
        if hasattr(strategy, 'execute_action_async'):
            print("✅ execute_action_async方法存在")
            
            # 检查是否为异步方法
            import inspect
            if inspect.iscoroutinefunction(strategy.execute_action_async):
                print("✅ execute_action_async是异步方法")
            else:
                print("❌ execute_action_async不是异步方法")
                return False
        else:
            print("❌ execute_action_async方法不存在")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_async_task_startup():
    """测试异步任务启动逻辑"""
    print("\n=== 测试异步任务启动逻辑 ===")
    
    try:
        from pyStrategy.self_strategy.Strategy3 import Strategy3
        
        strategy = Strategy3()
        
        # 检查on_start方法中的异步任务启动
        on_start_source = strategy.on_start.__code__.co_consts
        on_start_source_str = str(on_start_source)
        
        # 检查是否包含execute_action_async
        if 'execute_action_async' in on_start_source_str:
            print("✅ on_start方法中包含execute_action_async任务启动")
        else:
            print("❌ on_start方法中缺少execute_action_async任务启动")
            return False
            
        # 检查是否包含其他异步任务
        required_tasks = ['listen_market', 'calc_indicators_async', 'execute_action_async']
        for task in required_tasks:
            if task in on_start_source_str:
                print(f"✅ 包含{task}任务启动")
            else:
                print(f"❌ 缺少{task}任务启动")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_action_queue_functionality():
    """测试action_queue功能"""
    print("\n=== 测试action_queue功能 ===")
    
    try:
        from pyStrategy.self_strategy.Strategy3 import Strategy3
        
        strategy = Strategy3()
        
        # 测试队列基本功能
        test_action = {
            'decision': ('RiskMedium', 'Normal', 0.7),
            'tick': None,
            'indicator': {'stc_value': 50.0, 'hull_value': 100.0}
        }
        
        # 模拟异步环境
        async def test_queue():
            # 测试放入数据
            await strategy.action_queue.put(test_action)
            print("✅ 成功向action_queue放入数据")
            
            # 测试取出数据
            retrieved_action = await strategy.action_queue.get()
            print("✅ 成功从action_queue取出数据")
            
            # 验证数据完整性
            if retrieved_action == test_action:
                print("✅ 数据完整性验证通过")
                return True
            else:
                print("❌ 数据完整性验证失败")
                return False
        
        # 运行测试
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(test_queue())
        loop.close()
        
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_async_architecture_completeness():
    """测试异步架构完整性"""
    print("\n=== 测试异步架构完整性 ===")
    
    try:
        from pyStrategy.self_strategy.Strategy3 import Strategy3
        
        strategy = Strategy3()
        
        # 检查完整的异步架构组件
        required_components = {
            'tick_queue': '行情数据队列',
            'indicator_queue': '指标计算队列', 
            'decision_queue': '决策输入队列',
            'action_queue': '执行动作队列',
            'listen_market': '异步行情监听',
            'calc_indicators_async': '异步指标计算',
            'execute_action_async': '异步执行模块'
        }
        
        all_present = True
        for component, description in required_components.items():
            if hasattr(strategy, component):
                print(f"✅ {description}({component})存在")
            else:
                print(f"❌ {description}({component})缺失")
                all_present = False
                
        if all_present:
            print("✅ 异步架构组件完整")
        else:
            print("❌ 异步架构组件不完整")
            
        return all_present
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Strategy3 第四步：异步执行模块测试")
    print("=" * 50)
    
    tests = [
        test_action_queue_initialization,
        test_execute_action_async_method,
        test_async_task_startup,
        test_action_queue_functionality,
        test_async_architecture_completeness
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 第四步异步执行模块实现成功！")
        print("\n异步架构总结:")
        print("1. ✅ 行情监听模块 (listen_market)")
        print("2. ✅ 指标计算模块 (calc_indicators_async)") 
        print("3. ✅ 模糊决策模块 (fuzzy_decision_async)")
        print("4. ✅ 执行模块 (execute_action_async)")
        print("\n数据流: tick_queue → indicator_queue → decision_queue → action_queue")
        print("实现了完整的异步解耦架构！")
    else:
        print("⚠️  部分测试失败，需要检查实现")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 