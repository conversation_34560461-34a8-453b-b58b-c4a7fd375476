#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OptionStrategy2 程序化自动交易策略验证脚本
验证移除river依赖后的纯程序化交易功能
"""

import sys
import os
import numpy as np
import time
from datetime import datetime, timedelta
sys.path.append(os.path.join(os.path.dirname(__file__), 'pyStrategy'))

# 导入策略
try:
    from pyStrategy.self_strategy.OptionStrategy2 import OptionStrategy2
except ImportError:
    sys.path.append('pyStrategy/self_strategy')
    from OptionStrategy2 import OptionStrategy2

class MockKLineData:
    """模拟K线数据"""
    def __init__(self, open_price, high, low, close, volume, timestamp):
        self.open = open_price
        self.high = high
        self.low = low
        self.close = close
        self.volume = volume
        self.current_time = timestamp

class MockTickData:
    """模拟Tick数据"""
    def __init__(self, price):
        self.last_price = price
        self.ask_price1 = price + 0.01
        self.ask_price2 = price + 0.02
        self.bid_price1 = price - 0.01
        self.bid_price2 = price - 0.02

class MockPosition:
    """模拟持仓"""
    def __init__(self, net_position=0):
        self.net_position = net_position

class MockTradeData:
    """模拟成交数据"""
    def __init__(self, filled_price, filled_size, direction, filled_time):
        self.filled_price = filled_price
        self.filled_size = filled_size
        self.direction = direction
        self.filled_time = filled_time
        self.realized_pnl = 0

def test_strategy_initialization():
    """测试策略初始化"""
    print("🔧 测试策略初始化")
    print("=" * 60)
    
    try:
        strategy = OptionStrategy2()
        
        # 检查基本属性
        assert hasattr(strategy, 'hull_indicators')
        assert hasattr(strategy, 'stc_indicator')
        assert hasattr(strategy, 'current_params')
        assert hasattr(strategy, 'auto_adjust_enabled')
        
        print("✅ 策略初始化成功")
        print(f"   • HULL指标实例: {len(strategy.hull_indicators)}个")
        print(f"   • 自动调整参数: {strategy.auto_adjust_enabled}")
        print(f"   • 性能阈值: {strategy.performance_threshold}")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略初始化失败: {e}")
        return False

def test_indicators_calculation():
    """测试技术指标计算"""
    print("\n🔧 测试技术指标计算")
    print("=" * 60)
    
    strategy = OptionStrategy2()
    
    # 生成测试数据
    prices = []
    for i in range(100):
        price = 100 + i * 0.1 + np.sin(i * 0.1) * 3 + np.random.normal(0, 0.5)
        prices.append(price)
    
    strategy.price_history = prices
    strategy.current_params = strategy.param_sets["A"]
    strategy.tick = MockTickData(prices[-1])
    
    try:
        # 计算指标
        strategy.calc_indicator()
        
        # 验证指标数据
        hull_data = strategy.main_indicator_data
        stc_data = strategy.sub_indicator_data
        
        # 检查HULL指标
        hull_valid = (
            isinstance(hull_data["HULL_FAST"], (int, float)) and
            isinstance(hull_data["HULL_SLOW"], (int, float)) and
            isinstance(hull_data["HULL_SIGNAL"], (int, float))
        )
        
        # 检查STC指标
        stc_valid = (
            0 <= stc_data["STC"] <= 100 and
            0 <= stc_data["STC_SIGNAL"] <= 100 and
            stc_data["STC_OVERBOUGHT"] == 80.0
        )
        
        print("✅ 技术指标计算成功")
        print(f"   • HULL快线: {hull_data['HULL_FAST']:.4f}")
        print(f"   • HULL慢线: {hull_data['HULL_SLOW']:.4f}")
        print(f"   • STC值: {stc_data['STC']:.2f}")
        print(f"   • STC信号线: {stc_data['STC_SIGNAL']:.2f}")
        
        return hull_valid and stc_valid
        
    except Exception as e:
        print(f"❌ 技术指标计算失败: {e}")
        return False

def test_trading_signals():
    """测试交易信号生成"""
    print("\n🔧 测试交易信号生成")
    print("=" * 60)
    
    strategy = OptionStrategy2()
    
    # 生成趋势数据
    trend_prices = []
    for i in range(120):
        trend = 100 + i * 0.2  # 上升趋势
        noise = np.random.normal(0, 0.3)
        price = trend + noise
        trend_prices.append(price)
    
    # 模拟K线数据处理
    signals = []
    
    for i in range(60, len(trend_prices)):
        strategy.price_history = trend_prices[:i+1]
        strategy.current_params = strategy.param_sets["A"]
        strategy.tick = MockTickData(trend_prices[i])
        
        # 创建模拟K线
        kline = MockKLineData(
            trend_prices[i-1], trend_prices[i]+0.1, 
            trend_prices[i]-0.1, trend_prices[i],
            1000, datetime.now()
        )
        
        # 计算信号
        strategy.calc_signal(kline)
        
        signals.append({
            "buy": strategy.buy_signal,
            "sell": strategy.sell_signal,
            "price": trend_prices[i],
            "hull_trend": strategy.state_map.hull_trend,
            "stc_trend": strategy.state_map.stc_trend
        })
    
    # 分析信号
    buy_signals = sum(1 for s in signals if s["buy"])
    sell_signals = sum(1 for s in signals if s["sell"])
    
    print("✅ 交易信号生成测试完成")
    print(f"   • 总数据点: {len(signals)}")
    print(f"   • 买入信号: {buy_signals}")
    print(f"   • 卖出信号: {sell_signals}")
    print(f"   • 信号频率: {(buy_signals + sell_signals) / len(signals):.2%}")
    
    # 验证信号合理性
    signal_reasonable = 0 < (buy_signals + sell_signals) < len(signals) * 0.3
    
    return signal_reasonable

def test_auto_parameter_adjustment():
    """测试自动参数调整"""
    print("\n🔧 测试自动参数调整")
    print("=" * 60)
    
    strategy = OptionStrategy2()
    strategy.current_params = strategy.param_sets["A"].copy()
    
    # 记录初始参数
    initial_params = strategy.current_params.copy()
    
    # 模拟交易历史（低胜率）
    for i in range(15):
        trade = {
            "profit": -10 if i % 3 == 0 else 5,  # 胜率约33%
            "entry_time": time.time() - i * 3600,
            "mode": "A"
        }
        strategy.trade_history.append(trade)
    
    # 计算性能指标
    strategy.calculate_performance_metrics()
    
    print(f"   胜率: {strategy.state_map.win_rate:.2%}")
    print(f"   盈亏比: {strategy.state_map.profit_factor:.2f}")
    
    # 执行自动调整
    strategy._auto_adjust_parameters()
    
    # 检查参数变化
    params_changed = any(
        abs(strategy.current_params[key] - initial_params[key]) > 0.001
        for key in initial_params
    )
    
    print("✅ 自动参数调整测试完成")
    print(f"   • 参数是否调整: {'是' if params_changed else '否'}")
    print(f"   • 止损倍数: {initial_params['stop_mult']:.3f} → {strategy.current_params['stop_mult']:.3f}")
    print(f"   • 止盈倍数: {initial_params['profit_mult']:.3f} → {strategy.current_params['profit_mult']:.3f}")
    
    return params_changed

def test_risk_management():
    """测试风险管理功能"""
    print("\n🔧 测试风险管理功能")
    print("=" * 60)
    
    strategy = OptionStrategy2()
    strategy.current_params = strategy.param_sets["A"]
    
    # 模拟持仓状态
    strategy.entry_price = 100.0
    strategy.position_size = 10
    strategy.state_map.volatility = 0.5
    strategy.state_map.highest_price = 105.0
    strategy.state_map.lowest_price = 99.0
    
    # 模拟价格变动
    test_prices = [102.0, 104.0, 106.0, 103.0, 101.0]
    
    stop_losses = []
    profits = []
    
    for price in test_prices:
        strategy.update_dynamic_stops(price)
        stop_losses.append(strategy.state_map.stop_loss)
        profits.append(strategy.state_map.current_profit)
    
    print("✅ 风险管理测试完成")
    print(f"   • 入场价格: {strategy.entry_price}")
    print(f"   • 最终止损: {stop_losses[-1]:.2f}")
    print(f"   • 最大浮盈: {max(profits):.2f}")
    print(f"   • 止损调整次数: {len(set(stop_losses))}")
    
    # 验证止损合理性
    stop_reasonable = all(sl < strategy.entry_price + 2 for sl in stop_losses)
    
    return stop_reasonable

def test_performance_calculation():
    """测试性能指标计算"""
    print("\n🔧 测试性能指标计算")
    print("=" * 60)
    
    strategy = OptionStrategy2()
    
    # 模拟交易记录
    np.random.seed(42)
    for i in range(30):
        profit = np.random.normal(5, 15)  # 随机盈亏
        trade = {
            "profit": profit,
            "entry_time": time.time() - i * 3600,
            "mode": "A" if i % 2 == 0 else "B"
        }
        strategy.trade_history.append(trade)
    
    # 计算性能指标
    strategy.calculate_performance_metrics()
    
    print("✅ 性能指标计算完成")
    print(f"   • 胜率: {strategy.state_map.win_rate:.2%}")
    print(f"   • 盈亏比: {strategy.state_map.profit_factor:.2f}")
    print(f"   • 夏普比率: {strategy.state_map.sharpe_ratio:.4f}")
    print(f"   • 最大回撤: {strategy.state_map.max_drawdown:.2%}")
    
    # 验证指标合理性
    metrics_valid = (
        0 <= strategy.state_map.win_rate <= 1 and
        strategy.state_map.profit_factor >= 0 and
        strategy.state_map.max_drawdown >= 0
    )
    
    return metrics_valid

if __name__ == "__main__":
    print("🚀 OptionStrategy2 程序化自动交易策略验证")
    print("=" * 70)
    print("验证移除river依赖后的纯程序化交易功能")
    print()
    
    # 执行测试
    tests = [
        ("策略初始化", test_strategy_initialization),
        ("技术指标计算", test_indicators_calculation),
        ("交易信号生成", test_trading_signals),
        ("自动参数调整", test_auto_parameter_adjustment),
        ("风险管理", test_risk_management),
        ("性能指标计算", test_performance_calculation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            results.append((test_name, False))
    
    # 总结测试结果
    print("\n" + "=" * 70)
    print("📋 程序化自动交易策略验证总结:")
    
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   • {test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    
    if all_passed:
        print("\n🎉 程序化自动交易策略验证完全成功！")
        print("\n✅ 核心功能确认:")
        print("   • 移除river依赖库完成")
        print("   • HULL+STC技术指标正常工作")
        print("   • 交易信号生成逻辑正确")
        print("   • 自动参数调整功能可用")
        print("   • 风险管理机制完善")
        print("   • 性能统计功能正常")
        print("\n🚀 策略已准备好进行程序化自动交易！")
        
        print("\n📋 策略特点:")
        print("   • 纯技术分析驱动")
        print("   • 自适应参数调整")
        print("   • 动态风险管理")
        print("   • 实时性能监控")
        print("   • 无外部机器学习依赖")
    else:
        failed_tests = [name for name, result in results if not result]
        print(f"\n⚠️ 以下测试未通过: {', '.join(failed_tests)}")
        print("请检查代码并修复相关问题")
    
    print("=" * 70) 