#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# Add the pyStrategy directory to the Python path
sys.path.append('pyStrategy')

try:
    from self_strategy.Strategy2 import Strategy2
    print("✓ Strategy2 imported successfully")
    
    # Try to create an instance
    strategy = Strategy2()
    print("✓ Strategy2 instance created successfully")
    
    # Test basic properties
    print(f"✓ Strategy class name: {strategy.class_name}")
    print(f"✓ Params map type: {type(strategy.params_map)}")
    print(f"✓ State map type: {type(strategy.state_map)}")
    
    # Test signal generation with different market conditions
    print("\n🔍 Testing bidirectional trading capabilities:")
    
    # Test 1: Market regime filters
    strategy.state_map.market_regime = "TRENDING"
    strategy.state_map.regime_strength = 0.8
    regime_filter = strategy._get_regime_filter()
    print(f"✓ Market regime filter: {regime_filter}")
    
    # Test 2: Adaptive stops for different positions
    strategy.position_size = 1  # Long position
    strategy.calc_adaptive_stops(100.0)
    long_stops = {
        "stop_loss": strategy.state_map.adaptive_stop_loss,
        "take_profit": strategy.state_map.adaptive_take_profit
    }
    print(f"✓ Long position stops: {long_stops}")
    
    strategy.position_size = -1  # Short position
    strategy.calc_adaptive_stops(100.0)
    short_stops = {
        "stop_loss": strategy.state_map.adaptive_stop_loss,
        "take_profit": strategy.state_map.adaptive_take_profit
    }
    print(f"✓ Short position stops: {short_stops}")
    
    # Test 3: Position multiplier
    long_mult = strategy._get_position_multiplier()
    print(f"✓ Long position multiplier: {long_mult}")
    
    strategy.position_size = -1
    short_mult = strategy._get_position_multiplier()
    print(f"✓ Short position multiplier: {short_mult}")
    
    # Test 4: Signal sensitivity parameters
    print(f"✓ Long signal sensitivity: {strategy.params_map.long_signal_sensitivity}")
    print(f"✓ Short signal sensitivity: {strategy.params_map.short_signal_sensitivity}")
    
    print("\n🎉 Bidirectional trading optimization completed successfully!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc() 