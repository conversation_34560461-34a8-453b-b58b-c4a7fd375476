#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强依赖库功能测试
测试SciPy、Control、FilterPy、Numba等专业库的集成效果
"""

import sys
import os
import numpy as np
import time
from datetime import datetime

# 添加策略路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'pyStrategy'))

from pyStrategy.self_strategy.OptionStrategy2 import (
    AdvancedControlTheoryCore, 
    ControlSignal, 
    UnifiedIndicatorEngine,
    OptionStrategy2,
    SCIPY_AVAILABLE,
    CONTROL_AVAILABLE,
    FILTERPY_AVAILABLE,
    NUMBA_AVAILABLE
)
from pythongo.classdef import KLineData


def test_dependency_availability():
    """测试依赖库可用性"""
    print("🔍 检查依赖库可用性...")
    
    dependencies = {
        'SciPy': SCIPY_AVAILABLE,
        'Control': CONTROL_AVAILABLE,
        'FilterPy': FILTERPY_AVAILABLE,
        'Numba': NUMBA_AVAILABLE
    }
    
    for name, available in dependencies.items():
        status = "✅ 可用" if available else "❌ 不可用"
        print(f"  {name}: {status}")
    
    available_count = sum(dependencies.values())
    total_count = len(dependencies)
    
    print(f"\n📊 依赖库可用性: {available_count}/{total_count} ({available_count/total_count*100:.1f}%)")
    
    return available_count > 0


def test_advanced_kalman_filter():
    """测试高级卡尔曼滤波器"""
    print("\n🎯 测试高级卡尔曼滤波器...")
    
    if not FILTERPY_AVAILABLE:
        print("  ⚠️  FilterPy不可用，跳过高级卡尔曼滤波测试")
        return False
    
    try:
        # 创建控制理论核心
        core = AdvancedControlTheoryCore()
        
        # 检查高级卡尔曼滤波器是否初始化成功
        if hasattr(core, 'advanced_kalman_available') and core.advanced_kalman_available:
            print("  ✅ 扩展卡尔曼滤波器初始化成功")
            
            # 测试状态更新
            test_signal = ControlSignal(
                timestamp=time.time(),
                hull_fast=3010.5,
                hull_slow=3005.2,
                hull_signal=3008.1,
                stc_value=65.3,
                stc_signal=62.8,
                price=3012.0,
                volatility=0.025,
                signal_type='entry'
            )
            
            # 执行状态更新
            initial_state = core.state_vector.copy()
            core._update_state_vector(test_signal)
            updated_state = core.state_vector.copy()
            
            # 检查状态是否更新
            state_changed = not np.allclose(initial_state, updated_state)
            print(f"  ✅ 状态向量更新: {'成功' if state_changed else '无变化'}")
            print(f"  📊 状态向量范数: {np.linalg.norm(updated_state):.4f}")
            
            return True
        else:
            print("  ❌ 扩展卡尔曼滤波器初始化失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 高级卡尔曼滤波测试失败: {e}")
        return False


def test_control_system_models():
    """测试控制系统模型"""
    print("\n🎛️  测试控制系统模型...")
    
    if not CONTROL_AVAILABLE:
        print("  ⚠️  Control库不可用，跳过控制系统模型测试")
        return False
    
    try:
        # 创建控制理论核心
        core = AdvancedControlTheoryCore()
        
        # 检查控制系统模型是否初始化成功
        if hasattr(core, 'control_models_available') and core.control_models_available:
            print("  ✅ 控制系统模型初始化成功")
            
            # 测试传递函数
            if hasattr(core, 'transfer_function'):
                print("  ✅ 传递函数模型创建成功")
            
            # 测试状态空间模型
            if hasattr(core, 'state_space_model'):
                print("  ✅ 状态空间模型创建成功")
            
            # 测试LQR增益
            if hasattr(core, 'lqr_gain'):
                print(f"  ✅ LQR增益矩阵: {core.lqr_gain.shape}")
                print(f"  📊 LQR增益值: {core.lqr_gain.flatten()}")
            
            # 测试频率响应计算
            test_signal = ControlSignal(
                timestamp=time.time(),
                hull_fast=3010.5,
                hull_slow=3005.2,
                hull_signal=3008.1,
                stc_value=65.3,
                stc_signal=62.8,
                price=3012.0,
                volatility=0.025,
                signal_type='entry'
            )
            
            freq_response = core._calculate_frequency_response_spectrum()
            if freq_response and 'gain_margin' in freq_response:
                print(f"  ✅ 频率响应分析成功")
                print(f"  📊 增益裕度: {freq_response.get('gain_margin', 'N/A'):.2f} dB")
                print(f"  📊 相位裕度: {freq_response.get('phase_margin', 'N/A'):.2f}°")
            
            return True
        else:
            print("  ❌ 控制系统模型初始化失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 控制系统模型测试失败: {e}")
        return False


def test_signal_processing():
    """测试信号处理功能"""
    print("\n📡 测试信号处理功能...")
    
    if not SCIPY_AVAILABLE:
        print("  ⚠️  SciPy不可用，跳过信号处理测试")
        return False
    
    try:
        # 创建指标引擎
        engine = UnifiedIndicatorEngine()
        
        # 检查信号处理功能是否可用
        if hasattr(engine, 'signal_filtering_available') and engine.signal_filtering_available:
            print("  ✅ 信号滤波器初始化成功")
            
            # 生成测试数据
            test_data = np.random.randn(100) + np.sin(np.linspace(0, 4*np.pi, 100))
            
            # 测试巴特沃斯滤波
            filtered_data = engine._apply_signal_filter(test_data, 'butterworth')
            if len(filtered_data) == len(test_data):
                print("  ✅ 巴特沃斯滤波测试成功")
                noise_reduction = np.std(test_data) - np.std(filtered_data)
                print(f"  📊 噪声降低: {noise_reduction:.4f}")
            
            # 测试Savitzky-Golay滤波
            filtered_data_sg = engine._apply_signal_filter(test_data, 'savgol')
            if len(filtered_data_sg) == len(test_data):
                print("  ✅ Savitzky-Golay滤波测试成功")
            
            # 测试希尔伯特变换
            envelope = engine._calculate_envelope(test_data)
            if len(envelope) == len(test_data):
                print("  ✅ 希尔伯特变换包络检测成功")
            
            # 测试功率谱密度
            frequencies, psd = engine._calculate_power_spectrum(test_data)
            if frequencies is not None and psd is not None:
                print("  ✅ 功率谱密度计算成功")
                print(f"  📊 频率点数: {len(frequencies)}")
                print(f"  📊 主要频率: {frequencies[np.argmax(psd)]:.4f}")
            
            return True
        else:
            print("  ❌ 信号滤波器初始化失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 信号处理测试失败: {e}")
        return False


def test_numba_acceleration():
    """测试Numba加速功能"""
    print("\n⚡ 测试Numba加速功能...")
    
    if not NUMBA_AVAILABLE:
        print("  ⚠️  Numba不可用，跳过加速测试")
        return False
    
    try:
        # 创建指标引擎
        engine = UnifiedIndicatorEngine()
        
        # 生成测试数据
        test_values = np.random.randn(1000)
        test_weights = np.arange(1, 101, dtype=float)
        
        # 测试Numba加速的WMA计算
        if hasattr(engine, '_numba_wma'):
            start_time = time.time()
            for _ in range(100):
                result = engine._numba_wma(test_values[:100], test_weights)
            numba_time = time.time() - start_time
            
            print(f"  ✅ Numba加速WMA计算成功")
            print(f"  📊 100次计算耗时: {numba_time*1000:.2f}ms")
            print(f"  📊 计算结果: {result:.6f}")
            
            return True
        else:
            print("  ❌ Numba加速函数不可用")
            return False
            
    except Exception as e:
        print(f"  ❌ Numba加速测试失败: {e}")
        return False


def test_enhanced_indicators():
    """测试增强的技术指标计算"""
    print("\n📈 测试增强的技术指标计算...")
    
    try:
        # 创建指标引擎
        engine = UnifiedIndicatorEngine()
        
        # 生成模拟K线数据
        for i in range(100):
            kline = KLineData()
            kline.datetime = datetime.now()
            base_price = 3000 + i * 0.1 + np.sin(i * 0.1) * 5
            kline.open = base_price
            kline.high = base_price + np.random.uniform(0, 2)
            kline.low = base_price - np.random.uniform(0, 2)
            kline.close = base_price + np.random.uniform(-1, 1)
            kline.volume = 1000 + np.random.randint(0, 500)
            
            engine.update_data(kline)
        
        # 计算增强的技术指标
        indicators = engine.calculate_all_indicators()
        
        print("  ✅ 增强技术指标计算成功")
        print(f"  📊 HULL快线: {indicators.get('hull_fast', 0):.2f}")
        print(f"  📊 HULL慢线: {indicators.get('hull_slow', 0):.2f}")
        print(f"  📊 STC值: {indicators.get('stc', 0):.2f}")
        print(f"  📊 ATR: {indicators.get('atr', 0):.6f}")
        
        # 检查新增的信号质量指标
        if 'signal_noise_ratio' in indicators:
            print(f"  📊 信噪比: {indicators['signal_noise_ratio']:.4f}")
        if 'trend_strength' in indicators:
            print(f"  📊 趋势强度: {indicators['trend_strength']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 增强技术指标测试失败: {e}")
        return False


def test_performance_comparison():
    """测试性能对比"""
    print("\n🏃 测试性能对比...")
    
    try:
        # 创建两个控制理论核心实例
        core_basic = AdvancedControlTheoryCore()
        core_enhanced = AdvancedControlTheoryCore()
        
        # 生成测试信号
        test_signals = []
        for i in range(100):
            signal = ControlSignal(
                timestamp=time.time(),
                hull_fast=3000 + i * 0.1,
                hull_slow=3000 + i * 0.05,
                hull_signal=3000 + i * 0.02,
                stc_value=50 + i % 50,
                stc_signal=48 + i % 45,
                price=3000 + i * 0.1,
                volatility=0.01 + (i % 10) * 0.001,
                signal_type='entry'
            )
            test_signals.append(signal)
        
        # 测试基础实现性能
        start_time = time.time()
        for signal in test_signals:
            core_basic._process_control_signal(signal)
        basic_time = time.time() - start_time
        
        # 测试增强实现性能
        start_time = time.time()
        for signal in test_signals:
            core_enhanced._process_control_signal(signal)
        enhanced_time = time.time() - start_time
        
        print(f"  📊 基础实现耗时: {basic_time*1000:.2f}ms")
        print(f"  📊 增强实现耗时: {enhanced_time*1000:.2f}ms")
        
        if enhanced_time > 0:
            speedup = basic_time / enhanced_time
            print(f"  📊 性能提升: {speedup:.2f}x")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 性能对比测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始增强依赖库功能测试")
    print("=" * 80)
    
    tests = [
        ("依赖库可用性", test_dependency_availability),
        ("高级卡尔曼滤波器", test_advanced_kalman_filter),
        ("控制系统模型", test_control_system_models),
        ("信号处理功能", test_signal_processing),
        ("Numba加速功能", test_numba_acceleration),
        ("增强技术指标", test_enhanced_indicators),
        ("性能对比", test_performance_comparison),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed_tests += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 测试总结
    print("\n" + "=" * 80)
    print("📋 增强依赖库功能测试总结")
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests >= total_tests * 0.7:  # 70%通过率即可
        print("\n🎉 增强依赖库功能测试基本通过！")
        print("\n💡 建议安装的依赖库:")
        print("1. 📦 SciPy: pip install scipy")
        print("   - 提供高级信号处理、线性代数、优化算法")
        print("   - 增强频率响应分析、滤波器设计、数值求解")
        
        print("2. 📦 Control: pip install control")
        print("   - 专业控制理论库，提供传递函数、状态空间、LQR等")
        print("   - 精确的Bode图、Nyquist图、根轨迹分析")
        
        print("3. 📦 FilterPy: pip install filterpy")
        print("   - 高级卡尔曼滤波器实现")
        print("   - 扩展卡尔曼滤波、无迹卡尔曼滤波")
        
        print("4. 📦 Numba: pip install numba")
        print("   - JIT编译加速数值计算")
        print("   - 显著提升循环密集型计算性能")
        
        print("\n🔧 安装命令:")
        print("pip install scipy control filterpy numba")
        
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 个测试失败")
        print("请检查依赖库安装情况")


if __name__ == "__main__":
    main() 