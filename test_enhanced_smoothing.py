#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强HULL平滑效果验证脚本
验证新的HULL平滑算法是否产生明显的平滑效果
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
sys.path.append(os.path.join(os.path.dirname(__file__), 'pyStrategy'))

# 导入策略
try:
    from pyStrategy.self_strategy.OptionStrategy2 import OptionStrategy2, HullIndicator, STCIndicator
except ImportError:
    sys.path.append('pyStrategy/self_strategy')
    from OptionStrategy2 import OptionStrategy2, HullIndicator, STCIndicator

def generate_noisy_trend_data(length=200, noise_level=2.0):
    """生成高噪声的趋势数据"""
    np.random.seed(42)
    prices = []
    
    for i in range(length):
        # 基础趋势
        base_trend = 100 + i * 0.1
        
        # 周期性波动
        cycle1 = np.sin(i * 0.1) * 3
        cycle2 = np.sin(i * 0.05) * 1.5
        
        # 高频噪声
        noise = np.random.normal(0, noise_level)
        
        # 突然跳跃（模拟市场异常）
        if i % 50 == 0 and i > 0:
            jump = np.random.choice([-5, 5])
        else:
            jump = 0
            
        price = base_trend + cycle1 + cycle2 + noise + jump
        prices.append(price)
    
    return prices

def test_enhanced_hull_smoothing():
    """测试增强的HULL平滑效果"""
    print("🔧 增强HULL平滑效果验证")
    print("=" * 70)
    
    # 生成高噪声测试数据
    test_data = generate_noisy_trend_data(150, noise_level=3.0)
    
    print(f"📊 测试数据: {len(test_data)}个价格点，噪声水平=3.0")
    
    # 测试不同平滑配置
    smoothing_configs = [
        ("无平滑", 1, "EMA"),
        ("EMA轻度", 3, "EMA"),
        ("EMA中度", 5, "EMA"),
        ("EMA重度", 8, "EMA"),
        ("SMA轻度", 3, "SMA"),
        ("SMA中度", 5, "SMA"),
        ("WMA轻度", 3, "WMA"),
        ("WMA中度", 5, "WMA")
    ]
    
    results = {}
    
    for name, smooth_period, smooth_type in smoothing_configs:
        print(f"\n🔍 测试 {name} ({smooth_type}, 周期={smooth_period})")
        
        # 创建独立的HULL指标实例
        hull = HullIndicator(f"test_{name}")
        values = []
        
        # 逐步计算HULL值
        for i in range(21, len(test_data)):
            price_slice = test_data[:i+1]
            hull_value = hull.calculate(price_slice, 21, smooth_period, smooth_type)
            values.append(hull_value)
        
        if len(values) > 10:
            # 计算平滑度指标
            changes = np.diff(values)
            volatility = np.std(changes)
            max_change = np.max(np.abs(changes))
            mean_change = np.mean(np.abs(changes))
            
            # 计算与价格的跟踪性
            corresponding_prices = test_data[21:21+len(values)]
            price_changes = np.diff(corresponding_prices)
            correlation = np.corrcoef(changes[:-1], price_changes[:-1])[0,1] if len(changes) > 1 else 0
            
            results[name] = {
                "volatility": volatility,
                "max_change": max_change,
                "mean_change": mean_change,
                "correlation": correlation,
                "last_values": values[-5:]
            }
            
            print(f"  • 波动率: {volatility:.6f}")
            print(f"  • 最大变化: {max_change:.6f}")
            print(f"  • 平均变化: {mean_change:.6f}")
            print(f"  • 价格相关性: {correlation:.4f}")
            print(f"  • 最后5值: {[f'{v:.4f}' for v in values[-5:]]}")
    
    # 分析平滑效果
    print(f"\n📈 平滑效果对比分析:")
    baseline = results["无平滑"]
    
    improvements = {}
    for name, data in results.items():
        if name != "无平滑":
            vol_improvement = (baseline["volatility"] - data["volatility"]) / baseline["volatility"] * 100
            max_improvement = (baseline["max_change"] - data["max_change"]) / baseline["max_change"] * 100
            
            improvements[name] = {
                "vol_improvement": vol_improvement,
                "max_improvement": max_improvement,
                "correlation": data["correlation"]
            }
            
            print(f"  • {name:12}: 波动率改进 {vol_improvement:+.2f}% | 最大变化改进 {max_improvement:+.2f}% | 相关性 {data['correlation']:.4f}")
    
    # 找出最佳平滑配置
    best_overall = None
    best_score = -999
    
    for name, imp in improvements.items():
        # 综合评分：波动率改进 + 最大变化改进 - 相关性损失
        score = imp["vol_improvement"] + imp["max_improvement"] + imp["correlation"] * 50
        if score > best_score:
            best_score = score
            best_overall = name
            
    print(f"\n🏆 最佳平滑配置: {best_overall} (综合评分: {best_score:.2f})")
    
    # 验证平滑是否有效
    best_vol_improvement = improvements[best_overall]["vol_improvement"]
    smoothing_effective = best_vol_improvement > 10  # 至少10%改进
    
    print(f"✅ 增强平滑效果: {'有效' if smoothing_effective else '需进一步优化'}")
    print(f"   最佳配置波动率改进: {best_vol_improvement:.2f}%")
    
    return smoothing_effective, best_overall

def test_real_market_scenario():
    """测试真实市场场景下的平滑效果"""
    print("\n🏃‍♂️ 真实市场场景平滑效果测试")
    print("=" * 70)
    
    # 模拟真实市场数据（包含趋势、震荡、突发事件）
    market_scenarios = {
        "趋势市场": generate_trend_market_data(),
        "震荡市场": generate_oscillating_market_data(),
        "高波动市场": generate_volatile_market_data()
    }
    
    scenario_results = {}
    
    for scenario_name, data in market_scenarios.items():
        print(f"\n📊 测试场景: {scenario_name}")
        
        # 测试最优配置
        hull_raw = HullIndicator(f"raw_{scenario_name}")
        hull_smoothed = HullIndicator(f"smooth_{scenario_name}")
        
        raw_values = []
        smoothed_values = []
        
        for i in range(21, len(data)):
            price_slice = data[:i+1]
            
            raw_val = hull_raw.calculate(price_slice, 21, 1, "EMA")
            smooth_val = hull_smoothed.calculate(price_slice, 21, 5, "EMA")  # 使用中度EMA平滑
            
            raw_values.append(raw_val)
            smoothed_values.append(smooth_val)
        
        # 计算效果
        raw_volatility = np.std(np.diff(raw_values))
        smooth_volatility = np.std(np.diff(smoothed_values))
        improvement = (raw_volatility - smooth_volatility) / raw_volatility * 100
        
        scenario_results[scenario_name] = improvement
        
        print(f"  • 原始波动率: {raw_volatility:.6f}")
        print(f"  • 平滑波动率: {smooth_volatility:.6f}")
        print(f"  • 改进程度: {improvement:+.2f}%")
    
    # 总体评估
    avg_improvement = np.mean(list(scenario_results.values()))
    all_positive = all(imp > 0 for imp in scenario_results.values())
    
    print(f"\n📈 真实市场场景总评:")
    print(f"  • 平均改进: {avg_improvement:.2f}%")
    print(f"  • 全场景有效: {'是' if all_positive else '否'}")
    
    market_effective = avg_improvement > 5 and all_positive
    print(f"✅ 真实市场效果: {'有效' if market_effective else '需调整'}")
    
    return market_effective

def generate_trend_market_data():
    """生成趋势市场数据"""
    data = []
    for i in range(100):
        trend = 100 + i * 0.3
        noise = np.random.normal(0, 1.0)
        data.append(trend + noise)
    return data

def generate_oscillating_market_data():
    """生成震荡市场数据"""
    data = []
    for i in range(100):
        base = 100
        oscillation = np.sin(i * 0.1) * 8
        noise = np.random.normal(0, 1.5)
        data.append(base + oscillation + noise)
    return data

def generate_volatile_market_data():
    """生成高波动市场数据"""
    data = []
    for i in range(100):
        base = 100
        volatility = np.random.normal(0, 3.0)  # 高波动
        jump = 5 if i % 20 == 0 else 0  # 突发跳跃
        data.append(base + volatility + jump)
    return data

if __name__ == "__main__":
    print("🔧 HULL增强平滑效果验证")
    print("=" * 70)
    print("验证增强后的HULL平滑算法是否能产生明显效果")
    print()
    
    # 执行测试
    test1_result, best_config = test_enhanced_hull_smoothing()
    test2_result = test_real_market_scenario()
    
    # 总结
    print("\n" + "=" * 70)
    print("📋 增强平滑效果验证总结:")
    print(f"  • 基础平滑效果: {'✅ 有效' if test1_result else '❌ 需优化'}")
    print(f"  • 真实市场效果: {'✅ 有效' if test2_result else '❌ 需调整'}")
    
    if best_config:
        print(f"  • 推荐配置: {best_config}")
    
    overall_success = test1_result and test2_result
    
    if overall_success:
        print("\n🎉 增强平滑效果验证成功！")
        print("✅ 修复要点:")
        print("   • 平滑因子优化：EMA使用更保守的alpha值")
        print("   • 历史窗口扩大：提升平滑连续性")
        print("   • 二次平滑：对高周期配置应用额外平滑")
        print("   • 权重算法改进：WMA使用指数权重")
        print("\n🚀 HULL平滑化现已在回测环境中有效工作！")
    else:
        print("\n⚠️ 平滑效果仍需进一步优化")
        if not test1_result:
            print("   - 基础平滑算法需要调整")
        if not test2_result:
            print("   - 真实市场适应性需要改进")
    
    print("=" * 70) 