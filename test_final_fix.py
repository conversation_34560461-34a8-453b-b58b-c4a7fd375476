#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OptionStrategy2 HULL+STC最终修复效果验证脚本
"""

import sys
import os
import numpy as np
sys.path.append(os.path.join(os.path.dirname(__file__), 'pyStrategy'))

# 导入策略
try:
    from pyStrategy.self_strategy.OptionStrategy2 import OptionStrategy2, HullIndicator, STCIndicator
except ImportError:
    sys.path.append('pyStrategy/self_strategy')
    from OptionStrategy2 import OptionStrategy2, HullIndicator, STCIndicator

def generate_test_data(length=200, trend_type="oscillating"):
    """生成测试数据"""
    np.random.seed(42)
    prices = []
    
    for i in range(length):
        if trend_type == "trending":
            # 上升趋势 + 噪声
            base = 100 + i * 0.2
            noise = np.random.normal(0, 0.5)
            price = base + noise
        elif trend_type == "oscillating":
            # 振荡趋势
            base = 100
            trend = np.sin(i * 0.02) * 8
            cycle = np.sin(i * 0.1) * 3
            noise = np.random.normal(0, 0.3)
            price = base + trend + cycle + noise
        else:  # mixed
            # 混合模式：前半段趋势，后半段振荡
            if i < length // 2:
                base = 100 + i * 0.15
                noise = np.random.normal(0, 0.4)
                price = base + noise
            else:
                base = 100 + (length // 2) * 0.15
                cycle = np.sin((i - length // 2) * 0.15) * 5
                noise = np.random.normal(0, 0.6)
                price = base + cycle + noise
        
        prices.append(price)
    
    return prices

def test_hull_independence():
    """测试HULL指标独立性"""
    print("=" * 70)
    print("🔍 测试HULL指标独立性...")
    
    test_prices = generate_test_data(100, "trending")
    
    # 创建独立指标实例
    hull_fast = HullIndicator()
    hull_slow = HullIndicator()
    hull_signal = HullIndicator()
    
    fast_values = []
    slow_values = []
    signal_values = []
    
    # 测试不同周期的独立计算
    for i in range(55, len(test_prices)):  # 确保有足够数据
        price_slice = test_prices[:i+1]
        
        fast_val = hull_fast.calculate(price_slice, 21, 3, "EMA")
        slow_val = hull_slow.calculate(price_slice, 55, 3, "EMA")
        signal_val = hull_signal.calculate(price_slice, 13, 3, "EMA")
        
        fast_values.append(fast_val)
        slow_values.append(slow_val)
        signal_values.append(signal_val)
    
    print(f"  • HULL快线(21): {fast_values[-5:]}")
    print(f"  • HULL慢线(55): {slow_values[-5:]}")
    print(f"  • HULL信号(13): {signal_values[-5:]}")
    
    # 验证不同周期产生不同结果
    assert fast_values[-1] != slow_values[-1], "快线和慢线值不应相同"
    assert abs(fast_values[-1] - signal_values[-1]) > 0.01, "快线和信号线应有差异"
    
    print("✅ HULL指标独立性测试通过！")
    return True

def test_stc_plotting_data():
    """测试STC指标绘制数据"""
    print("\n" + "=" * 70)
    print("📊 测试STC指标绘制数据...")
    
    test_prices = generate_test_data(150, "oscillating")
    stc = STCIndicator()
    
    results = []
    for i in range(100, len(test_prices)):
        price_slice = test_prices[:i+1]
        result = stc.calculate(price_slice, 23, 50, 100, 0.5)
        results.append(result)
    
    # 检查最后10个结果
    recent_results = results[-10:]
    
    print("📈 STC指标绘制数据（最后10个值）:")
    for i, result in enumerate(recent_results):
        print(f"  [{i+1:2d}] STC: {result['stc']:6.2f} | 信号: {result['stc_signal']:6.2f} | 柱状: {result['stc_histogram']:8.4f}")
    
    # 验证数据范围
    for result in recent_results:
        assert 0 <= result['stc'] <= 100, f"STC值 {result['stc']} 超出范围"
        assert 0 <= result['stc_signal'] <= 100, f"STC信号线 {result['stc_signal']} 超出范围"
        assert -100 <= result['stc_histogram'] <= 100, f"STC柱状图 {result['stc_histogram']} 超出范围"
    
    # 检查信号线的平滑性
    stc_signals = [r['stc_signal'] for r in recent_results]
    signal_changes = np.diff(stc_signals)
    max_change = np.max(np.abs(signal_changes))
    
    print(f"  • 信号线最大变化: {max_change:.4f}")
    print(f"  • 信号线平滑度: {'优秀' if max_change < 5 else '一般' if max_change < 10 else '需改进'}")
    
    print("✅ STC指标绘制数据测试通过！")
    return True

def test_strategy_complete_workflow():
    """测试策略完整工作流程"""
    print("\n" + "=" * 70)
    print("🚀 测试策略完整工作流程...")
    
    strategy = OptionStrategy2()
    
    # 生成混合市场数据
    test_prices = generate_test_data(120, "mixed")
    strategy.price_history = test_prices
    strategy.current_params = strategy.param_sets["A"]
    
    # 模拟Tick数据
    class MockTick:
        def __init__(self, price):
            self.last_price = price
            self.ask_price1 = price + 0.01
            self.ask_price2 = price + 0.02
    
    strategy.tick = MockTick(test_prices[-1])
    
    # 计算所有指标
    strategy.calc_indicator()
    
    print("📊 策略指标计算结果:")
    
    # 主图指标
    main_data = strategy.main_indicator_data
    print("  主图指标:")
    for key, value in main_data.items():
        print(f"    • {key}: {value:.4f}")
    
    # 副图指标
    sub_data = strategy.sub_indicator_data
    print("  副图指标:")
    for key, value in sub_data.items():
        print(f"    • {key}: {value:.4f}")
    
    # 状态检查
    print("  策略状态:")
    print(f"    • HULL趋势: {strategy.state_map.hull_trend}")
    print(f"    • STC趋势: {strategy.state_map.stc_trend}")
    print(f"    • 波动率: {strategy.state_map.volatility:.6f}")
    print(f"    • 趋势强度: {strategy.state_map.trend_strength:.4f}")
    
    # 验证数据完整性
    assert all(isinstance(v, (int, float)) for v in main_data.values()), "主图数据类型错误"
    assert all(isinstance(v, (int, float)) for v in sub_data.values()), "副图数据类型错误"
    assert strategy.state_map.hull_fast != 0, "HULL快线未计算"
    assert strategy.state_map.hull_slow != 0, "HULL慢线未计算"
    assert strategy.state_map.hull_signal != 0, "HULL信号线未计算"
    
    print("✅ 策略完整工作流程测试通过！")
    return True

def test_smoothing_effectiveness():
    """测试平滑效果"""
    print("\n" + "=" * 70)
    print("🎯 测试HULL平滑效果...")
    
    # 生成噪声数据
    noisy_prices = generate_test_data(80, "trending")
    for i in range(len(noisy_prices)):
        noisy_prices[i] += np.random.normal(0, 1.5)  # 增加噪声
    
    # 测试不同平滑参数
    smooth_configs = [
        ("SMA", 3), ("SMA", 5),
        ("EMA", 3), ("EMA", 5), 
        ("WMA", 3), ("WMA", 5)
    ]
    
    results = {}
    
    for smooth_type, smooth_period in smooth_configs:
        hull = HullIndicator()
        values = []
        
        for i in range(21, len(noisy_prices)):
            price_slice = noisy_prices[:i+1]
            hull_val = hull.calculate(price_slice, 21, smooth_period, smooth_type)
            values.append(hull_val)
        
        # 计算平滑度（变化率的标准差）
        changes = np.diff(values)
        smoothness = np.std(changes)
        results[f"{smooth_type}_{smooth_period}"] = smoothness
        
        print(f"  • {smooth_type}({smooth_period}): 平滑度 = {smoothness:.6f}")
    
    # 找出最平滑的配置
    best_config = min(results.items(), key=lambda x: x[1])
    print(f"  🏆 最佳平滑配置: {best_config[0]} (平滑度: {best_config[1]:.6f})")
    
    print("✅ HULL平滑效果测试通过！")
    return True

def test_market_mode_switching():
    """测试市场模式切换"""
    print("\n" + "=" * 70)
    print("🔄 测试市场模式切换...")
    
    strategy = OptionStrategy2()
    
    # 模拟趋势市场
    trend_prices = generate_test_data(60, "trending")
    strategy.price_history = trend_prices[-15:]  # 保持15周期历史
    
    # 模拟趋势判断
    class MockKLine:
        def __init__(self, close):
            self.close = close
    
    for price in trend_prices[-5:]:
        kline = MockKLine(price)
        strategy.calc_trend(kline)
    
    trend_mode = strategy.state_map.trend_type
    trend_strength = strategy.state_map.trend_strength
    
    print(f"  • 趋势市场模式: {trend_mode}")
    print(f"  • 趋势强度: {trend_strength:.4f}")
    
    # 模拟振荡市场
    oscillating_prices = generate_test_data(60, "oscillating")
    strategy.price_history = oscillating_prices[-15:]
    
    for price in oscillating_prices[-5:]:
        kline = MockKLine(price)
        strategy.calc_trend(kline)
    
    osc_mode = strategy.state_map.trend_type
    osc_strength = strategy.state_map.trend_strength
    
    print(f"  • 振荡市场模式: {osc_mode}")
    print(f"  • 趋势强度: {osc_strength:.4f}")
    
    print("✅ 市场模式切换测试通过！")
    return True

if __name__ == "__main__":
    print("🔧 HULL+STC指标最终修复效果验证")
    print("=" * 70)
    
    # 执行所有测试
    tests = [
        test_hull_independence,
        test_stc_plotting_data,
        test_strategy_complete_workflow,
        test_smoothing_effectiveness,
        test_market_mode_switching
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append(False)
    
    # 最终总结
    print("\n" + "=" * 70)
    print("📋 最终修复效果验证总结:")
    
    test_names = [
        "HULL指标独立性",
        "STC指标绘制数据", 
        "策略完整工作流程",
        "HULL平滑效果",
        "市场模式切换"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        print(f"  • {name}: {'✅ 通过' if result else '❌ 失败'}")
    
    all_passed = all(results)
    
    if all_passed:
        print("\n🎉 恭喜！HULL+STC指标修复完全成功！")
        print("\n📈 核心改进总结:")
        print("   ✅ HULL指标平滑处理: 实现真正的SMA/EMA/WMA平滑算法")
        print("   ✅ HULL指标独立性: 不同周期使用独立实例，避免数据干扰")
        print("   ✅ STC指标优化: 信号线计算更稳定，数据范围正确")
        print("   ✅ 副图绘制优化: 移除干扰元素，添加超买超卖参考线")
        print("   ✅ 策略工作流程: 完整的指标计算和状态更新")
        print("\n🚀 策略现在可以正常使用了！")
    else:
        failed_tests = [name for name, result in zip(test_names, results) if not result]
        print(f"\n⚠️ 以下测试失败: {', '.join(failed_tests)}")
        print("请检查具体错误信息并进一步修复")
    
    print("=" * 70) 