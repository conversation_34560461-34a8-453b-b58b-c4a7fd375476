#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OptionStrategy2 HULL+STC测试脚本
"""

import sys
import os
import numpy as np
sys.path.append('pyStrategy')

# 导入策略
from self_strategy.OptionStrategy2 import OptionStrategy2, HullIndicator, STCIndicator

def test_hull_indicator():
    """测试HULL指标"""
    print("=" * 50)
    print("测试HULL指标...")
    
    try:
        hull = HullIndicator()
        
        # 生成测试数据（模拟上涨趋势）
        test_prices = []
        for i in range(100):
            price = 100 + i * 0.1 + np.sin(i * 0.1) * 0.5  # 带噪声的上涨趋势
            test_prices.append(price)
        
        # 测试不同周期的HULL
        periods = [14, 21, 34, 55]
        for period in periods:
            hull_value = hull.calculate(test_prices, period)
            print(f"  • HULL({period}): {hull_value:.4f}")
        
        print("✅ HULL指标测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ HULL指标测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stc_indicator():
    """测试STC指标"""
    print("\n" + "=" * 50)
    print("测试STC指标...")
    
    try:
        stc = STCIndicator()
        
        # 生成测试数据（模拟振荡趋势）
        test_prices = []
        for i in range(200):
            price = 100 + np.sin(i * 0.05) * 5 + np.random.normal(0, 0.1)  # 振荡+噪声
            test_prices.append(price)
        
        # 测试STC指标
        stc_result = stc.calculate(test_prices, 23, 50, 100, 0.5)
        
        print(f"  • STC值: {stc_result['stc']:.2f}")
        print(f"  • STC信号线: {stc_result['stc_signal']:.2f}")
        print(f"  • STC柱状图: {stc_result['stc_histogram']:.4f}")
        
        # 验证STC值在合理范围内
        assert 0 <= stc_result['stc'] <= 100, "STC值超出范围"
        assert 0 <= stc_result['stc_signal'] <= 100, "STC信号线超出范围"
        
        print("✅ STC指标测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ STC指标测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_creation():
    """测试策略创建"""
    print("\n" + "=" * 50)
    print("测试OptionStrategy2策略创建...")
    
    try:
        # 创建策略实例
        strategy = OptionStrategy2()
        print("✅ 策略创建成功")
        
        # 检查核心组件
        print("\n📊 核心组件检查:")
        print(f"  • HULL指标: {type(strategy.hull_indicator).__name__}")
        print(f"  • STC指标: {type(strategy.stc_indicator).__name__}")
        print(f"  • 在线学习模型: {type(strategy.model).__name__}")
        
        # 检查参数设置
        print("\n⚙️ 参数设置:")
        print(f"  • HULL快线周期: {strategy.params_map.hull_fast_period}")
        print(f"  • HULL慢线周期: {strategy.params_map.hull_slow_period}")
        print(f"  • HULL信号周期: {strategy.params_map.hull_signal_period}")
        print(f"  • STC周期: {strategy.params_map.stc_length}")
        print(f"  • STC快速MA: {strategy.params_map.stc_fast_ma}")
        print(f"  • STC慢速MA: {strategy.params_map.stc_slow_ma}")
        print(f"  • 平滑类型: {strategy.params_map.smooth_type}")
        print(f"  • 平滑周期: {strategy.params_map.smooth_period}")
        print(f"  • 最大持仓: {strategy.params_map.max_positions}")
        
        # 检查参数组合
        print("\n🔧 参数组合:")
        for mode, params in strategy.param_sets.items():
            print(f"  • 模式{mode}: {params}")
        
        print("\n✅ 策略创建测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 策略创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_indicators_definition():
    """测试主图和副图指标定义"""
    print("\n" + "=" * 50)
    print("测试指标定义...")
    
    try:
        strategy = OptionStrategy2()
        
        print("\n📊 主图指标数据:")
        main_data = strategy.main_indicator_data
        for key, value in main_data.items():
            print(f"  • {key}: {value}")
        
        print("\n📊 副图指标数据:")
        sub_data = strategy.sub_indicator_data
        for key, value in sub_data.items():
            print(f"  • {key}: {value}")
            
        print("\n✅ 指标定义测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 指标定义测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trend_calculation():
    """测试趋势计算功能"""
    print("\n" + "=" * 50)
    print("测试趋势计算...")
    
    try:
        strategy = OptionStrategy2()
        
        # 模拟价格历史数据
        strategy.price_history = []
        for i in range(50):
            price = 100 + i * 0.2 + np.sin(i * 0.1) * 1.0  # 上涨趋势 + 波动
            strategy.price_history.append(price)
        
        # 设置当前参数
        strategy.current_params = strategy.param_sets["A"]
        
        # 计算指标
        strategy.calc_indicator()
        
        print(f"\n📈 HULL指标计算结果:")
        print(f"  • HULL快线: {strategy.state_map.hull_fast:.4f}")
        print(f"  • HULL慢线: {strategy.state_map.hull_slow:.4f}")
        print(f"  • HULL信号线: {strategy.state_map.hull_signal:.4f}")
        print(f"  • HULL趋势: {strategy.state_map.hull_trend}")
        
        print(f"\n📊 STC指标计算结果:")
        print(f"  • STC值: {strategy.state_map.stc:.2f}")
        print(f"  • STC信号线: {strategy.state_map.stc_signal:.2f}")
        print(f"  • STC柱状图: {strategy.state_map.stc_histogram:.4f}")
        print(f"  • STC趋势: {strategy.state_map.stc_trend}")
        
        print(f"\n🌊 市场状态:")
        print(f"  • 趋势强度: {strategy.state_map.trend_strength:.4f}")
        print(f"  • 波动率: {strategy.state_map.volatility:.6f}")
        print(f"  • 行情模式: {strategy.state_map.trend_type}")
        
        print("\n✅ 趋势计算测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 趋势计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始OptionStrategy2 HULL+STC策略测试")
    
    # 测试各个组件
    test1 = test_hull_indicator()
    test2 = test_stc_indicator()
    test3 = test_strategy_creation()
    test4 = test_indicators_definition()
    test5 = test_trend_calculation()
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print(f"  • HULL指标测试: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"  • STC指标测试: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"  • 策略创建测试: {'✅ 通过' if test3 else '❌ 失败'}")
    print(f"  • 指标定义测试: {'✅ 通过' if test4 else '❌ 失败'}")
    print(f"  • 趋势计算测试: {'✅ 通过' if test5 else '❌ 失败'}")
    
    all_passed = all([test1, test2, test3, test4, test5])
    
    if all_passed:
        print("\n🎉 恭喜！HULL+STC策略修改完成并测试通过！")
        print("   📝 主要改进:")
        print("   • ✅ 用HULL指标替换了EMA指标，提供更快速的趋势识别")
        print("   • ✅ 用STC指标替换了ATR指标，提供更精确的买卖点")
        print("   • ✅ 实现了TradingView社区最佳实践的平滑处理")
        print("   • ✅ 增强了信号过滤器和入场时机优化")
        print("   • ✅ 保持了原有的在线学习和动态参数调整功能")
        print("\n   📊 技术特性:")
        print("   • HULL指标: 基于加权移动平均的快速响应趋势指标")
        print("   • STC指标: 结合MACD和随机指标的复合周期指标")
        print("   • 自适应波动率: 基于HULL指标的动态波动率计算")
        print("   • 智能信号过滤: 多层过滤器避免假信号")
        print("   • 优化止损机制: 基于波动率的动态止损调整")
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息")
    
    print("=" * 50) 