#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化后的事件响应机制和任务线程性能测试
测试智能调度、批量处理、事件驱动等优化特性
"""

import sys
import os
import numpy as np
import time
import threading
from datetime import datetime

# 添加策略路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'pyStrategy'))

from pyStrategy.self_strategy.OptionStrategy2 import (
    AdvancedControlTheoryCore, 
    ControlSignal, 
    OptionStrategy2
)
from pythongo.classdef import KLineData, TickData


def create_test_signal(price: float, index: int) -> ControlSignal:
    """创建测试控制信号"""
    return ControlSignal(
        timestamp=time.time(),
        hull_fast=price + index * 0.1,
        hull_slow=price - index * 0.05,
        hull_signal=price + index * 0.02,
        stc_value=50 + index % 50,
        stc_signal=48 + index % 45,
        price=price,
        volatility=0.01 + (index % 10) * 0.001,
        signal_type='entry' if index % 3 == 0 else 'adjust'
    )


def test_optimized_control_core_performance():
    """测试优化后的控制理论核心性能"""
    print("\n🚀 测试优化后的控制理论核心性能...")
    
    # 创建控制理论核心
    core = AdvancedControlTheoryCore()
    
    # 启动异步处理
    core.start_async_processing()
    print("✓ 异步处理已启动")
    
    # 性能测试参数
    test_signals_count = 1000
    batch_sizes = [1, 5, 10, 20]
    
    for batch_size in batch_sizes:
        print(f"\n📊 测试批量大小: {batch_size}")
        
        # 重置性能监控
        core.performance_monitor = {
            'signals_processed': 0,
            'processing_time_avg': 0.0,
            'queue_utilization': 0.0,
            'thread_efficiency': 0.0,
            'last_activity_time': time.time(),
            'idle_cycles': 0,
            'busy_cycles': 0,
            'error_count': 0,
            'recovery_count': 0
        }
        core.batch_size = batch_size
        
        # 发送测试信号
        start_time = time.time()
        
        for i in range(test_signals_count):
            signal = create_test_signal(3000.0 + i * 0.1, i)
            success = core.add_signal(signal)
            
            if not success:
                print(f"⚠️  信号 {i} 添加失败（队列满）")
            
            # 模拟不同的发送频率
            if i % batch_size == 0:
                time.sleep(0.001)  # 1ms间隔
        
        # 等待处理完成
        time.sleep(2.0)
        
        # 获取性能状态
        status = core.get_performance_status()
        processing_time = time.time() - start_time
        
        print(f"  处理信号数: {status['signals_processed']}")
        print(f"  平均处理时间: {status['processing_time_avg']:.6f}s")
        print(f"  队列利用率: {status['queue_utilization']:.2%}")
        print(f"  线程效率: {status['thread_efficiency']:.2%}")
        print(f"  空闲周期: {status['idle_cycles']}")
        print(f"  繁忙周期: {status['busy_cycles']}")
        print(f"  错误计数: {status['error_count']}")
        print(f"  自适应睡眠时间: {status['adaptive_sleep_time']:.6f}s")
        print(f"  总处理时间: {processing_time:.3f}s")
        print(f"  吞吐量: {status['signals_processed']/processing_time:.1f} 信号/秒")
    
    # 停止异步处理
    core.stop_async_processing()
    print("\n✓ 异步处理已停止")
    
    return True


def test_event_driven_mechanism():
    """测试事件驱动机制"""
    print("\n⚡ 测试事件驱动机制...")
    
    core = AdvancedControlTheoryCore()
    core.start_async_processing()
    
    # 测试空闲状态下的CPU使用率
    print("测试空闲状态（无信号）...")
    idle_start = time.time()
    time.sleep(1.0)  # 空闲1秒
    
    status_idle = core.get_performance_status()
    print(f"  空闲周期: {status_idle['idle_cycles']}")
    print(f"  繁忙周期: {status_idle['busy_cycles']}")
    print(f"  自适应睡眠时间: {status_idle['adaptive_sleep_time']:.6f}s")
    
    # 测试突发负载
    print("\n测试突发负载...")
    burst_signals = 50
    for i in range(burst_signals):
        signal = create_test_signal(3000.0, i)
        core.add_signal(signal)
    
    time.sleep(0.5)  # 等待处理
    
    status_burst = core.get_performance_status()
    print(f"  处理信号数: {status_burst['signals_processed']}")
    print(f"  自适应睡眠时间: {status_burst['adaptive_sleep_time']:.6f}s")
    print(f"  队列利用率: {status_burst['queue_utilization']:.2%}")
    
    core.stop_async_processing()
    return True


def test_error_recovery_mechanism():
    """测试错误恢复机制"""
    print("\n🛡️  测试错误恢复机制...")
    
    core = AdvancedControlTheoryCore()
    core.start_async_processing()
    
    # 模拟错误情况
    original_process_method = core._process_control_signal
    error_count = 0
    
    def error_prone_process(signal):
        nonlocal error_count
        error_count += 1
        if error_count <= 5:  # 前5次抛出错误
            raise ValueError("模拟处理错误")
        return original_process_method(signal)
    
    # 替换处理方法
    core._process_control_signal = error_prone_process
    
    # 发送测试信号
    for i in range(10):
        signal = create_test_signal(3000.0, i)
        core.add_signal(signal)
        time.sleep(0.1)
    
    time.sleep(1.0)  # 等待处理
    
    status = core.get_performance_status()
    print(f"  错误计数: {status['error_count']}")
    print(f"  恢复计数: {status['recovery_count']}")
    print(f"  处理信号数: {status['signals_processed']}")
    
    # 恢复原始方法
    core._process_control_signal = original_process_method
    
    core.stop_async_processing()
    return True


def test_strategy_event_optimization():
    """测试策略事件优化"""
    print("\n📈 测试策略事件优化...")
    
    try:
        strategy = OptionStrategy2()
        
        # 设置参数
        strategy.params_map.exchange = "TEST"
        strategy.params_map.instrument_id = "TEST2501"
        strategy.params_map.kline_style = "M1"
        
        # 初始化策略
        strategy.on_init()
        
        # 测试事件处理效率
        start_time = time.time()
        
        # 模拟大量tick数据
        for i in range(100):
            tick = TickData()
            tick.datetime = datetime.now()
            tick.last_price = 3000.0 + i * 0.1
            tick.bid_price_1 = tick.last_price - 0.1
            tick.ask_price_1 = tick.last_price + 0.1
            tick.volume = 1000
            
            strategy.on_tick(tick)
        
        processing_time = time.time() - start_time
        print(f"  处理100个tick用时: {processing_time:.3f}s")
        print(f"  平均每个tick处理时间: {processing_time/100*1000:.2f}ms")
        
        # 测试K线处理
        start_time = time.time()
        
        for i in range(50):
            kline = KLineData()
            kline.datetime = datetime.now()
            kline.open = 3000.0 + i
            kline.high = 3000.0 + i + 0.5
            kline.low = 3000.0 + i - 0.5
            kline.close = 3000.0 + i + 0.2
            kline.volume = 1000
            
            strategy.on_kline(kline)
        
        processing_time = time.time() - start_time
        print(f"  处理50个K线用时: {processing_time:.3f}s")
        print(f"  平均每个K线处理时间: {processing_time/50*1000:.2f}ms")
        
        # 检查控制理论引擎状态
        if hasattr(strategy, 'control_theory_trading_engine'):
            engine = strategy.control_theory_trading_engine
            if hasattr(engine, 'advanced_control_core'):
                core_status = engine.advanced_control_core.get_performance_status()
                print(f"  控制理论核心处理信号数: {core_status['signals_processed']}")
                print(f"  控制理论核心线程效率: {core_status['thread_efficiency']:.2%}")
        
        # 清理资源
        strategy.on_exit()
        
        return True
        
    except Exception as e:
        print(f"❌ 策略事件优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_memory_usage():
    """测试内存使用优化"""
    print("\n💾 测试内存使用优化...")
    
    import psutil
    import gc
    
    # 获取当前进程
    process = psutil.Process()
    
    # 记录初始内存
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    print(f"  初始内存使用: {initial_memory:.2f} MB")
    
    # 创建控制理论核心并运行大量操作
    core = AdvancedControlTheoryCore()
    core.start_async_processing()
    
    # 发送大量信号
    for i in range(5000):
        signal = create_test_signal(3000.0, i)
        core.add_signal(signal)
        
        if i % 1000 == 0:
            current_memory = process.memory_info().rss / 1024 / 1024
            print(f"  处理 {i} 信号后内存: {current_memory:.2f} MB")
    
    # 等待处理完成
    time.sleep(2.0)
    
    # 最终内存使用
    final_memory = process.memory_info().rss / 1024 / 1024
    print(f"  最终内存使用: {final_memory:.2f} MB")
    print(f"  内存增长: {final_memory - initial_memory:.2f} MB")
    
    # 清理资源
    core.stop_async_processing()
    del core
    gc.collect()
    
    # 清理后内存
    cleanup_memory = process.memory_info().rss / 1024 / 1024
    print(f"  清理后内存: {cleanup_memory:.2f} MB")
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始优化后的事件响应机制和任务线程性能测试")
    print("=" * 80)
    
    tests = [
        ("控制理论核心性能", test_optimized_control_core_performance),
        ("事件驱动机制", test_event_driven_mechanism),
        ("错误恢复机制", test_error_recovery_mechanism),
        ("策略事件优化", test_strategy_event_optimization),
        ("内存使用优化", test_memory_usage),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed_tests += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 测试总结
    print("\n" + "=" * 80)
    print("📋 优化效果测试总结")
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有优化测试通过！")
        print("\n💡 优化效果总结:")
        print("1. ✅ 智能线程调度 - 根据负载动态调整睡眠时间")
        print("2. ✅ 批量处理机制 - 提高信号处理效率")
        print("3. ✅ 事件驱动架构 - 避免无意义的CPU空转")
        print("4. ✅ 实时性能监控 - 全面的性能指标跟踪")
        print("5. ✅ 智能异常恢复 - 自动错误处理和系统恢复")
        print("6. ✅ 内存使用优化 - 有效控制内存增长")
        print("7. ✅ 事件处理精简 - 移除无功能的空方法")
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 个测试失败")
        print("请检查相关优化实现")


if __name__ == "__main__":
    main() 