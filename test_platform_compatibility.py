#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Strategy2.py 平台适应性测试脚本
测试平台兼容性调整是否正常工作
"""

import sys
import os
import traceback
from unittest.mock import Mock, MagicMock, patch

# 添加策略路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'pyStrategy'))

def test_platform_compatibility():
    """测试平台适应性功能"""
    print("=" * 60)
    print("Strategy2.py 平台适应性测试")
    print("=" * 60)
    
    try:
        # 导入策略
        from self_strategy.Strategy2 import Strategy2, Params, State
        
        print("✓ 策略导入成功")
        
        # 创建策略实例
        strategy = Strategy2()
        print("✓ 策略实例创建成功")
        
        # 测试参数初始化
        assert hasattr(strategy, 'params_map'), "参数映射缺失"
        assert hasattr(strategy, 'state_map'), "状态映射缺失"
        assert strategy.platform_initialized == False, "平台初始化状态错误"
        print("✓ 参数和状态初始化正常")
        
        # 测试平台适应性变量
        assert hasattr(strategy, 'widget_available'), "widget可用性检查缺失"
        assert hasattr(strategy, 'kline_generator_ready'), "K线生成器就绪状态缺失"
        print("✓ 平台适应性变量存在")
        
        # 测试指标数据属性
        main_data = strategy.main_indicator_data
        sub_data = strategy.sub_indicator_data
        assert isinstance(main_data, dict), "主图指标数据类型错误"
        assert isinstance(sub_data, dict), "副图指标数据类型错误"
        print("✓ 指标数据属性正常")
        
        # 测试错误处理机制
        print("\n测试错误处理机制...")
        
        # 模拟K线数据
        mock_kline = Mock()
        mock_kline.close = 100.0
        mock_kline.open = 99.0
        mock_kline.high = 101.0
        mock_kline.low = 98.0
        
        # 模拟TICK数据
        mock_tick = Mock()
        mock_tick.ask_price1 = 100.1
        mock_tick.bid_price1 = 99.9
        mock_tick.ask_price2 = 100.2
        mock_tick.bid_price2 = 99.8
        
        # 测试on_tick错误处理
        try:
            strategy.on_tick(mock_tick)
            print("✓ on_tick错误处理正常")
        except Exception as e:
            print(f"✗ on_tick错误处理失败: {e}")
        
        # 测试callback错误处理
        try:
            strategy.callback(mock_kline)
            print("✓ callback错误处理正常")
        except Exception as e:
            print(f"✗ callback错误处理失败: {e}")
        
        # 测试real_time_callback错误处理
        try:
            strategy.real_time_callback(mock_kline)
            print("✓ real_time_callback错误处理正常")
        except Exception as e:
            print(f"✗ real_time_callback错误处理失败: {e}")
        
        # 测试calc_indicator错误处理
        try:
            strategy.calc_indicator()
            print("✓ calc_indicator错误处理正常")
        except Exception as e:
            print(f"✗ calc_indicator错误处理失败: {e}")
        
        # 测试exec_signal错误处理
        try:
            strategy.exec_signal()
            print("✓ exec_signal错误处理正常")
        except Exception as e:
            print(f"✗ exec_signal错误处理失败: {e}")
        
        # 测试平台初始化
        print("\n测试平台初始化...")
        try:
            # 简化平台初始化测试
            strategy.on_start()
            
            # 检查基本初始化
            assert hasattr(strategy, 'kline_generator'), "K线生成器未初始化"
            print("✓ 平台初始化正常")
            
        except Exception as e:
            print(f"✗ 平台初始化失败: {e}")
            # 不打印详细错误，避免测试中断
        
        # 测试数据安全性
        print("\n测试数据安全性...")
        
        # 测试空数据处理
        strategy.close_prices = []
        try:
            strategy.calc_indicator()
            print("✓ 空数据处理正常")
        except Exception as e:
            print(f"✗ 空数据处理失败: {e}")
        
        # 测试None数据处理
        strategy.close_prices = None
        try:
            strategy.calc_indicator()
            print("✓ None数据处理正常")
        except Exception as e:
            print(f"✗ None数据处理失败: {e}")
        
        # 测试异常数据处理
        strategy.close_prices = [100, 101, "invalid", 103]
        try:
            strategy.calc_indicator()
            print("✓ 异常数据处理正常")
        except Exception as e:
            print(f"✗ 异常数据处理失败: {e}")
        
        print("\n" + "=" * 60)
        print("平台适应性测试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n✗ 平台适应性测试失败: {e}")
        traceback.print_exc()
        return False

def test_strategy_integration():
    """测试策略集成功能"""
    print("\n" + "=" * 60)
    print("Strategy2.py 集成功能测试")
    print("=" * 60)
    
    try:
        from self_strategy.Strategy2 import Strategy2
        
        # 创建策略实例
        strategy = Strategy2()
        
        # 设置测试参数
        strategy.params_map.exchange = "TEST"
        strategy.params_map.instrument_id = "TEST001"
        strategy.params_map.kline_style = "M1"
        
        # 模拟价格数据
        strategy.close_prices = [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110]
        strategy.high_prices = [101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111]
        strategy.low_prices = [99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109]
        
        # 测试指标计算
        try:
            strategy.calc_indicator()
            print("✓ 指标计算集成正常")
        except Exception as e:
            print(f"✗ 指标计算集成失败: {e}")
        
        # 测试信号计算
        mock_kline = Mock()
        mock_kline.close = 110.0
        try:
            strategy.calc_signal(mock_kline)
            print("✓ 信号计算集成正常")
        except Exception as e:
            print(f"✗ 信号计算集成失败: {e}")
        
        # 测试信号执行
        try:
            strategy.exec_signal()
            print("✓ 信号执行集成正常")
        except Exception as e:
            print(f"✗ 信号执行集成失败: {e}")
        
        print("✓ 策略集成功能测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 策略集成功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_error_recovery():
    """测试错误恢复机制"""
    print("\n" + "=" * 60)
    print("Strategy2.py 错误恢复机制测试")
    print("=" * 60)
    
    try:
        from self_strategy.Strategy2 import Strategy2
        
        strategy = Strategy2()
        
        # 测试各种异常情况下的恢复
        test_cases = [
            ("空K线生成器", lambda: setattr(strategy, 'kline_generator', None)),
            ("空producer", lambda: setattr(strategy.kline_generator, 'producer', None) if hasattr(strategy, 'kline_generator') else None),
            ("空widget", lambda: setattr(strategy, 'widget', None)),
            ("空价格数据", lambda: setattr(strategy, 'close_prices', [])),
            ("无效价格数据", lambda: setattr(strategy, 'close_prices', [None, "invalid", 100])),
        ]
        
        for test_name, setup_func in test_cases:
            try:
                if setup_func:
                    setup_func()
                
                # 尝试执行策略方法
                strategy.calc_indicator()
                strategy.exec_signal()
                
                print(f"✓ {test_name}错误恢复正常")
                
            except Exception as e:
                print(f"✗ {test_name}错误恢复失败: {e}")
        
        print("✓ 错误恢复机制测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 错误恢复机制测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始Strategy2.py平台适应性测试...")
    
    # 运行所有测试
    tests = [
        test_platform_compatibility,
        test_strategy_integration,
        test_error_recovery
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"测试 {test_func.__name__} 执行失败: {e}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有平台适应性测试通过！Strategy2.py已针对平台进行优化。")
    else:
        print("⚠️  部分测试失败，请检查平台兼容性问题。") 