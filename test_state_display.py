#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
控制理论交易引擎状态显示测试
验证State类的实时状态监控功能
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加策略路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'pyStrategy'))

from pyStrategy.self_strategy.OptionStrategy2 import (
    OptionStrategy2, 
    State,
    SCIPY_AVAILABLE,
    CONTROL_AVAILABLE,
    FILTERPY_AVAILABLE,
    NUMBA_AVAILABLE
)
from pythongo.classdef import KLineData


def print_state_section(title: str, state_dict: dict, show_zeros: bool = False):
    """打印状态部分"""
    print(f"\n{'='*20} {title} {'='*20}")
    
    for key, value in state_dict.items():
        # 跳过零值（除非明确要求显示）
        if not show_zeros and (value == 0 or value == 0.0 or value == "" or value == False):
            continue
            
        # 格式化显示
        if isinstance(value, float):
            if abs(value) < 0.001 and value != 0:
                print(f"  {key}: {value:.6f}")
            else:
                print(f"  {key}: {value:.4f}")
        elif isinstance(value, bool):
            status = "✅" if value else "❌"
            print(f"  {key}: {status} {value}")
        else:
            print(f"  {key}: {value}")


def test_state_display():
    """测试状态显示功能"""
    print("🚀 开始控制理论交易引擎状态显示测试")
    print("=" * 80)
    
    # 创建策略实例
    strategy = OptionStrategy2()
    
    # 初始化策略
    strategy.on_init()
    strategy.on_start()
    
    print("📊 初始状态显示:")
    
    # 获取状态字典
    state_dict = strategy.state_map.dict()
    
    # 按类别显示状态
    categories = {
        "基础技术指标": [
            "hull_fast", "hull_slow", "hull_signal", "hull_trend",
            "stc", "stc_signal", "stc_histogram", "stc_trend"
        ],
        "控制理论核心": [
            "system_stability", "control_output", "phase_margin", "gain_margin",
            "signal_strength", "trade_confidence", "lyapunov_stability",
            "state_vector_norm", "covariance_trace"
        ],
        "多控制器融合": [
            "pid_output", "adaptive_output", "robust_output", "mpc_output",
            "sliding_output", "neural_output", "fuzzy_output", "lqr_output",
            "pid_weight", "adaptive_weight", "robust_weight", "mpc_weight"
        ],
        "异步处理引擎": [
            "engine_running", "thread_efficiency", "adaptive_sleep_time",
            "input_queue_size", "output_queue_size", "queue_utilization",
            "signals_processed", "processing_time_avg", "busy_cycles", "idle_cycles"
        ],
        "依赖库状态": [
            "scipy_available", "control_available", "filterpy_available", "numba_available",
            "signal_filters_active", "advanced_kalman_active", "control_models_loaded", "numba_acceleration_active"
        ],
        "交易决策": [
            "entry_conditions_met", "exit_conditions_met", "should_trade", "exit_signal",
            "protection_active", "risk_level", "position_adjustment"
        ],
        "性能统计": [
            "total_signals", "successful_trades", "failed_trades", "win_rate",
            "average_confidence", "average_stability", "performance_trend"
        ],
        "系统健康": [
            "system_health", "cpu_usage", "memory_usage", "latency_ms",
            "throughput_signals_per_sec", "continuous_runtime", "uptime_percentage"
        ],
        "调试诊断": [
            "error_count", "recovery_count", "last_error_message",
            "kalman_updates", "control_calculations", "stability_analyses",
            "update_frequency"
        ]
    }
    
    # 显示各类别状态
    for category, fields in categories.items():
        category_data = {field: state_dict.get(field, "N/A") for field in fields}
        print_state_section(category, category_data)
    
    # 模拟K线数据更新
    print(f"\n{'='*80}")
    print("📈 模拟K线数据更新...")
    
    for i in range(5):
        print(f"\n🔄 第 {i+1} 次更新:")
        
        # 创建模拟K线数据
        kline = KLineData()
        kline.datetime = datetime.now()
        base_price = 3000 + i * 0.5
        kline.open = base_price
        kline.high = base_price + 1.0
        kline.low = base_price - 1.0
        kline.close = base_price + 0.5
        kline.volume = 1000 + i * 100
        
        # 处理K线数据
        strategy.on_kline(kline)
        
        # 显示关键状态变化
        current_state = strategy.state_map.dict()
        
        key_metrics = {
            "HULL快线": current_state.get("hull_fast", 0),
            "HULL慢线": current_state.get("hull_slow", 0),
            "STC值": current_state.get("stc", 0),
            "系统稳定性": current_state.get("system_stability", 0),
            "信号强度": current_state.get("signal_strength", 0),
            "交易置信度": current_state.get("trade_confidence", 0),
            "引擎运行": current_state.get("engine_running", False),
            "已处理信号": current_state.get("signals_processed", 0),
            "系统健康": current_state.get("system_health", "未知")
        }
        
        print_state_section("关键指标", key_metrics, show_zeros=True)
        
        time.sleep(0.5)  # 模拟实时更新间隔
    
    # 显示依赖库状态总结
    print(f"\n{'='*80}")
    print("📦 依赖库状态总结:")
    
    dependencies = {
        "SciPy": SCIPY_AVAILABLE,
        "Control": CONTROL_AVAILABLE,
        "FilterPy": FILTERPY_AVAILABLE,
        "Numba": NUMBA_AVAILABLE
    }
    
    available_count = sum(dependencies.values())
    total_count = len(dependencies)
    
    for name, available in dependencies.items():
        status = "✅ 可用" if available else "❌ 不可用"
        print(f"  {name}: {status}")
    
    print(f"\n📊 依赖库可用性: {available_count}/{total_count} ({available_count/total_count*100:.1f}%)")
    
    # 显示功能模块状态
    print(f"\n📋 功能模块状态:")
    final_state = strategy.state_map.dict()
    
    modules = {
        "信号滤波器": final_state.get("signal_filters_active", False),
        "高级卡尔曼滤波": final_state.get("advanced_kalman_active", False),
        "控制系统模型": final_state.get("control_models_loaded", False),
        "Numba加速": final_state.get("numba_acceleration_active", False)
    }
    
    for name, active in modules.items():
        status = "🟢 激活" if active else "🔴 未激活"
        print(f"  {name}: {status}")
    
    # 清理资源
    strategy.on_exit()
    
    print(f"\n{'='*80}")
    print("✅ 控制理论交易引擎状态显示测试完成！")
    
    return True


def test_state_json_export():
    """测试状态JSON导出功能"""
    print(f"\n{'='*80}")
    print("📄 测试状态JSON导出功能...")
    
    try:
        strategy = OptionStrategy2()
        strategy.on_init()
        strategy.on_start()
        
        # 获取状态字典
        state_dict = strategy.state_map.dict()
        
        # 导出为JSON
        json_output = json.dumps(state_dict, indent=2, ensure_ascii=False, default=str)
        
        # 保存到文件
        with open("control_theory_engine_state.json", "w", encoding="utf-8") as f:
            f.write(json_output)
        
        print("✅ 状态已导出到 control_theory_engine_state.json")
        print(f"📊 状态字段总数: {len(state_dict)}")
        
        # 显示JSON文件大小
        file_size = len(json_output.encode('utf-8'))
        print(f"📁 JSON文件大小: {file_size} 字节")
        
        strategy.on_exit()
        return True
        
    except Exception as e:
        print(f"❌ JSON导出测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🎯 控制理论交易引擎状态显示测试套件")
    print("=" * 80)
    
    tests = [
        ("状态显示功能", test_state_display),
        ("状态JSON导出", test_state_json_export),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed_tests += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 测试总结
    print("\n" + "=" * 80)
    print("📋 状态显示测试总结")
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有状态显示测试通过！")
        print("\n💡 用户现在可以实时查看:")
        print("1. 📊 基础技术指标状态 (HULL、STC)")
        print("2. 🎛️  控制理论核心状态 (稳定性、置信度)")
        print("3. 🔄 多控制器融合状态 (PID、自适应、鲁棒等)")
        print("4. ⚡ 异步处理引擎状态 (队列、性能、线程)")
        print("5. 📦 依赖库状态 (SciPy、Control、FilterPy、Numba)")
        print("6. 🎯 交易决策状态 (开仓条件、风险评估)")
        print("7. 📈 性能统计状态 (胜率、盈亏比、夏普比率)")
        print("8. 🏥 系统健康状态 (CPU、内存、延迟)")
        print("9. 🔧 调试诊断状态 (错误计数、更新频率)")
        print("10. 🎨 用户界面控制 (显示选项、刷新间隔)")
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 个测试失败")


if __name__ == "__main__":
    main() 