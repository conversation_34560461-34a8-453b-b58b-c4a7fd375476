#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OptionStrategy2a测试脚本
"""

import sys
import os
sys.path.append('pyStrategy')

# 导入策略
from self_strategy.OptionStrategy2a import OptionStrategy2a

def test_strategy_creation():
    """测试策略创建"""
    print("=" * 50)
    print("测试OptionStrategy2a策略创建...")
    
    try:
        # 创建策略实例
        strategy = OptionStrategy2a()
        print("✅ 策略创建成功")
        
        # 检查核心组件
        print("\n📊 核心组件检查:")
        print(f"  • HULL指标: {type(strategy.hull_indicator).__name__}")
        print(f"  • STC指标: {type(strategy.stc_indicator).__name__}")
        print(f"  • 特征构建器: {type(strategy.feature_builder).__name__}")
        print(f"  • ML管理器: {type(strategy.ml_manager).__name__}")
        print(f"  • 信号引擎: {type(strategy.signal_engine).__name__}")
        print(f"  • 风控管理器: {type(strategy.risk_manager).__name__}")
        
        # 检查参数设置
        print("\n⚙️ 参数设置:")
        print(f"  • HULL快线周期: {strategy.params_map.hull_fast_period}")
        print(f"  • HULL慢线周期: {strategy.params_map.hull_slow_period}")
        print(f"  • STC周期: {strategy.params_map.stc_length}")
        print(f"  • ML模型周期: {strategy.params_map.river_periods}")
        print(f"  • 最大持仓: {strategy.params_map.max_positions}")
        
        # 检查ML管理器统计
        print("\n🤖 ML管理器状态:")
        ml_stats = strategy.ml_manager.get_model_statistics()
        print(f"  • 模型数量: {ml_stats.get('model_count', 0)}")
        print(f"  • River可用性: {ml_stats.get('river_available', False)}")
        print(f"  • 训练样本数: {ml_stats.get('total_training_samples', 0)}")
        
        # 测试技术指标计算
        print("\n📈 技术指标测试:")
        test_prices = [100.0, 101.0, 99.5, 102.0, 103.5, 101.5, 104.0, 102.5, 105.0, 103.0] * 10
        
        # 测试HULL指标
        hull_result = strategy.hull_indicator.calculate(test_prices)
        print(f"  • HULL快线: {hull_result.get('hull_fast', 0):.2f}")
        print(f"  • HULL慢线: {hull_result.get('hull_slow', 0):.2f}")
        print(f"  • 趋势强度: {hull_result.get('trend_strength', 0):.3f}")
        
        # 测试STC指标
        stc_result = strategy.stc_indicator.calculate(test_prices)
        print(f"  • STC值: {stc_result.get('stc', 50):.2f}")
        print(f"  • STC信号线: {stc_result.get('stc_signal', 50):.2f}")
        print(f"  • STC柱状图: {stc_result.get('stc_histogram', 0):.2f}")
        
        print("\n✅ 所有测试通过！策略已完全修复。")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_sub_indicators():
    """测试主图和副图指标定义"""
    print("\n" + "=" * 50)
    print("测试指标定义...")
    
    try:
        strategy = OptionStrategy2a()
        
        print("\n📊 主图指标:")
        main_indicators = strategy.main_indicator
        for indicator in main_indicators:
            print(f"  • {indicator}")
        
        print("\n📊 副图指标:")
        sub_indicators = strategy.sub_indicator
        for indicator in sub_indicators:
            print(f"  • {indicator}")
            
        print("\n✅ 指标定义测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 指标定义测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始OptionStrategy2a策略测试")
    
    # 测试策略创建
    test1_passed = test_strategy_creation()
    
    # 测试指标定义
    test2_passed = test_main_sub_indicators()
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print(f"  • 策略创建测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"  • 指标定义测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 恭喜！OptionStrategy2a策略已完全修复！")
        print("   • STC指标问题已解决")
        print("   • River库集成功能已优化")
        print("   • 策略现在可以正常运行")
    else:
        print("\n⚠️ 还有一些问题需要解决")
    
    print("=" * 50) 