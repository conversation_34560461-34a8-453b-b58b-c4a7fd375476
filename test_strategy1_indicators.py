#!/usr/bin/env python3
"""
Strategy1技术指标窗口测试脚本
验证技术指标窗口相关功能是否正常
"""

import sys
import os

# 添加策略路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'pyStrategy', 'self_strategy'))

def test_indicator_data_methods():
    """测试指标数据方法"""
    print("=== 测试指标数据方法 ===")
    
    try:
        # 模拟导入Strategy1类
        from Strategy1 import Strategy1, Params, State
        
        # 创建策略实例
        strategy = Strategy1()
        
        # 初始化参数和状态
        strategy.params_map = Params()
        strategy.state_map = State()
        
        # 初始化必要的组件
        from Strategy1 import HullMovingAverage, SchaffTrendCycle
        strategy.hull_calculators = {
            9: HullMovingAverage(9),
            21: HullMovingAverage(21),
            55: HullMovingAverage(55)
        }
        
        # 测试main_indicator_data方法
        print("测试main_indicator_data方法...")
        main_data = strategy.main_indicator_data
        print(f"主图指标数据: {main_data}")
        
        # 验证返回值类型和内容
        assert isinstance(main_data, dict), "main_indicator_data应该返回字典"
        expected_keys = ['HULL9', 'HULL21', 'HULL55', 'HULL_MA', 'STC', 'STC_Signal']
        for key in expected_keys:
            assert key in main_data, f"缺少主图指标: {key}"
            assert isinstance(main_data[key], (int, float)), f"{key}应该是数值类型"
            assert main_data[key] is not None, f"{key}不应该是None"
        
        print("✓ main_indicator_data方法测试通过")
        
        # 测试sub_indicator_data方法
        print("测试sub_indicator_data方法...")
        sub_data = strategy.sub_indicator_data
        print(f"副图指标数据: {sub_data}")
        
        # 验证返回值类型和内容
        assert isinstance(sub_data, dict), "sub_indicator_data应该返回字典"
        expected_sub_keys = ['Confidence', 'WaveStrength', 'PatternStrength', 'IntegratedSignal',
                            'SignalThreshold', 'ConfidenceThreshold', 'RedundancyScore', 'ThresholdAdjustment']
        for key in expected_sub_keys:
            assert key in sub_data, f"缺少副图指标: {key}"
            assert isinstance(sub_data[key], (int, float)), f"{key}应该是数值类型"
            assert sub_data[key] is not None, f"{key}不应该是None"
        
        print("✓ sub_indicator_data方法测试通过")
        
        # 测试main_indicator属性
        print("测试main_indicator属性...")
        main_indicator = strategy.main_indicator
        print(f"主图指标列表: {main_indicator}")
        assert isinstance(main_indicator, list), "main_indicator应该返回列表"
        assert len(main_indicator) > 0, "main_indicator不应该为空"
        print("✓ main_indicator属性测试通过")
        
        # 测试sub_indicator属性
        print("测试sub_indicator属性...")
        sub_indicator = strategy.sub_indicator
        print(f"副图指标列表: {sub_indicator}")
        assert isinstance(sub_indicator, list), "sub_indicator应该返回列表"
        assert len(sub_indicator) > 0, "sub_indicator不应该为空"
        print("✓ sub_indicator属性测试通过")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_widget_recv_kline_data():
    """测试widget.recv_kline数据格式"""
    print("\n=== 测试widget.recv_kline数据格式 ===")
    
    try:
        from Strategy1 import Strategy1, Params, State
        
        # 创建策略实例
        strategy = Strategy1()
        strategy.params_map = Params()
        strategy.state_map = State()
        
        # 初始化必要的组件
        from Strategy1 import HullMovingAverage
        strategy.hull_calculators = {
            9: HullMovingAverage(9),
            21: HullMovingAverage(21),
            55: HullMovingAverage(55)
        }
        
        # 模拟K线数据
        class MockKLine:
            def __init__(self):
                self.open = 100.0
                self.high = 102.0
                self.low = 99.0
                self.close = 101.0
                self.volume = 1000
        
        kline = MockKLine()
        strategy.signal_price = 101.5
        
        # 构建recv_kline数据
        recv_data = {
            "kline": kline,
            "signal_price": strategy.signal_price,
            **strategy.main_indicator_data,
            **strategy.sub_indicator_data
        }
        
        print("recv_kline数据结构:")
        for key, value in recv_data.items():
            if key != "kline":  # 跳过kline对象
                print(f"  {key}: {value} ({type(value).__name__})")
        
        # 验证数据完整性
        assert "kline" in recv_data, "缺少kline数据"
        assert "signal_price" in recv_data, "缺少signal_price数据"
        
        # 验证所有指标数据都是数值类型
        for key, value in recv_data.items():
            if key not in ["kline"]:  # 排除非数值字段
                assert isinstance(value, (int, float)), f"{key}应该是数值类型，实际是{type(value)}"
                assert value is not None, f"{key}不应该是None"
        
        print("✓ widget.recv_kline数据格式测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_update_status_bar():
    """测试update_status_bar方法"""
    print("\n=== 测试update_status_bar方法 ===")
    
    try:
        from Strategy1 import Strategy1, Params, State
        
        # 创建策略实例
        strategy = Strategy1()
        strategy.params_map = Params()
        strategy.state_map = State()
        
        # 设置一些测试数据
        strategy.params_map.instrument_id = "TEST001"
        strategy.state_map.trend_strength = 0.05
        strategy.state_map.volatility_level = 0.02
        strategy.state_map.integrated_signal = 0.75
        strategy.state_map.signal_confidence = 0.8
        strategy.state_map.pattern_detected = "bullish_engulfing"
        strategy.state_map.wave_type = "uptrend"
        strategy.state_map.redundancy_score = 0.3
        
        # 模拟get_position方法
        class MockPosition:
            def __init__(self):
                self.net_position = 2
        
        def mock_get_position(instrument_id):
            return MockPosition()
        
        strategy.get_position = mock_get_position
        
        # 测试update_status_bar方法
        print("测试update_status_bar方法...")
        strategy.update_status_bar()  # 应该不会抛出异常
        
        print("✓ update_status_bar方法测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("Strategy1 技术指标窗口功能测试")
    print("=" * 50)
    
    tests = [
        ("指标数据方法", test_indicator_data_methods),
        ("widget.recv_kline数据格式", test_widget_recv_kline_data),
        ("update_status_bar方法", test_update_status_bar)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n开始测试: {test_name}")
            result = test_func()
            if result:
                print(f"✓ {test_name} 测试通过")
                passed += 1
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有技术指标窗口功能测试通过！")
        print("\n修复内容验证:")
        print("✓ 移除了重复的on_start方法")
        print("✓ 修复了main_indicator_data返回None的问题")
        print("✓ 修复了sub_indicator_data返回None的问题")
        print("✓ 添加了widget安全检查")
        print("✓ 添加了update_status_bar方法")
        print("✓ 添加了异常处理和调试信息")
        return True
    else:
        print("❌ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
