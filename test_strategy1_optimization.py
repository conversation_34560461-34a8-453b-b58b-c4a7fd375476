#!/usr/bin/env python3
"""
Strategy1优化测试脚本
测试多模块融合信号+自适应权重+置信度+灵活阈值体系
"""

import sys
import os
import numpy as np
from collections import deque
import time

# 添加策略路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'pyStrategy', 'self_strategy'))

try:
    from Strategy1 import (
        HullMovingAverage, 
        SchaffTrendCycle, 
        CandlePatternRecognizer,
        WavePatternRecognizer,
        FuzzySystem,
        ModuleCorrelationAnalyzer,
        DynamicThresholdOptimizer,
        AdaptiveWeightAllocator,
        SignalFusionEngine,
        MarketRegimeClassifier
    )
    print("✓ 成功导入所有优化后的组件")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)

def test_module_correlation_analyzer():
    """测试模块相关性分析器"""
    print("\n=== 测试模块相关性分析器 ===")
    
    analyzer = ModuleCorrelationAnalyzer(window_size=20)
    
    # 模拟信号数据
    for i in range(25):
        signals = {
            'technical': np.sin(i * 0.1) + np.random.normal(0, 0.1),
            'pattern': np.sin(i * 0.1 + 0.5) + np.random.normal(0, 0.1),  # 与technical相关
            'wave': np.random.normal(0, 0.5),  # 独立信号
            'fuzzy': np.cos(i * 0.15) + np.random.normal(0, 0.1)  # 不同频率
        }
        analyzer.update_signals(signals)
    
    # 计算相关性
    correlations = analyzer.calculate_correlations()
    print(f"模块间相关性: {correlations}")
    
    # 检测冗余
    redundancy_score, redundant_pairs = analyzer.detect_redundancy()
    print(f"冗余度得分: {redundancy_score:.3f}")
    print(f"冗余模块对: {redundant_pairs}")
    
    # 测试权重调整
    base_weights = {'technical': 0.25, 'pattern': 0.25, 'wave': 0.25, 'fuzzy': 0.25}
    adjusted_weights = analyzer.get_adjusted_weights(base_weights)
    print(f"原始权重: {base_weights}")
    print(f"调整后权重: {adjusted_weights}")
    
    return True

def test_dynamic_threshold_optimizer():
    """测试动态阈值优化器"""
    print("\n=== 测试动态阈值优化器 ===")
    
    optimizer = DynamicThresholdOptimizer(
        base_signal_threshold=0.6,
        base_confidence_threshold=0.7,
        adaptation_speed=0.1
    )
    
    # 模拟不同市场状态
    market_scenarios = [
        (0.01, 0.005, "低波动率+弱趋势"),
        (0.05, 0.02, "高波动率+强趋势"),
        (0.03, 0.001, "中等波动率+无趋势"),
        (0.02, 0.015, "中等波动率+中等趋势")
    ]
    
    for volatility, trend_strength, description in market_scenarios:
        optimizer.update_market_state(volatility, trend_strength)
        signal_threshold, confidence_threshold = optimizer.get_current_thresholds()
        
        print(f"{description}:")
        print(f"  波动率: {volatility:.3f}, 趋势强度: {trend_strength:.3f}")
        print(f"  信号阈值: {signal_threshold:.3f}, 置信度阈值: {confidence_threshold:.3f}")
        
        # 测试交易决策
        test_signals = [(0.5, 0.8), (0.7, 0.6), (0.8, 0.9)]
        for signal_strength, confidence in test_signals:
            should_trade = optimizer.should_trade(signal_strength, confidence)
            print(f"  信号强度: {signal_strength}, 置信度: {confidence} -> 交易: {should_trade}")
        print()
    
    return True

def test_signal_fusion_with_correlation():
    """测试信号融合与相关性分析的集成"""
    print("\n=== 测试信号融合与相关性分析集成 ===")
    
    # 初始化组件
    correlation_analyzer = ModuleCorrelationAnalyzer()
    weight_allocator = AdaptiveWeightAllocator()
    signal_fusion = SignalFusionEngine()
    
    # 模拟多轮信号
    for round_num in range(10):
        # 生成模拟信号（某些模块故意设置高相关性）
        base_signal = np.sin(round_num * 0.2)
        signals = {
            'technical': base_signal + np.random.normal(0, 0.1),
            'pattern': base_signal * 0.8 + np.random.normal(0, 0.15),  # 与technical高度相关
            'wave': np.random.normal(0, 0.3),  # 独立信号
            'fuzzy': -base_signal * 0.6 + np.random.normal(0, 0.1)  # 反向相关
        }
        
        # 更新相关性分析
        correlation_analyzer.update_signals(signals)
        
        # 模拟性能数据
        performance = {
            'technical': 0.6 + np.random.normal(0, 0.1),
            'pattern': 0.55 + np.random.normal(0, 0.1),
            'wave': 0.7 + np.random.normal(0, 0.1),
            'fuzzy': 0.65 + np.random.normal(0, 0.1)
        }
        
        # 更新权重
        base_weights = weight_allocator.update_weights("trending", performance)
        
        # 应用相关性调整
        adjusted_weights = correlation_analyzer.get_adjusted_weights(base_weights)
        
        # 融合信号
        final_signal, confidence = signal_fusion.fuse_signals(signals, adjusted_weights)
        
        if round_num % 3 == 0:  # 每3轮打印一次
            print(f"轮次 {round_num + 1}:")
            print(f"  原始信号: {signals}")
            print(f"  基础权重: {base_weights}")
            print(f"  调整权重: {adjusted_weights}")
            print(f"  最终信号: {final_signal:.3f}, 置信度: {confidence:.3f}")
            
            # 显示相关性信息
            redundancy_score, _ = correlation_analyzer.detect_redundancy()
            print(f"  冗余度: {redundancy_score:.3f}")
            print()
    
    return True

def test_complete_system_integration():
    """测试完整系统集成"""
    print("\n=== 测试完整系统集成 ===")
    
    # 初始化所有组件
    hull = HullMovingAverage(9)
    stc = SchaffTrendCycle(23, 50, 10)
    pattern_recognizer = CandlePatternRecognizer()
    wave_recognizer = WavePatternRecognizer()
    fuzzy_system = FuzzySystem()
    market_classifier = MarketRegimeClassifier()
    correlation_analyzer = ModuleCorrelationAnalyzer()
    threshold_optimizer = DynamicThresholdOptimizer()
    weight_allocator = AdaptiveWeightAllocator()
    signal_fusion = SignalFusionEngine()
    
    print("✓ 所有组件初始化成功")
    
    # 模拟价格数据
    prices = []
    base_price = 100.0
    for i in range(100):
        # 生成带趋势和噪声的价格
        trend = 0.01 * np.sin(i * 0.05)
        noise = np.random.normal(0, 0.5)
        price = base_price + trend + noise
        prices.append(price)
        base_price = price
    
    print(f"✓ 生成了 {len(prices)} 个价格数据点")
    
    # 逐步处理数据
    signals_generated = 0
    trades_executed = 0
    
    for i, price in enumerate(prices):
        # 更新技术指标
        hull.update(price)
        stc.update(price)
        market_classifier.update(price, 1000 + np.random.normal(0, 100))
        
        # 检查指标是否就绪
        if not (hull.is_ready() and stc.is_ready()):
            continue
        
        # 生成各模块信号
        hull_value, hull_prev = hull.get_hull()
        stc_value, stc_signal = stc.get_stc()
        
        signals = {
            'technical': 1.0 if hull_value > hull_prev else -1.0,
            'pattern': np.random.uniform(-0.5, 0.5),  # 简化的形态信号
            'wave': np.random.uniform(-0.3, 0.3),     # 简化的波段信号
            'fuzzy': np.random.uniform(-0.4, 0.4)     # 简化的模糊信号
        }
        
        # 更新相关性分析
        correlation_analyzer.update_signals(signals)
        
        # 更新市场状态和动态阈值
        regime, volatility, trend_strength = market_classifier.classify_regime()
        threshold_optimizer.update_market_state(volatility, trend_strength)
        
        # 更新权重
        performance = {'technical': 0.6, 'pattern': 0.5, 'wave': 0.7, 'fuzzy': 0.65}
        base_weights = weight_allocator.update_weights(regime, performance)
        adjusted_weights = correlation_analyzer.get_adjusted_weights(base_weights)
        
        # 融合信号
        final_signal, confidence = signal_fusion.fuse_signals(signals, adjusted_weights)
        signals_generated += 1
        
        # 使用动态阈值判断是否交易
        should_trade = threshold_optimizer.should_trade(abs(final_signal), confidence)
        if should_trade:
            trades_executed += 1
            
            # 模拟交易结果并更新性能
            trade_result = np.random.normal(0, 1)  # 随机交易结果
            threshold_optimizer.update_performance(trade_result, final_signal, confidence)
        
        # 每20步打印一次状态
        if i % 20 == 0 and i > 50:
            signal_threshold, confidence_threshold = threshold_optimizer.get_current_thresholds()
            redundancy_score, _ = correlation_analyzer.detect_redundancy()
            
            print(f"步骤 {i}: 价格={price:.2f}, 信号={final_signal:.3f}, 置信度={confidence:.3f}")
            print(f"  市场状态: {regime}, 波动率: {volatility:.4f}")
            print(f"  动态阈值: 信号={signal_threshold:.3f}, 置信度={confidence_threshold:.3f}")
            print(f"  冗余度: {redundancy_score:.3f}, 交易: {should_trade}")
            print()
    
    print(f"✓ 测试完成:")
    print(f"  处理了 {len(prices)} 个数据点")
    print(f"  生成了 {signals_generated} 个信号")
    print(f"  执行了 {trades_executed} 次交易")
    print(f"  交易频率: {trades_executed/signals_generated*100:.1f}%")
    
    return True

def main():
    """主测试函数"""
    print("Strategy1 优化测试开始")
    print("=" * 50)
    
    tests = [
        ("模块相关性分析器", test_module_correlation_analyzer),
        ("动态阈值优化器", test_dynamic_threshold_optimizer),
        ("信号融合与相关性集成", test_signal_fusion_with_correlation),
        ("完整系统集成", test_complete_system_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n开始测试: {test_name}")
            result = test_func()
            if result:
                print(f"✓ {test_name} 测试通过")
                passed += 1
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Strategy1优化成功！")
        print("\n优化特性验证:")
        print("✓ 去除了river依赖库")
        print("✓ 实现了多模块融合信号体系")
        print("✓ 添加了自适应权重系统")
        print("✓ 集成了置信度评估")
        print("✓ 实现了灵活动态阈值")
        print("✓ 增加了模块相关性分析")
        print("✓ 实现了阈值参数动态优化")
        print("✓ 根据市场波动率自动缩放阈值")
        return True
    else:
        print("❌ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
