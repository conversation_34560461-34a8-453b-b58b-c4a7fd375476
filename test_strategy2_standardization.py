#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 Strategy2.py 规范化后的代码标准
"""

import sys
import os
import re

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'pyStrategy'))

def test_file_header():
    """测试文件头部格式"""
    try:
        with open('pyStrategy/self_strategy/Strategy2.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查编码声明
        assert '# encoding: UTF-8' in content, "缺少编码声明"
        
        # 检查文档字符串
        assert '"""' in content, "缺少文档字符串"
        assert 'HULL+STC策略' in content, "缺少策略描述"
        assert 'last update:' in content, "缺少更新时间"
        
        print("✓ 文件头部格式符合标准")
        return True
    except Exception as e:
        print(f"✗ 文件头部格式测试失败: {e}")
        return False

def test_imports():
    """测试导入语句格式"""
    try:
        with open('pyStrategy/self_strategy/Strategy2.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 检查导入语句
        import_lines = [line.strip() for line in lines if line.strip().startswith('from') or line.strip().startswith('import')]
        
        # 检查标准导入
        assert any('from pythongo.base import' in line for line in import_lines), "缺少 pythongo.base 导入"
        assert any('from pythongo.classdef import' in line for line in import_lines), "缺少 pythongo.classdef 导入"
        assert any('from pythongo.ui import' in line for line in import_lines), "缺少 pythongo.ui 导入"
        assert any('from pythongo.utils import' in line for line in import_lines), "缺少 pythongo.utils 导入"
        assert any('import numpy as np' in line for line in import_lines), "缺少 numpy 导入"
        assert any('from typing import' in line for line in import_lines), "缺少 typing 导入"
        
        print("✓ 导入语句格式符合标准")
        return True
    except Exception as e:
        print(f"✗ 导入语句测试失败: {e}")
        return False

def test_class_structure():
    """测试类结构"""
    try:
        from self_strategy.Strategy2 import Strategy2, Params, State
        
        # 测试 Params 类
        params = Params()
        assert hasattr(params, 'exchange'), "Params 应该有 exchange 属性"
        assert hasattr(params, 'instrument_id'), "Params 应该有 instrument_id 属性"
        assert hasattr(params, 'hull_period'), "Params 应该有 hull_period 属性"
        assert hasattr(params, '__iter__'), "Params 应该有 __iter__ 方法"
        assert hasattr(params, 'model_dump'), "Params 应该有 model_dump 方法"
        assert hasattr(params, 'model_fields'), "Params 应该有 model_fields 属性"
        
        # 测试 State 类
        state = State()
        assert hasattr(state, 'hull_ma'), "State 应该有 hull_ma 属性"
        assert hasattr(state, 'stc_value'), "State 应该有 stc_value 属性"
        assert hasattr(state, 'trend_strength'), "State 应该有 trend_strength 属性"
        assert hasattr(state, '__iter__'), "State 应该有 __iter__ 方法"
        assert hasattr(state, 'model_dump'), "State 应该有 model_dump 方法"
        assert hasattr(state, 'model_fields'), "State 应该有 model_fields 属性"
        
        # 测试 Strategy2 类
        strategy = Strategy2()
        assert hasattr(strategy, 'params_map'), "Strategy2 应该有 params_map 属性"
        assert hasattr(strategy, 'state_map'), "Strategy2 应该有 state_map 属性"
        assert hasattr(strategy, 'buy_signal'), "Strategy2 应该有 buy_signal 属性"
        assert hasattr(strategy, 'sell_signal'), "Strategy2 应该有 sell_signal 属性"
        
        print("✓ 类结构符合标准")
        return True
    except Exception as e:
        print(f"✗ 类结构测试失败: {e}")
        return False

def test_method_documentation():
    """测试方法文档"""
    try:
        from self_strategy.Strategy2 import Strategy2
        
        strategy = Strategy2()
        
        # 检查关键方法的文档字符串
        key_methods = [
            'on_tick', 'on_order_cancel', 'on_trade', 'on_start', 'on_stop',
            'callback', 'real_time_callback', 'calc_trend', 'calc_wma',
            'calc_hull_ma', 'calc_ema', 'calc_true_range', 'calc_atr',
            'calc_adaptive_volatility', 'calc_stochastic_k', 'calc_stc'
        ]
        
        for method_name in key_methods:
            method = getattr(strategy, method_name)
            assert method.__doc__ is not None, f"方法 {method_name} 缺少文档字符串"
            assert len(method.__doc__.strip()) > 0, f"方法 {method_name} 文档字符串为空"
        
        print("✓ 方法文档符合标准")
        return True
    except Exception as e:
        print(f"✗ 方法文档测试失败: {e}")
        return False

def test_property_methods():
    """测试属性方法"""
    try:
        from self_strategy.Strategy2 import Strategy2
        
        strategy = Strategy2()
        
        # 检查属性方法
        assert hasattr(strategy, 'main_indicator_data'), "应该有 main_indicator_data 属性"
        assert hasattr(strategy, 'sub_indicator_data'), "应该有 sub_indicator_data 属性"
        
        # 检查属性方法返回类型
        main_data = strategy.main_indicator_data
        sub_data = strategy.sub_indicator_data
        
        assert isinstance(main_data, dict), "main_indicator_data 应该返回字典"
        assert isinstance(sub_data, dict), "sub_indicator_data 应该返回字典"
        
        print("✓ 属性方法符合标准")
        return True
    except Exception as e:
        print(f"✗ 属性方法测试失败: {e}")
        return False

def test_field_definitions():
    """测试字段定义"""
    try:
        from self_strategy.Strategy2 import Params, State
        
        # 检查 Params 字段定义
        params = Params()
        param_fields = [
            'exchange', 'instrument_id', 'kline_style', 'mode', 'price_type',
            'order_volume', 'max_positions', 'hull_period', 'stc_fast', 'stc_slow',
            'stc_cycle', 'stop_mult', 'profit_mult', 'trail_step'
        ]
        
        for field in param_fields:
            assert hasattr(params, field), f"Params 缺少字段: {field}"
        
        # 检查 State 字段定义
        state = State()
        state_fields = [
            'hull_ma', 'hull_signal', 'hull_price_relation', 'stc_value',
            'stc_signal', 'stc_trend', 'trend_type', 'is_trending',
            'trend_strength', 'trend_duration', 'volatility'
        ]
        
        for field in state_fields:
            assert hasattr(state, field), f"State 缺少字段: {field}"
        
        print("✓ 字段定义符合标准")
        return True
    except Exception as e:
        print(f"✗ 字段定义测试失败: {e}")
        return False

def test_code_style():
    """测试代码风格"""
    try:
        with open('pyStrategy/self_strategy/Strategy2.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查类定义格式
        class_pattern = r'class\s+\w+\([^)]+\):'
        classes = re.findall(class_pattern, content)
        assert len(classes) >= 3, "应该有至少3个类定义"
        
        # 检查方法定义格式
        method_pattern = r'def\s+\w+\([^)]*\):'
        methods = re.findall(method_pattern, content)
        assert len(methods) >= 20, "应该有足够的方法定义"
        
        # 检查文档字符串格式
        docstring_pattern = r'"""[^"]*"""'
        docstrings = re.findall(docstring_pattern, content)
        assert len(docstrings) >= 5, "应该有足够的文档字符串"
        
        print("✓ 代码风格符合标准")
        return True
    except Exception as e:
        print(f"✗ 代码风格测试失败: {e}")
        return False

def test_platform_compatibility():
    """测试平台兼容性"""
    try:
        from self_strategy.Strategy2 import Strategy2
        
        strategy = Strategy2()
        
        # 检查平台必需的方法
        required_methods = [
            'on_tick', 'on_order_cancel', 'on_trade', 'on_start', 'on_stop',
            'callback', 'real_time_callback'
        ]
        
        for method_name in required_methods:
            assert hasattr(strategy, method_name), f"缺少必需方法: {method_name}"
            method = getattr(strategy, method_name)
            assert callable(method), f"方法 {method_name} 不可调用"
        
        # 检查平台必需的属性
        required_attrs = ['params_map', 'state_map', 'trading']
        
        for attr_name in required_attrs:
            assert hasattr(strategy, attr_name), f"缺少必需属性: {attr_name}"
        
        print("✓ 平台兼容性符合标准")
        return True
    except Exception as e:
        print(f"✗ 平台兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试 Strategy2.py 规范化后的代码标准...")
    print("=" * 60)
    
    tests = [
        ("文件头部格式测试", test_file_header),
        ("导入语句格式测试", test_imports),
        ("类结构测试", test_class_structure),
        ("方法文档测试", test_method_documentation),
        ("属性方法测试", test_property_methods),
        ("字段定义测试", test_field_definitions),
        ("代码风格测试", test_code_style),
        ("平台兼容性测试", test_platform_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Strategy2.py 已符合交易平台代码标准")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 