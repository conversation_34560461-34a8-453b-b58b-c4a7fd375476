#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Strategy6.py 修复验证测试脚本
测试策略是否能正常运行，解决假死问题
"""

import sys
import os
import numpy as np
from datetime import datetime
import time

# 添加策略路径
current_dir = os.path.dirname(__file__)
sys.path.append(os.path.join(current_dir, 'pyStrategy', 'self_strategy'))
sys.path.append(os.path.join(current_dir, 'pyStrategy'))

def test_strategy6_import():
    """测试Strategy6导入"""
    print("=" * 60)
    print("🔍 测试Strategy6导入...")
    
    try:
        from Strategy6 import Strategy6, Params, State, SimpleFuzzySystem, SimpleControlCenter
        print("✅ Strategy6导入成功")
        return True
    except ImportError as e:
        print(f"❌ Strategy6导入失败: {e}")
        return False

def test_fuzzy_system():
    """测试简化的模糊系统"""
    print("\n" + "=" * 60)
    print("🧠 测试简化模糊系统...")
    
    try:
        from Strategy6 import SimpleFuzzySystem, SimpleControlCenter
        
        # 创建控制中心
        control_center = SimpleControlCenter()
        
        # 测试不同的输入组合
        test_cases = [
            (0.02, 0.1),   # 低波动率，弱上涨
            (0.15, -0.3),  # 高波动率，下跌
            (0.05, 0.0),   # 中等波动率，中性
            (0.25, 0.8),   # 极高波动率，强上涨
        ]
        
        for volatility, trend in test_cases:
            action, confidence = control_center.make_decision(volatility, trend)
            print(f"  输入: 波动率={volatility:.3f}, 趋势={trend:.3f} -> 行动={action}, 置信度={confidence:.3f}")
        
        print("✅ 模糊系统测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 模糊系统测试失败: {e}")
        return False

def test_strategy_initialization():
    """测试策略初始化"""
    print("\n" + "=" * 60)
    print("🚀 测试策略初始化...")
    
    try:
        from Strategy6 import Strategy6
        
        # 创建策略实例
        strategy = Strategy6()
        
        # 检查基本属性
        assert hasattr(strategy, 'params_map'), "缺少params_map属性"
        assert hasattr(strategy, 'state_map'), "缺少state_map属性"
        assert hasattr(strategy, 'control_center'), "缺少control_center属性"
        
        # 检查初始状态
        assert strategy.state_map.stc_value == 50.0, "STC初始值错误"
        assert strategy.state_map.stc_signal == 50.0, "STC信号初始值错误"
        assert strategy.state_map.fuzzy_action == "Normal", "模糊行动初始值错误"
        assert strategy.state_map.fuzzy_confidence == 0.5, "模糊置信度初始值错误"
        
        # 检查信号状态
        assert strategy.buy_signal == False, "买入信号初始状态错误"
        assert strategy.sell_signal == False, "卖出信号初始状态错误"
        
        print("✅ 策略初始化测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 策略初始化测试失败: {e}")
        return False

def test_indicator_calculation():
    """测试技术指标计算"""
    print("\n" + "=" * 60)
    print("📊 测试技术指标计算...")
    
    try:
        from Strategy6 import Strategy6
        
        strategy = Strategy6()
        
        # 生成测试价格数据
        np.random.seed(42)
        test_prices = []
        base_price = 100.0
        
        for i in range(50):
            # 生成带趋势的价格数据
            trend = i * 0.1
            noise = np.random.normal(0, 0.5)
            price = base_price + trend + noise
            test_prices.append(price)
        
        prices_array = np.array(test_prices)
        
        # 测试EMA计算
        ema_12 = strategy.calculate_ema(prices_array, 12)
        ema_26 = strategy.calculate_ema(prices_array, 26)
        
        assert len(ema_12) == len(prices_array), "EMA12长度错误"
        assert len(ema_26) == len(prices_array), "EMA26长度错误"
        assert ema_12[-1] > 0, "EMA12值异常"
        assert ema_26[-1] > 0, "EMA26值异常"
        
        # 测试WMA计算
        wma_7 = strategy.calculate_wma(prices_array, 7)
        wma_14 = strategy.calculate_wma(prices_array, 14)
        
        assert len(wma_7) > 0, "WMA7计算失败"
        assert len(wma_14) > 0, "WMA14计算失败"
        
        # 测试STC指标计算
        strategy.calc_stc_indicator(prices_array)
        assert 0 <= strategy.state_map.stc_value <= 100, f"STC值异常: {strategy.state_map.stc_value}"
        
        # 测试HULL指标计算
        strategy.calc_hull_indicator(prices_array)
        assert strategy.state_map.hull_value > 0, f"HULL值异常: {strategy.state_map.hull_value}"
        
        print(f"  STC值: {strategy.state_map.stc_value:.2f}")
        print(f"  STC信号: {strategy.state_map.stc_signal:.2f}")
        print(f"  HULL值: {strategy.state_map.hull_value:.2f}")
        print(f"  HULL前值: {strategy.state_map.hull_prev:.2f}")
        
        print("✅ 技术指标计算测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 技术指标计算测试失败: {e}")
        return False

def test_signal_generation():
    """测试信号生成"""
    print("\n" + "=" * 60)
    print("📡 测试信号生成...")
    
    try:
        from Strategy6 import Strategy6
        
        strategy = Strategy6()
        
        # 设置测试状态
        strategy.state_map.filtered_price = 100.0
        strategy.state_map.stc_value = 30.0  # 超卖区域
        strategy.state_map.stc_signal = 25.0
        strategy.state_map.hull_value = 100.5
        strategy.state_map.hull_prev = 100.0
        strategy.state_map.fuzzy_action = "Aggressive"
        strategy.state_map.fuzzy_confidence = 0.8
        
        # 重置信号
        strategy.buy_signal = False
        strategy.sell_signal = False
        strategy.cover_signal = False
        strategy.short_signal = False
        
        # 执行信号计算
        strategy.calc_stc_hull_signal()
        
        # 检查是否生成了信号
        signal_generated = (strategy.buy_signal or strategy.sell_signal or 
                          strategy.cover_signal or strategy.short_signal)
        
        print(f"  买入信号: {strategy.buy_signal}")
        print(f"  卖出信号: {strategy.sell_signal}")
        print(f"  平多信号: {strategy.cover_signal}")
        print(f"  平空信号: {strategy.short_signal}")
        print(f"  信号生成: {signal_generated}")
        
        # 测试不同的市场条件
        test_conditions = [
            {"stc": 70, "action": "Conservative", "confidence": 0.6},
            {"stc": 20, "action": "Aggressive", "confidence": 0.9},
            {"stc": 50, "action": "Normal", "confidence": 0.5},
        ]
        
        for condition in test_conditions:
            strategy.state_map.stc_value = condition["stc"]
            strategy.state_map.fuzzy_action = condition["action"]
            strategy.state_map.fuzzy_confidence = condition["confidence"]
            
            # 重置信号
            strategy.buy_signal = False
            strategy.sell_signal = False
            strategy.cover_signal = False
            strategy.short_signal = False
            
            strategy.calc_stc_hull_signal()
            
            signal_count = sum([strategy.buy_signal, strategy.sell_signal, 
                              strategy.cover_signal, strategy.short_signal])
            
            print(f"  条件 STC={condition['stc']}, 行动={condition['action']} -> 信号数量={signal_count}")
        
        print("✅ 信号生成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 信号生成测试失败: {e}")
        return False

def test_market_indicators():
    """测试市场指标计算"""
    print("\n" + "=" * 60)
    print("📈 测试市场指标计算...")
    
    try:
        from Strategy6 import Strategy6
        
        strategy = Strategy6()
        
        # 添加测试价格历史
        test_prices = [100 + i * 0.1 + np.random.normal(0, 0.2) for i in range(20)]
        strategy.price_history.extend(test_prices)
        
        # 计算市场指标
        strategy.calc_market_indicators()
        
        # 检查指标值
        assert 0 <= strategy.state_map.volatility_index <= 1, f"波动率指数异常: {strategy.state_map.volatility_index}"
        assert -1 <= strategy.state_map.trend_strength <= 1, f"趋势强度异常: {strategy.state_map.trend_strength}"
        assert strategy.state_map.liquidity_index > 0, f"流动性指数异常: {strategy.state_map.liquidity_index}"
        assert 0 <= strategy.state_map.system_stability <= 1, f"系统稳定性异常: {strategy.state_map.system_stability}"
        
        print(f"  波动率指数: {strategy.state_map.volatility_index:.4f}")
        print(f"  趋势强度: {strategy.state_map.trend_strength:.4f}")
        print(f"  流动性指数: {strategy.state_map.liquidity_index:.2f}")
        print(f"  系统稳定性: {strategy.state_map.system_stability:.4f}")
        
        print("✅ 市场指标计算测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 市场指标计算测试失败: {e}")
        return False

def test_fuzzy_decision():
    """测试模糊决策执行"""
    print("\n" + "=" * 60)
    print("🎯 测试模糊决策执行...")
    
    try:
        from Strategy6 import Strategy6
        
        strategy = Strategy6()
        
        # 添加测试数据
        test_prices = [100 + i * 0.05 for i in range(15)]
        strategy.price_history.extend(test_prices)
        
        # 执行模糊决策
        strategy.execute_fuzzy_decision()
        
        # 检查决策结果
        valid_actions = ["Stop", "Conservative", "Normal", "Aggressive"]
        assert strategy.state_map.fuzzy_action in valid_actions, f"无效的模糊行动: {strategy.state_map.fuzzy_action}"
        assert 0 <= strategy.state_map.fuzzy_confidence <= 1, f"模糊置信度异常: {strategy.state_map.fuzzy_confidence}"
        
        print(f"  模糊行动: {strategy.state_map.fuzzy_action}")
        print(f"  模糊置信度: {strategy.state_map.fuzzy_confidence:.3f}")
        
        # 测试多次决策
        for i in range(5):
            # 修改市场条件
            strategy.state_map.volatility_index = 0.02 + i * 0.03
            strategy.state_map.trend_strength = -0.5 + i * 0.25
            
            strategy.execute_fuzzy_decision()
            print(f"  测试{i+1}: 行动={strategy.state_map.fuzzy_action}, 置信度={strategy.state_map.fuzzy_confidence:.3f}")
        
        print("✅ 模糊决策执行测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 模糊决策执行测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 Strategy6.py 修复验证测试")
    print("=" * 60)
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("导入测试", test_strategy6_import),
        ("模糊系统测试", test_fuzzy_system),
        ("策略初始化测试", test_strategy_initialization),
        ("技术指标计算测试", test_indicator_calculation),
        ("信号生成测试", test_signal_generation),
        ("市场指标测试", test_market_indicators),
        ("模糊决策测试", test_fuzzy_decision),
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📋 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Strategy6.py修复成功！")
        print("\n📝 修复要点:")
        print("  • 简化了模糊系统，避免过度复杂化")
        print("  • 修复了技术指标计算逻辑")
        print("  • 优化了信号生成机制")
        print("  • 增强了错误处理和异常恢复")
        print("  • 确保状态更新机制正常工作")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    main() 