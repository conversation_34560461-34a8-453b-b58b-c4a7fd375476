#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OptionStrategy2 修复验证测试
测试修复后的策略是否能正常运行
"""

import sys
import time
import numpy as np
from dataclasses import dataclass

# 添加路径
sys.path.append('pyStrategy')

# 模拟数据类
@dataclass
class MockKLineData:
    """模拟K线数据"""
    open: float
    high: float
    low: float
    close: float
    volume: float
    timestamp: float

@dataclass
class MockTickData:
    """模拟Tick数据"""
    last_price: float
    volume: float
    bid_price_1: float = 0.0
    ask_price_1: float = 0.0

def test_strategy_initialization():
    """测试策略初始化"""
    print("=" * 50)
    print("测试1: 策略初始化")
    print("=" * 50)
    
    try:
        from self_strategy.OptionStrategy2 import OptionStrategy2
        
        # 创建策略实例
        strategy = OptionStrategy2()
        print("✅ 策略实例创建成功")
        
        # 检查关键组件
        assert hasattr(strategy, 'unified_indicator_engine'), "缺少技术指标引擎"
        assert hasattr(strategy, 'control_core'), "缺少控制理论核心"
        assert hasattr(strategy, 'position_manager'), "缺少仓位管理器"
        print("✅ 关键组件检查通过")
        
        # 检查状态映射
        assert hasattr(strategy, 'state_map'), "缺少状态映射"
        assert hasattr(strategy, 'params_map'), "缺少参数映射"
        print("✅ 状态和参数映射检查通过")
        
        # 检查状态属性
        state_attrs = [
            'hull_fast', 'hull_slow', 'hull_signal',
            'stc', 'stc_signal', 'stc_histogram',
            'system_stability', 'signal_strength', 'trade_confidence',
            'last_error_message', 'engine_running'
        ]
        
        for attr in state_attrs:
            assert hasattr(strategy.state_map, attr), f"缺少状态属性: {attr}"
        print("✅ 状态属性检查通过")
        
        # 检查参数属性
        param_attrs = [
            'exchange', 'instrument_id', 'kline_style',
            'hull_fast_period', 'hull_slow_period', 'hull_signal_period',
            'stc_length', 'stc_fast_ma', 'stc_slow_ma'
        ]
        
        for attr in param_attrs:
            assert hasattr(strategy.params_map, attr), f"缺少参数属性: {attr}"
        print("✅ 参数属性检查通过")
        
        return strategy
        
    except Exception as e:
        print(f"❌ 策略初始化失败: {str(e)}")
        raise

def test_indicator_calculation(strategy):
    """测试技术指标计算"""
    print("\n" + "=" * 50)
    print("测试2: 技术指标计算")
    print("=" * 50)
    
    try:
        # 生成模拟K线数据
        base_price = 3000.0
        klines = []
        
        for i in range(100):
            # 生成随机价格变动
            price_change = np.random.normal(0, 10)
            current_price = base_price + price_change
            
            kline = MockKLineData(
                open=current_price - 2,
                high=current_price + 5,
                low=current_price - 5,
                close=current_price,
                volume=1000 + np.random.randint(0, 500),
                timestamp=time.time() + i
            )
            klines.append(kline)
            
            # 更新指标引擎
            strategy.unified_indicator_engine.update_data(kline)
        
        print(f"✅ 生成了 {len(klines)} 条模拟K线数据")
        
        # 计算技术指标
        indicators = strategy.unified_indicator_engine.calculate_all_indicators()
        
        # 验证指标结果
        required_indicators = [
            'hull_fast', 'hull_slow', 'hull_signal',
            'stc', 'stc_signal', 'stc_histogram',
            'atr', 'volatility'
        ]
        
        for indicator in required_indicators:
            assert indicator in indicators, f"缺少指标: {indicator}"
            assert isinstance(indicators[indicator], (int, float)), f"指标 {indicator} 类型错误"
        
        print("✅ 技术指标计算成功")
        print(f"   HULL快线: {indicators['hull_fast']:.2f}")
        print(f"   HULL慢线: {indicators['hull_slow']:.2f}")
        print(f"   HULL信号线: {indicators['hull_signal']:.2f}")
        print(f"   STC: {indicators['stc']:.2f}")
        print(f"   STC信号线: {indicators['stc_signal']:.2f}")
        print(f"   波动率: {indicators['volatility']:.4f}")
        
        return indicators
        
    except Exception as e:
        print(f"❌ 技术指标计算失败: {str(e)}")
        raise

def test_control_theory_analysis(strategy, indicators):
    """测试控制理论分析"""
    print("\n" + "=" * 50)
    print("测试3: 控制理论分析")
    print("=" * 50)
    
    try:
        # 构建信号数据
        signal_data = {
            'hull_fast': indicators['hull_fast'],
            'hull_slow': indicators['hull_slow'],
            'hull_signal': indicators['hull_signal'],
            'stc': indicators['stc'],
            'stc_signal': indicators['stc_signal'],
            'price': 3000.0,
            'volatility': indicators['volatility'],
            'volume': 1000.0
        }
        
        # 控制理论处理
        control_result = strategy.control_core.process_control_signal(signal_data)
        
        # 验证控制结果
        required_fields = [
            'signal_strength', 'stability_index', 'trade_confidence',
            'should_trade', 'trade_direction', 'position_size'
        ]
        
        for field in required_fields:
            assert field in control_result, f"缺少控制结果字段: {field}"
        
        print("✅ 控制理论分析成功")
        print(f"   信号强度: {control_result['signal_strength']:.3f}")
        print(f"   稳定性指数: {control_result['stability_index']:.3f}")
        print(f"   交易置信度: {control_result['trade_confidence']:.3f}")
        print(f"   是否交易: {control_result['should_trade']}")
        print(f"   交易方向: {control_result['trade_direction']}")
        print(f"   仓位大小: {control_result['position_size']}")
        
        return control_result
        
    except Exception as e:
        print(f"❌ 控制理论分析失败: {str(e)}")
        raise

def test_state_management(strategy, indicators, control_result):
    """测试状态管理"""
    print("\n" + "=" * 50)
    print("测试4: 状态管理")
    print("=" * 50)
    
    try:
        # 模拟K线数据
        kline = MockKLineData(
            open=2995.0,
            high=3005.0,
            low=2990.0,
            close=3000.0,
            volume=1200,
            timestamp=time.time()
        )
        
        # 热更新状态
        strategy._hot_update_states(indicators, kline)
        
        # 验证状态更新
        assert strategy.state_map.hull_fast == indicators['hull_fast']
        assert strategy.state_map.hull_slow == indicators['hull_slow']
        assert strategy.state_map.stc == indicators['stc']
        assert strategy.state_map.volatility == indicators['volatility']
        
        print("✅ 状态热更新成功")
        
        # 更新控制理论状态
        strategy.state_map.system_stability = control_result['stability_index']
        strategy.state_map.signal_strength = control_result['signal_strength']
        strategy.state_map.trade_confidence = control_result['trade_confidence']
        
        print("✅ 控制理论状态更新成功")
        
        # 检查状态一致性
        assert 0 <= strategy.state_map.system_stability <= 1, "系统稳定性超出范围"
        assert 0 <= strategy.state_map.signal_strength <= 1, "信号强度超出范围"
        assert 0 <= strategy.state_map.trade_confidence <= 1, "交易置信度超出范围"
        
        print("✅ 状态一致性检查通过")
        
        # 显示当前状态
        print(f"   HULL趋势: {strategy.state_map.hull_trend}")
        print(f"   STC趋势: {strategy.state_map.stc_trend}")
        print(f"   系统稳定性: {strategy.state_map.system_stability:.3f}")
        print(f"   信号强度: {strategy.state_map.signal_strength:.3f}")
        print(f"   交易置信度: {strategy.state_map.trade_confidence:.3f}")
        
    except Exception as e:
        print(f"❌ 状态管理测试失败: {str(e)}")
        raise

def test_position_management(strategy):
    """测试仓位管理"""
    print("\n" + "=" * 50)
    print("测试5: 仓位管理")
    print("=" * 50)
    
    try:
        # 测试开仓
        strategy.position_manager.open_position(1, 2, 3000.0)
        assert strategy.position_manager.has_position(), "开仓后应该有持仓"
        
        position = strategy.position_manager.get_current_position()
        assert position['direction'] == 1, "持仓方向错误"
        assert position['volume'] == 2, "持仓数量错误"
        assert position['entry_price'] == 3000.0, "入场价格错误"
        
        print("✅ 开仓功能正常")
        
        # 测试平仓
        strategy.position_manager.close_position(3010.0, "测试平仓")
        assert not strategy.position_manager.has_position(), "平仓后不应该有持仓"
        assert len(strategy.position_manager.position_history) == 1, "历史记录应该有1条"
        
        print("✅ 平仓功能正常")
        print(f"   历史持仓记录: {len(strategy.position_manager.position_history)} 条")
        
    except Exception as e:
        print(f"❌ 仓位管理测试失败: {str(e)}")
        raise

def test_ui_data_interface(strategy):
    """测试UI数据接口"""
    print("\n" + "=" * 50)
    print("测试6: UI数据接口")
    print("=" * 50)
    
    try:
        # 测试主图指标数据
        main_data = strategy.main_indicator_data
        assert isinstance(main_data, dict), "主图数据应该是字典"
        assert 'HULL_FAST' in main_data, "缺少HULL_FAST"
        assert 'HULL_SLOW' in main_data, "缺少HULL_SLOW"
        assert 'HULL_SIGNAL' in main_data, "缺少HULL_SIGNAL"
        
        print("✅ 主图指标数据接口正常")
        
        # 测试副图指标数据
        sub_data = strategy.sub_indicator_data
        assert isinstance(sub_data, dict), "副图数据应该是字典"
        assert 'STC' in sub_data, "缺少STC"
        assert 'STC_SIGNAL' in sub_data, "缺少STC_SIGNAL"
        assert 'SYSTEM_STABILITY' in sub_data, "缺少SYSTEM_STABILITY"
        
        print("✅ 副图指标数据接口正常")
        
        # 测试指标名称列表
        main_indicators = strategy.main_indicator
        sub_indicators = strategy.sub_indicator
        
        assert isinstance(main_indicators, list), "主图指标列表应该是列表"
        assert isinstance(sub_indicators, list), "副图指标列表应该是列表"
        
        print("✅ 指标名称列表接口正常")
        print(f"   主图指标: {main_indicators}")
        print(f"   副图指标: {sub_indicators}")
        
    except Exception as e:
        print(f"❌ UI数据接口测试失败: {str(e)}")
        raise

def main():
    """主测试函数"""
    print("OptionStrategy2 修复验证测试")
    print("测试时间:", time.strftime('%Y-%m-%d %H:%M:%S'))
    
    try:
        # 测试1: 策略初始化
        strategy = test_strategy_initialization()
        
        # 测试2: 技术指标计算
        indicators = test_indicator_calculation(strategy)
        
        # 测试3: 控制理论分析
        control_result = test_control_theory_analysis(strategy, indicators)
        
        # 测试4: 状态管理
        test_state_management(strategy, indicators, control_result)
        
        # 测试5: 仓位管理
        test_position_management(strategy)
        
        # 测试6: UI数据接口
        test_ui_data_interface(strategy)
        
        # 总结
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！")
        print("=" * 50)
        print("✅ 策略初始化正常")
        print("✅ 技术指标计算正常")
        print("✅ 控制理论分析正常")
        print("✅ 状态管理正常")
        print("✅ 仓位管理正常")
        print("✅ UI数据接口正常")
        print("\n🚀 OptionStrategy2 策略修复成功，可以正常使用！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 