#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流式Strategy6.py 验证测试脚本
测试保持丰富模糊理论功能的流式工作机制
"""

import sys
import os
import numpy as np
from datetime import datetime
import time

# 添加策略路径
current_dir = os.path.dirname(__file__)
sys.path.append(os.path.join(current_dir, 'pyStrategy', 'self_strategy'))
sys.path.append(os.path.join(current_dir, 'pyStrategy'))

def test_streaming_strategy6_import():
    """测试流式Strategy6导入"""
    print("=" * 60)
    print("🔍 测试流式Strategy6导入...")
    
    try:
        from Strategy6 import (Strategy6, Params, State, 
                              AdvancedIntuitiveTrapezoidalFuzzyNumber,
                              StreamingHybridFuzzySystem, 
                              StreamingControlCenter,
                              WorkflowState)
        print("✅ 流式Strategy6导入成功")
        print(f"  - 工作流状态枚举: {[state.value for state in WorkflowState]}")
        return True
    except ImportError as e:
        print(f"❌ 流式Strategy6导入失败: {e}")
        return False

def test_advanced_fuzzy_number():
    """测试高级直觉梯形模糊数"""
    print("\n" + "=" * 60)
    print("🧮 测试高级直觉梯形模糊数...")
    
    try:
        from Strategy6 import AdvancedIntuitiveTrapezoidalFuzzyNumber
        
        # 创建模糊数
        fuzzy_num = AdvancedIntuitiveTrapezoidalFuzzyNumber(
            a=0.0, b=0.3, c=0.7, d=1.0, mu=0.8, nu=0.1
        )
        
        # 测试隶属度计算
        test_values = [0.0, 0.2, 0.5, 0.8, 1.0]
        for val in test_values:
            membership = fuzzy_num.membership(val)
            non_membership = fuzzy_num.non_membership(val)
            hesitation = fuzzy_num.hesitation(val)
            print(f"  x={val}: μ={membership:.3f}, ν={non_membership:.3f}, π={hesitation:.3f}")
        
        # 测试重心计算
        centroid = fuzzy_num.centroid_x_stream()
        expected_value = fuzzy_num.expected_value_stream()
        print(f"  重心: {centroid:.3f}, 期望值: {expected_value:.3f}")
        
        # 测试去模糊化
        defuzz_centroid = fuzzy_num.defuzzify_centroid()
        defuzz_mom = fuzzy_num.defuzzify_mom()
        print(f"  去模糊化 - 重心法: {defuzz_centroid:.3f}, 最大隶属度中值法: {defuzz_mom:.3f}")
        
        print("✅ 高级直觉梯形模糊数测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 高级直觉梯形模糊数测试失败: {e}")
        return False

def test_streaming_fuzzy_system():
    """测试流式混合模糊系统"""
    print("\n" + "=" * 60)
    print("🌊 测试流式混合模糊系统...")
    
    try:
        from Strategy6 import StreamingHybridFuzzySystem
        
        # 创建流式模糊系统
        fuzzy_system = StreamingHybridFuzzySystem()
        
        # 测试模糊集定义
        print(f"  波动率模糊集数量: {len(fuzzy_system.volatility_sets)}")
        print(f"  趋势模糊集数量: {len(fuzzy_system.trend_sets)}")
        print(f"  流动性模糊集数量: {len(fuzzy_system.liquidity_sets)}")
        print(f"  稳定性模糊集数量: {len(fuzzy_system.stability_sets)}")
        print(f"  盈利模糊集数量: {len(fuzzy_system.profit_sets)}")
        print(f"  规则数量: {len(fuzzy_system.primary_rules)}")
        
        # 测试模糊化处理
        test_cases = [
            (0.02, 0.1, 1.2, 0.8, 0.05),   # 低波动率，弱上涨，高流动性，高稳定性，小盈利
            (0.15, -0.3, 0.8, 0.4, -0.08), # 高波动率，下跌，中等流动性，中等稳定性，亏损
            (0.05, 0.0, 1.0, 0.6, 0.0),    # 中等波动率，中性，正常流动性，中等稳定性，无盈亏
        ]
        
        for i, (vol, trend, liq, stab, profit) in enumerate(test_cases):
            print(f"\n  测试案例 {i+1}: 波动率={vol}, 趋势={trend}, 流动性={liq}, 稳定性={stab}, 盈利={profit}")
            
            # 执行模糊化
            v_mem, t_mem, l_mem, s_mem, p_mem = fuzzy_system.fuzzify_streaming(vol, trend, liq, stab, profit)
            
            # 显示隶属度
            print(f"    波动率隶属度: {[(k, f'{v:.3f}') for k, v in v_mem.items() if v > 0.1]}")
            print(f"    趋势隶属度: {[(k, f'{v:.3f}') for k, v in t_mem.items() if v > 0.1]}")
            
            # 执行推理
            risk, action, confidence = fuzzy_system.streaming_inference(v_mem, t_mem, l_mem, s_mem, p_mem)
            print(f"    决策结果: 风险={risk}, 行动={action}, 置信度={confidence:.3f}")
        
        # 测试模糊熵计算
        entropy = fuzzy_system.calculate_fuzzy_entropy(v_mem)
        print(f"\n  模糊熵: {entropy:.3f}")
        
        print("✅ 流式混合模糊系统测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 流式混合模糊系统测试失败: {e}")
        return False

def test_streaming_control_center():
    """测试流式控制中心"""
    print("\n" + "=" * 60)
    print("🎛️ 测试流式控制中心...")
    
    try:
        from Strategy6 import Strategy6, StreamingControlCenter
        
        # 创建策略实例
        strategy = Strategy6()
        control_center = StreamingControlCenter(strategy)
        
        # 测试李雅普诺夫方程求解
        print(f"  A矩阵: \n{control_center.A_matrix}")
        print(f"  P矩阵: \n{control_center.P_matrix}")
        
        # 测试稳定性检查
        test_states = [
            np.array([0.1, 0.2]),
            np.array([0.5, -0.3]),
            np.array([-0.2, 0.4])
        ]
        
        for i, state in enumerate(test_states):
            stability = control_center.check_stability_streaming(state)
            print(f"  状态 {i+1} {state}: 稳定性={stability:.4f}")
        
        # 测试流式模糊决策
        decision_cases = [
            (0.03, 0.2, 1.5, 0.9, 0.08),   # 优质交易条件
            (0.2, -0.5, 0.6, 0.3, -0.12),  # 高风险条件
            (0.08, 0.0, 1.0, 0.7, 0.02),   # 中性条件
        ]
        
        for i, (vol, trend, liq, stab, profit) in enumerate(decision_cases):
            decision = control_center.streaming_fuzzy_decision(vol, trend, liq, stab, profit)
            print(f"  决策 {i+1}: 风险={decision[0]}, 行动={decision[1]}, 置信度={decision[2]:.3f}")
        
        print("✅ 流式控制中心测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 流式控制中心测试失败: {e}")
        return False

def test_strategy_initialization():
    """测试策略初始化"""
    print("\n" + "=" * 60)
    print("🚀 测试策略初始化...")
    
    try:
        from Strategy6 import Strategy6, WorkflowState
        
        # 创建策略实例
        strategy = Strategy6()
        
        # 检查基本属性
        assert hasattr(strategy, 'params_map'), "缺少params_map属性"
        assert hasattr(strategy, 'state_map'), "缺少state_map属性"
        assert hasattr(strategy, 'control_center'), "缺少control_center属性"
        assert hasattr(strategy, 'workflow_state'), "缺少workflow_state属性"
        
        # 检查初始状态
        assert strategy.workflow_state == WorkflowState.IDLE, "工作流状态初始值错误"
        assert strategy.state_map.fuzzy_risk == "RiskMedium", "模糊风险初始值错误"
        assert strategy.state_map.fuzzy_action == "Normal", "模糊行动初始值错误"
        assert strategy.state_map.fuzzy_confidence == 0.5, "模糊置信度初始值错误"
        
        # 检查性能监控
        assert "processing_times" in strategy.performance_metrics, "缺少处理时间监控"
        assert "error_count" in strategy.performance_metrics, "缺少错误计数监控"
        assert "recovery_count" in strategy.performance_metrics, "缺少恢复计数监控"
        
        # 测试状态信息获取
        state_info = strategy.get_full_state_info()
        assert "技术指标" in state_info, "缺少技术指标信息"
        assert "市场分析" in state_info, "缺少市场分析信息"
        assert "模糊决策" in state_info, "缺少模糊决策信息"
        assert "控制理论" in state_info, "缺少控制理论信息"
        assert "流式状态" in state_info, "缺少流式状态信息"
        
        print("  状态映射模型信息:")
        for category, info in state_info.items():
            print(f"    {category}: {len(info)}项指标")
        
        print("✅ 策略初始化测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 策略初始化测试失败: {e}")
        return False

def test_streaming_indicator_calculation():
    """测试流式技术指标计算"""
    print("\n" + "=" * 60)
    print("📊 测试流式技术指标计算...")
    
    try:
        from Strategy6 import Strategy6
        
        strategy = Strategy6()
        
        # 生成测试价格数据
        np.random.seed(42)
        test_prices = []
        base_price = 100.0
        
        for i in range(60):
            # 生成带趋势和噪声的价格数据
            trend = i * 0.05
            cycle = 5 * np.sin(i * 0.1)
            noise = np.random.normal(0, 0.3)
            price = base_price + trend + cycle + noise
            test_prices.append(price)
        
        prices_array = np.array(test_prices)
        
        # 测试EMA计算
        ema_12 = strategy.calculate_ema_streaming(prices_array, 12)
        ema_26 = strategy.calculate_ema_streaming(prices_array, 26)
        
        assert len(ema_12) == len(prices_array), "EMA12长度错误"
        assert len(ema_26) == len(prices_array), "EMA26长度错误"
        assert ema_12[-1] > 0, "EMA12值异常"
        assert ema_26[-1] > 0, "EMA26值异常"
        
        print(f"  EMA12最新值: {ema_12[-1]:.2f}")
        print(f"  EMA26最新值: {ema_26[-1]:.2f}")
        
        # 测试WMA计算
        wma_7 = strategy.calculate_wma_streaming(prices_array, 7)
        wma_14 = strategy.calculate_wma_streaming(prices_array, 14)
        
        assert len(wma_7) > 0, "WMA7计算失败"
        assert len(wma_14) > 0, "WMA14计算失败"
        
        print(f"  WMA7最新值: {wma_7[-1]:.2f}")
        print(f"  WMA14最新值: {wma_14[-1]:.2f}")
        
        # 测试STC指标计算
        strategy.calc_stc_indicator_streaming(prices_array)
        print(f"  STC值: {strategy.state_map.stc_value:.2f}")
        print(f"  STC信号: {strategy.state_map.stc_signal:.2f}")
        
        # 测试HULL指标计算
        strategy.calc_hull_indicator_streaming(prices_array)
        print(f"  HULL值: {strategy.state_map.hull_value:.2f}")
        print(f"  HULL前值: {strategy.state_map.hull_prev:.2f}")
        
        # 测试信号质量计算
        strategy.state_map.filtered_price = prices_array[-1]
        strategy.calc_signal_quality_streaming()
        print(f"  信号质量: {strategy.state_map.signal_quality:.3f}")
        
        print("✅ 流式技术指标计算测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 流式技术指标计算测试失败: {e}")
        return False

def test_streaming_market_analysis():
    """测试流式市场分析"""
    print("\n" + "=" * 60)
    print("📈 测试流式市场分析...")
    
    try:
        from Strategy6 import Strategy6
        
        strategy = Strategy6()
        
        # 添加测试价格历史
        test_prices = []
        for i in range(50):
            price = 100 + i * 0.1 + np.random.normal(0, 0.2)
            test_prices.append(price)
        
        strategy.price_history.extend(test_prices)
        
        # 执行流式市场指标计算
        start_time = time.time()
        strategy.calc_market_indicators_streaming()
        processing_time = time.time() - start_time
        
        print(f"  处理时间: {processing_time*1000:.2f}ms")
        
        # 检查指标值
        assert 0 <= strategy.state_map.volatility_index <= 1, f"波动率指数异常: {strategy.state_map.volatility_index}"
        assert 0 <= strategy.state_map.trend_strength <= 1, f"趋势强度异常: {strategy.state_map.trend_strength}"
        assert strategy.state_map.liquidity_index > 0, f"流动性指数异常: {strategy.state_map.liquidity_index}"
        assert 0 <= strategy.state_map.system_stability <= 1, f"系统稳定性异常: {strategy.state_map.system_stability}"
        
        print(f"  波动率指数: {strategy.state_map.volatility_index:.4f}")
        print(f"  趋势强度: {strategy.state_map.trend_strength:.4f}")
        print(f"  流动性指数: {strategy.state_map.liquidity_index:.2f}")
        print(f"  系统稳定性: {strategy.state_map.system_stability:.4f}")
        
        print("✅ 流式市场分析测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 流式市场分析测试失败: {e}")
        return False

def test_streaming_fuzzy_decision():
    """测试流式模糊决策执行"""
    print("\n" + "=" * 60)
    print("🎯 测试流式模糊决策执行...")
    
    try:
        from Strategy6 import Strategy6, WorkflowState
        
        strategy = Strategy6()
        
        # 添加测试数据
        test_prices = [100 + i * 0.05 for i in range(30)]
        strategy.price_history.extend(test_prices)
        strategy.state_map.filtered_price = test_prices[-1]
        
        # 执行流式模糊决策
        start_time = time.time()
        strategy.execute_streaming_fuzzy_decision()
        processing_time = time.time() - start_time
        
        print(f"  处理时间: {processing_time*1000:.2f}ms")
        print(f"  工作流状态: {strategy.state_map.workflow_state}")
        
        # 检查决策结果
        valid_risks = ["RiskNone", "RiskLow", "RiskMedium", "RiskHigh", "RiskVeryHigh"]
        valid_actions = ["Stop", "Conservative", "Normal", "Aggressive", "VeryAggressive"]
        
        assert strategy.state_map.fuzzy_risk in valid_risks, f"无效的模糊风险: {strategy.state_map.fuzzy_risk}"
        assert strategy.state_map.fuzzy_action in valid_actions, f"无效的模糊行动: {strategy.state_map.fuzzy_action}"
        assert 0 <= strategy.state_map.fuzzy_confidence <= 1, f"模糊置信度异常: {strategy.state_map.fuzzy_confidence}"
        
        print(f"  模糊风险: {strategy.state_map.fuzzy_risk}")
        print(f"  模糊行动: {strategy.state_map.fuzzy_action}")
        print(f"  模糊置信度: {strategy.state_map.fuzzy_confidence:.3f}")
        
        # 测试多次决策的一致性
        decisions = []
        for i in range(5):
            strategy.execute_streaming_fuzzy_decision()
            decisions.append((strategy.state_map.fuzzy_risk, strategy.state_map.fuzzy_action, strategy.state_map.fuzzy_confidence))
            print(f"  决策{i+1}: 风险={decisions[-1][0]}, 行动={decisions[-1][1]}, 置信度={decisions[-1][2]:.3f}")
        
        # 检查性能指标
        print(f"  错误计数: {strategy.performance_metrics['error_count']}")
        print(f"  恢复计数: {strategy.performance_metrics['recovery_count']}")
        
        print("✅ 流式模糊决策执行测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 流式模糊决策执行测试失败: {e}")
        return False

def test_performance_monitoring():
    """测试性能监控"""
    print("\n" + "=" * 60)
    print("⚡ 测试性能监控...")
    
    try:
        from Strategy6 import Strategy6
        
        strategy = Strategy6()
        
        # 添加测试数据
        test_prices = [100 + i * 0.02 + np.random.normal(0, 0.1) for i in range(100)]
        strategy.price_history.extend(test_prices)
        
        # 执行多次处理，测试性能
        processing_times = []
        for i in range(10):
            start_time = time.time()
            
            # 执行完整的处理流程
            strategy.calc_market_indicators_streaming()
            strategy.execute_streaming_fuzzy_decision()
            
            processing_time = time.time() - start_time
            processing_times.append(processing_time)
        
        avg_time = np.mean(processing_times)
        max_time = np.max(processing_times)
        min_time = np.min(processing_times)
        
        print(f"  平均处理时间: {avg_time*1000:.2f}ms")
        print(f"  最大处理时间: {max_time*1000:.2f}ms")
        print(f"  最小处理时间: {min_time*1000:.2f}ms")
        print(f"  处理时间标准差: {np.std(processing_times)*1000:.2f}ms")
        
        # 检查超时保护
        assert max_time < 0.1, f"处理时间过长: {max_time*1000:.2f}ms"
        
        # 检查性能指标记录
        assert len(strategy.performance_metrics["processing_times"]) > 0, "处理时间记录为空"
        
        print(f"  记录的处理时间数量: {len(strategy.performance_metrics['processing_times'])}")
        print(f"  累计错误计数: {strategy.performance_metrics['error_count']}")
        print(f"  累计恢复计数: {strategy.performance_metrics['recovery_count']}")
        
        print("✅ 性能监控测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 性能监控测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🌊 流式Strategy6.py 验证测试")
    print("保持丰富模糊理论功能的流式工作机制测试")
    print("=" * 60)
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("导入测试", test_streaming_strategy6_import),
        ("高级模糊数测试", test_advanced_fuzzy_number),
        ("流式模糊系统测试", test_streaming_fuzzy_system),
        ("流式控制中心测试", test_streaming_control_center),
        ("策略初始化测试", test_strategy_initialization),
        ("流式技术指标测试", test_streaming_indicator_calculation),
        ("流式市场分析测试", test_streaming_market_analysis),
        ("流式模糊决策测试", test_streaming_fuzzy_decision),
        ("性能监控测试", test_performance_monitoring),
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📋 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！流式Strategy6.py验证成功！")
        print("\n📝 流式设计要点:")
        print("  • 保持了原有丰富的模糊理论功能")
        print("  • 采用状态机模式避免假死")
        print("  • 实现了超时保护机制")
        print("  • 支持异步处理和错误恢复")
        print("  • 提供了完整的性能监控")
        print("  • 确保了流式工作的稳定性")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步优化")
        return False

if __name__ == "__main__":
    main() 