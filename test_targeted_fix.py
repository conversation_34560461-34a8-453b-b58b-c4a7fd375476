#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OptionStrategy2 HULL+STC针对性修复验证脚本
专门测试HULL平滑化和STC绘制问题的修复效果
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
sys.path.append(os.path.join(os.path.dirname(__file__), 'pyStrategy'))

# 导入策略
try:
    from pyStrategy.self_strategy.OptionStrategy2 import OptionStrategy2, HullIndicator, STCIndicator
except ImportError:
    sys.path.append('pyStrategy/self_strategy')
    from OptionStrategy2 import OptionStrategy2, HullIndicator, STCIndicator

def test_hull_smoothing_effectiveness():
    """重点测试HULL平滑化是否真正实现"""
    print("🎯 重点测试HULL平滑化实现效果")
    print("=" * 70)
    
    # 生成高噪声测试数据
    np.random.seed(42)
    noisy_prices = []
    for i in range(100):
        base_trend = 100 + i * 0.1
        high_noise = np.random.normal(0, 2.0)  # 高噪声
        price = base_trend + high_noise
        noisy_prices.append(price)
    
    print("📊 测试数据: 高噪声趋势数据（噪声标准差=2.0）")
    
    # 测试原始HULL vs 不同平滑方式
    test_configs = [
        ("无平滑", 1, "EMA"),
        ("EMA平滑3", 3, "EMA"),
        ("EMA平滑5", 5, "EMA"),
        ("SMA平滑3", 3, "SMA"),
        ("SMA平滑5", 5, "SMA"),
        ("WMA平滑3", 3, "WMA"),
        ("WMA平滑5", 5, "WMA")
    ]
    
    results = {}
    
    for name, smooth_period, smooth_type in test_configs:
        hull = HullIndicator(f"test_{name}")
        values = []
        
        for i in range(21, len(noisy_prices)):
            price_slice = noisy_prices[:i+1]
            hull_value = hull.calculate(price_slice, 21, smooth_period, smooth_type)
            values.append(hull_value)
        
        # 计算平滑度指标
        if len(values) > 5:
            # 计算变化率的标准差（越小越平滑）
            changes = np.diff(values)
            volatility = np.std(changes)
            
            # 计算信噪比
            signal_strength = np.mean(np.abs(changes))
            noise_level = np.std(changes)
            snr = signal_strength / (noise_level + 1e-6)
            
            results[name] = {
                "volatility": volatility,
                "snr": snr,
                "last_values": values[-5:]
            }
            
            print(f"  • {name:12}: 波动率={volatility:.6f} | 信噪比={snr:.4f}")
            print(f"    最后5值: {[f'{v:.4f}' for v in values[-5:]]}")
    
    # 分析平滑效果
    print("\n📈 平滑效果分析:")
    baseline_volatility = results["无平滑"]["volatility"]
    
    best_smooth = None
    best_improvement = 0
    
    for name, data in results.items():
        if name != "无平滑":
            improvement = (baseline_volatility - data["volatility"]) / baseline_volatility * 100
            print(f"  • {name}: 改进 {improvement:+.2f}%")
            
            if improvement > best_improvement:
                best_improvement = improvement
                best_smooth = name
    
    print(f"\n🏆 最佳平滑配置: {best_smooth} (改进 {best_improvement:.2f}%)")
    
    # 验证平滑确实生效
    smooth_working = best_improvement > 5  # 至少5%改进
    print(f"\n✅ HULL平滑化实现状态: {'生效' if smooth_working else '未生效'}")
    
    return smooth_working

def test_stc_plotting_compatibility():
    """重点测试STC附图指标绘制兼容性"""
    print("\n🎯 重点测试STC附图指标绘制兼容性")
    print("=" * 70)
    
    strategy = OptionStrategy2()
    
    # 生成振荡测试数据
    test_prices = []
    for i in range(150):
        base = 100
        trend = np.sin(i * 0.03) * 8
        cycle = np.sin(i * 0.15) * 3
        noise = np.random.normal(0, 0.2)
        price = base + trend + cycle + noise
        test_prices.append(price)
    
    strategy.price_history = test_prices
    strategy.current_params = strategy.param_sets["A"]
    
    # 模拟Tick数据
    class MockTick:
        def __init__(self, price):
            self.last_price = price
    
    strategy.tick = MockTick(test_prices[-1])
    
    # 计算指标
    strategy.calc_indicator()
    
    # 获取副图指标数据
    sub_data = strategy.sub_indicator_data
    
    print("📊 STC副图指标数据结构:")
    for key, value in sub_data.items():
        print(f"  • {key:18}: {value:8.4f} ({type(value).__name__})")
    
    # 验证数据变化性（确保不是静态值）
    stc_values = []
    for i in range(len(test_prices) - 30, len(test_prices)):
        strategy.price_history = test_prices[:i+1]
        strategy.calc_indicator()
        stc_values.append(strategy.state_map.stc)
    
    data_changes = len(set([round(x, 1) for x in stc_values]))  # 统计不同数值个数
    valid_data_rate = len([x for x in stc_values if 0 <= x <= 100]) / len(stc_values)
    
    # 验证数据兼容性（简化版本）
    compatibility_passed = (
        0 <= sub_data["STC"] <= 100 and
        0 <= sub_data["STC_SIGNAL"] <= 100 and
        isinstance(sub_data["STC"], float) and
        isinstance(sub_data["STC_SIGNAL"], float)
    )
    
    print("✅ STC附图指标兼容性测试完成")
    print(f"   • 数据格式兼容: {'是' if compatibility_passed else '否'}")
    print(f"   • STC指标数量: {len(sub_data)}")
    print(f"   • 数据变化数: {data_changes}")
    print(f"   • 有效数据率: {valid_data_rate:.1%}")
    
    # 输出具体数据检查（简化版本）
    print("\n📋 STC数据具体检查:")
    data_items = [
        ("STC", sub_data.get("STC", 0)),
        ("STC_SIGNAL", sub_data.get("STC_SIGNAL", 0))
    ]
    
    for name, value in data_items:
        print(f"   • {name:<15}: {value:8.4f} ({type(value).__name__})")
    
    return compatibility_passed and data_changes > 0 and valid_data_rate > 0.8

def test_integration_workflow():
    """测试完整集成工作流程"""
    print("\n🎯 测试完整集成工作流程")
    print("=" * 70)
    
    strategy = OptionStrategy2()
    
    # 生成混合市场数据
    mixed_prices = []
    for i in range(200):
        if i < 100:
            # 前半段：趋势市场
            base = 100 + i * 0.15
            noise = np.random.normal(0, 0.5)
            price = base + noise
        else:
            # 后半段：震荡市场
            base = 115
            cycle = np.sin((i-100) * 0.1) * 5
            noise = np.random.normal(0, 0.8)
            price = base + cycle + noise
        mixed_prices.append(price)
    
    # 模拟实时数据处理
    class MockKLine:
        def __init__(self, close):
            self.close = close
    
    class MockTick:
        def __init__(self, price):
            self.last_price = price
    
    print("📊 模拟实时数据处理...")
    
    hull_values = {"fast": [], "slow": [], "signal": []}
    stc_values = []
    
    # 逐步处理数据
    for i in range(60, len(mixed_prices), 5):  # 每5个数据点测试一次
        strategy.price_history = mixed_prices[:i+1]
        strategy.current_params = strategy.param_sets["A"]
        strategy.tick = MockTick(mixed_prices[i])
        
        # 计算趋势和指标
        kline = MockKLine(mixed_prices[i])
        strategy.calc_trend(kline)
        strategy.calc_indicator()
        
        # 收集指标数据
        hull_values["fast"].append(strategy.state_map.hull_fast)
        hull_values["slow"].append(strategy.state_map.hull_slow)
        hull_values["signal"].append(strategy.state_map.hull_signal)
        stc_values.append(strategy.state_map.stc)
    
    # 分析结果
    print(f"  • 处理数据点: {len(stc_values)}")
    print(f"  • HULL快线范围: {min(hull_values['fast']):.2f} - {max(hull_values['fast']):.2f}")
    print(f"  • HULL慢线范围: {min(hull_values['slow']):.2f} - {max(hull_values['slow']):.2f}")
    print(f"  • STC范围: {min(stc_values):.2f} - {max(stc_values):.2f}")
    
    # 验证集成正常性
    hull_reasonable = all(
        80 <= max(hull_values[key]) <= 200 and
        80 <= min(hull_values[key]) <= 200
        for key in hull_values
    )
    
    stc_reasonable = 0 <= min(stc_values) <= 100 and 0 <= max(stc_values) <= 100
    
    print(f"\n📈 集成工作流程验证:")
    print(f"  • HULL指标合理性: {'✅ 正常' if hull_reasonable else '❌ 异常'}")
    print(f"  • STC指标合理性: {'✅ 正常' if stc_reasonable else '❌ 异常'}")
    
    integration_working = hull_reasonable and stc_reasonable
    print(f"\n✅ 集成工作流程状态: {'正常' if integration_working else '异常'}")
    
    return integration_working

if __name__ == "__main__":
    print("🔧 HULL+STC针对性修复验证")
    print("=" * 70)
    print("针对回测发现的问题进行专项测试：")
    print("1. HULL平滑化未实现")
    print("2. STC指标附图指标窗口未能成功绘制")
    print()
    
    # 执行针对性测试
    test1 = test_hull_smoothing_effectiveness()
    test2 = test_stc_plotting_compatibility()
    test3 = test_integration_workflow()
    
    # 总结修复效果
    print("\n" + "=" * 70)
    print("📋 针对性修复验证结果:")
    print(f"  • HULL平滑化实现: {'✅ 已修复' if test1 else '❌ 仍有问题'}")
    print(f"  • STC绘制兼容性: {'✅ 已修复' if test2 else '❌ 仍有问题'}")
    print(f"  • 集成工作流程: {'✅ 正常' if test3 else '❌ 异常'}")
    
    all_fixed = all([test1, test2, test3])
    
    if all_fixed:
        print("\n🎉 恭喜！针对性修复完全成功！")
        print("\n✅ 修复要点:")
        print("   • HULL指标增强平滑算法，确保平滑效果生效")
        print("   • 为不同周期创建独立指标实例，避免缓存干扰")
        print("   • 优化STC副图数据结构，提升绘制兼容性")
        print("   • 添加标准参考线和边界设置")
        print("   • 确保所有数据类型和范围正确")
        print("\n🚀 现在可以进行回测验证！")
    else:
        failed_items = []
        if not test1: failed_items.append("HULL平滑化")
        if not test2: failed_items.append("STC绘制")
        if not test3: failed_items.append("集成工作流程")
        
        print(f"\n⚠️ 以下问题仍需解决: {', '.join(failed_items)}")
        print("请根据测试结果进一步调整实现")
    
    print("=" * 70) 