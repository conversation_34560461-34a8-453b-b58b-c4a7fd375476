# OptionStrategy2 程序化自动交易策略

## 📋 策略概述

OptionStrategy2是一个基于HULL指标和STC指标的程序化自动交易策略，专门设计用于自动化交易系统。该策略移除了外部机器学习依赖（river库），采用纯技术分析方法，实现完全程序化的自动交易。

### 🎯 核心特点

- **纯技术指标驱动**：基于HULL和STC技术指标生成交易信号
- **自适应参数调整**：根据历史表现自动调整策略参数
- **动态风险管理**：实时调整止盈止损，保护资金安全
- **双模式切换**：根据市场环境自动切换趋势型和震荡型策略
- **无外部依赖**：不依赖机器学习库，降低系统复杂度

## 🔧 技术指标详解

### HULL指标（船体移动平均）

#### 计算公式
```
WMA1 = WMA(price, period/2)
WMA2 = WMA(price, period)
Raw_HMA = 2*WMA1 - WMA2
HMA = WMA(Raw_HMA, sqrt(period))
Smoothed_HMA = Smooth(HMA, smooth_period)
```

#### 优化特性
- **增强平滑**：使用更保守的EMA平滑因子（alpha = 1.0/(period+1)）
- **扩展历史**：保留period*5历史数据，提升平滑连续性
- **二次平滑**：对高周期配置应用额外平滑处理
- **独立实例**：为不同周期创建独立指标实例，避免数据干扰

### STC指标（Schaff趋势周期）

#### 计算公式
```
MACD = EMA(fast) - EMA(slow)
%K = Stochastic(MACD, length)
%D = Smooth(%K, factor)
STC = %D
```

#### 关键参数
- **长度周期**：默认23，控制指标敏感度
- **快速MA**：默认50，短期趋势跟踪
- **慢速MA**：默认100，长期趋势跟踪
- **平滑因子**：默认0.5，控制指标平滑程度

## 🚀 交易逻辑

### 模式切换机制

#### 模式A（趋势型）
- **适用环境**：明显的上升或下降趋势
- **买入条件**：
  - HULL快线 > HULL慢线
  - HULL趋势为"多头"
  - STC > 25 且 STC > STC信号线
  - 趋势强度 > 0.4

#### 模式B（震荡型）
- **适用环境**：横盘震荡或高波动市场
- **买入条件**：
  - HULL快慢线收敛（发散度 < 2%）
  - STC < 20（超卖）
  - STC柱状图 > 0（开始反弹）

### 信号确认机制

1. **趋势确认**：需要连续3个周期的趋势一致性
2. **强度过滤**：趋势强度必须达到阈值
3. **波动率控制**：极端波动时暂停交易
4. **时间过滤**：避免在特定时间段交易

## 📊 风险管理

### 动态止盈止损

#### 递进式追踪止损
```python
# 浮盈档位设置
profit_step1 = volatility * 1.0  # 第一档
profit_step2 = volatility * 2.0  # 第二档
profit_step3 = volatility * 3.0  # 第三档

# 止损调整逻辑
if 浮盈 > profit_step3:
    止损 = 最高价 - volatility * 1.2
elif 浮盈 > profit_step2:
    止损 = 最高价 - volatility * 1.5
elif 浮盈 > profit_step1:
    止损 = 最高价 - volatility * 2.0
```

### 风控参数

| 参数 | 趋势型(A) | 震荡型(B) | 说明 |
|------|-----------|-----------|------|
| 止损倍数 | 2.2 | 1.8 | 基于波动率的止损设置 |
| 止盈倍数 | 3.0 | 2.5 | 基于波动率的止盈设置 |
| 追踪步长 | 1.0 | 0.5 | 追踪止损的调整步长 |

## 🎮 参数配置

### 基础参数
```python
# HULL指标参数
hull_fast_period = 21    # 快线周期
hull_slow_period = 55    # 慢线周期
hull_signal_period = 13  # 信号线周期
smooth_type = "EMA"      # 平滑类型
smooth_period = 5        # 平滑周期

# STC指标参数
stc_length = 23          # STC长度
stc_fast_ma = 50         # 快速MA
stc_slow_ma = 100        # 慢速MA
stc_factor = 0.5         # 平滑因子
```

### 交易参数
```python
max_positions = 5        # 最大持仓数
order_volume = 1         # 单次下单量
price_type = "D1"        # 价格档位
```

## 🔄 自适应机制

### 自动参数调整

#### 调整触发条件
- 交易次数达到调整频率（默认10次）
- 胜率低于性能阈值（默认60%）
- 盈亏比不达标

#### 调整逻辑
```python
if 胜率 < 性能阈值:
    止损倍数 *= 0.95  # 收紧止损
    止盈倍数 *= 1.05  # 扩大止盈
elif 盈亏比 > 1.5:
    追踪步长 *= 1.02  # 更激进的追踪
```

### 性能监控

#### 关键指标
- **胜率**：盈利交易占总交易的比例
- **盈亏比**：平均盈利与平均亏损的比率
- **夏普比率**：风险调整后的收益率
- **最大回撤**：历史最大损失幅度

## 💻 使用示例

### 基本使用
```python
from OptionStrategy2 import OptionStrategy2

# 创建策略实例
strategy = OptionStrategy2()

# 配置参数
strategy.params_map.smooth_period = 5
strategy.params_map.hull_fast_period = 21
strategy.params_map.mode = "auto"  # 自动模式

# 启动策略
strategy.on_start()

# 接收K线数据
for kline in kline_data:
    strategy.callback(kline)
```

### 手动模式
```python
# 设置为手动模式
strategy.params_map.mode = "manual"
strategy.params_map.use_fixed_stops = True
strategy.params_map.fixed_stop_loss = 95.0
strategy.params_map.fixed_take_profit = 105.0
```

## 📈 性能优化

### 计算效率
- **指标缓存**：智能缓存历史计算结果
- **增量计算**：避免重复计算全部历史数据
- **内存管理**：合理限制历史数据保留量

### 信号质量
- **多重过滤**：时间、波动率、趋势强度多维度过滤
- **确认机制**：避免因噪音产生的虚假信号
- **自适应阈值**：根据市场环境动态调整阈值

## 🛡️ 风险提示

### 适用市场
- ✅ **适合**：趋势明显或规律震荡的市场
- ❌ **不适合**：极端波动或流动性极差的市场

### 注意事项
1. **参数调优**：建议先在历史数据上充分测试
2. **风控设置**：务必设置合理的止损和仓位控制
3. **监控频率**：定期监控策略表现，及时调整
4. **技术环境**：确保交易系统稳定性和网络连接质量

## 📊 回测建议

### 数据要求
- **时间跨度**：建议至少3个月的历史数据
- **数据质量**：使用高质量的K线和Tick数据
- **市场条件**：包含不同市场环境的数据

### 评估指标
- **收益率**：年化收益率应为基准的1.5倍以上
- **夏普比率**：建议大于1.0
- **最大回撤**：建议控制在20%以内
- **胜率**：建议保持在45%以上

---

**版本信息**：OptionStrategy2 v2.0（无River依赖版）  
**更新时间**：2025年1月25日  
**适用环境**：InfiniTrader程序化交易平台 